<?php
/**
 * Plugin Name: Gravity Forms User Entries Block
 * Plugin URI: https://example.com
 * Description: A custom block that displays all Gravity Forms entries for the current user.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: gf-user-entries
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GF_USER_ENTRIES_VERSION', '1.0.0');
define('GF_USER_ENTRIES_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GF_USER_ENTRIES_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main plugin class
 */
class GF_User_Entries_Block {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_editor_assets'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Gravity Forms is active
        if (!class_exists('GFAPI')) {
            add_action('admin_notices', array($this, 'gravity_forms_missing_notice'));
            return;
        }
        
        // Register the block
        register_block_type('gf-user-entries/user-entries-list', array(
            'render_callback' => array($this, 'render_block'),
            'attributes' => array(
                'showFormTitle' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showDate' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showStatus' => array(
                    'type' => 'boolean',
                    'default' => false,
                ),
                'entriesPerPage' => array(
                    'type' => 'number',
                    'default' => 10,
                ),
                'formIds' => array(
                    'type' => 'array',
                    'default' => array(),
                ),
            ),
        ));
    }
    
    /**
     * Render the block on the frontend
     */
    public function render_block($attributes) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="gf-user-entries-notice">' . __('You must be logged in to view your form entries.', 'gf-user-entries') . '</div>';
        }
        
        $current_user_id = get_current_user_id();
        
        // Set default attributes
        $attributes = wp_parse_args($attributes, array(
            'showFormTitle' => true,
            'showDate' => true,
            'showStatus' => false,
            'entriesPerPage' => 10,
            'formIds' => array(),
        ));
        
        // Get entries for current user
        $search_criteria = array(
            'status' => 'active',
            'field_filters' => array(
                array(
                    'key' => 'created_by',
                    'value' => $current_user_id,
                )
            )
        );
        
        // If specific form IDs are selected, filter by them
        $form_ids = !empty($attributes['formIds']) ? $attributes['formIds'] : 0;
        
        $sorting = array(
            'key' => 'date_created',
            'direction' => 'DESC',
        );
        
        $paging = array(
            'offset' => 0,
            'page_size' => $attributes['entriesPerPage'],
        );
        
        $entries = GFAPI::get_entries($form_ids, $search_criteria, $sorting, $paging);
        
        if (empty($entries) || is_wp_error($entries)) {
            return '<div class="gf-user-entries-notice">' . __('No form entries found.', 'gf-user-entries') . '</div>';
        }
        
        // Start building the output
        $output = '<div class="gf-user-entries-block">';
        $output .= '<h3 class="gf-user-entries-title">' . __('Your Form Submissions', 'gf-user-entries') . '</h3>';
        $output .= '<div class="gf-user-entries-list">';
        
        foreach ($entries as $entry) {
            $form = GFAPI::get_form($entry['form_id']);
            $output .= $this->render_entry($entry, $form, $attributes);
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render a single entry
     */
    private function render_entry($entry, $form, $attributes) {
        $output = '<div class="gf-user-entry-item">';
        
        // Form title
        if ($attributes['showFormTitle'] && !empty($form['title'])) {
            $output .= '<h4 class="gf-entry-form-title">' . esc_html($form['title']) . '</h4>';
        }
        
        // Entry date
        if ($attributes['showDate']) {
            $date = date_i18n(get_option('date_format'), strtotime($entry['date_created']));
            $output .= '<div class="gf-entry-date">' . sprintf(__('Submitted: %s', 'gf-user-entries'), $date) . '</div>';
        }
        
        // Entry status
        if ($attributes['showStatus']) {
            $status = ucfirst($entry['status']);
            $output .= '<div class="gf-entry-status">' . sprintf(__('Status: %s', 'gf-user-entries'), $status) . '</div>';
        }
        
        // Entry fields
        $output .= '<div class="gf-entry-fields">';
        
        foreach ($form['fields'] as $field) {
            $field_value = rgar($entry, $field->id);
            
            // Skip empty fields and certain field types
            if (empty($field_value) || in_array($field->type, array('html', 'section', 'page', 'captcha'))) {
                continue;
            }
            
            $field_label = $field->label;
            $display_value = $this->get_display_value($field, $field_value, $entry);
            
            if (!empty($display_value)) {
                $output .= '<div class="gf-entry-field">';
                $output .= '<span class="gf-field-label">' . esc_html($field_label) . ':</span> ';
                $output .= '<span class="gf-field-value">' . $display_value . '</span>';
                $output .= '</div>';
            }
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Get display value for a field
     */
    private function get_display_value($field, $value, $entry) {
        switch ($field->type) {
            case 'fileupload':
                if (!empty($value)) {
                    $files = json_decode($value, true);
                    if (is_array($files)) {
                        $file_links = array();
                        foreach ($files as $file) {
                            $file_links[] = '<a href="' . esc_url($file) . '" target="_blank">' . basename($file) . '</a>';
                        }
                        return implode(', ', $file_links);
                    } else {
                        return '<a href="' . esc_url($value) . '" target="_blank">' . basename($value) . '</a>';
                    }
                }
                break;
                
            case 'checkbox':
                if (!empty($value)) {
                    $choices = array();
                    foreach ($field->choices as $choice) {
                        $input_id = $field->id . '.' . $choice['value'];
                        if (!empty($entry[$input_id])) {
                            $choices[] = $choice['text'];
                        }
                    }
                    return implode(', ', $choices);
                }
                break;
                
            case 'multiselect':
                if (!empty($value)) {
                    return str_replace(',', ', ', $value);
                }
                break;
                
            default:
                return esc_html($value);
        }
        
        return '';
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_style(
            'gf-user-entries-frontend',
            GF_USER_ENTRIES_PLUGIN_URL . 'assets/frontend.css',
            array(),
            GF_USER_ENTRIES_VERSION
        );
    }
    
    /**
     * Enqueue editor assets
     */
    public function enqueue_editor_assets() {
        wp_enqueue_script(
            'gf-user-entries-editor',
            GF_USER_ENTRIES_PLUGIN_URL . 'assets/editor.js',
            array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
            GF_USER_ENTRIES_VERSION,
            true
        );
        
        wp_enqueue_style(
            'gf-user-entries-editor',
            GF_USER_ENTRIES_PLUGIN_URL . 'assets/editor.css',
            array('wp-edit-blocks'),
            GF_USER_ENTRIES_VERSION
        );
        
        // Localize script with form data
        $forms = GFAPI::get_forms();
        $form_options = array();
        foreach ($forms as $form) {
            $form_options[] = array(
                'value' => $form['id'],
                'label' => $form['title'],
            );
        }
        
        wp_localize_script('gf-user-entries-editor', 'gfUserEntriesData', array(
            'forms' => $form_options,
        ));
    }
    
    /**
     * Show admin notice if Gravity Forms is not active
     */
    public function gravity_forms_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('Gravity Forms User Entries Block requires Gravity Forms to be installed and activated.', 'gf-user-entries');
        echo '</p></div>';
    }
}

// Initialize the plugin
new GF_User_Entries_Block();

(function() {
    const { registerBlockType } = wp.blocks;
    const { InspectorControls } = wp.blockEditor;
    const { PanelBody, ToggleControl, RangeControl, CheckboxControl } = wp.components;
    const { __ } = wp.i18n;
    const { createElement: el } = wp.element;

    registerBlockType('gf-user-entries/user-entries-list', {
        title: __('User Form Entries', 'gf-user-entries'),
        description: __('Display all Gravity Forms entries for the current user.', 'gf-user-entries'),
        icon: 'list-view',
        category: 'widgets',
        keywords: [
            __('gravity forms', 'gf-user-entries'),
            __('entries', 'gf-user-entries'),
            __('user', 'gf-user-entries'),
        ],
        attributes: {
            showFormTitle: {
                type: 'boolean',
                default: true,
            },
            showDate: {
                type: 'boolean',
                default: true,
            },
            showStatus: {
                type: 'boolean',
                default: false,
            },
            entriesPerPage: {
                type: 'number',
                default: 10,
            },
            formIds: {
                type: 'array',
                default: [],
            },
        },
        edit: function(props) {
            const { attributes, setAttributes } = props;
            const { showFormTitle, showDate, showStatus, entriesPerPage, formIds } = attributes;

            // Get available forms from localized data
            const availableForms = window.gfUserEntriesData ? window.gfUserEntriesData.forms : [];

            return el('div', { className: 'gf-user-entries-editor' }, [
                el(InspectorControls, { key: 'inspector' }, [
                    el(PanelBody, {
                        key: 'display-settings',
                        title: __('Display Settings', 'gf-user-entries'),
                        initialOpen: true,
                    }, [
                        el(ToggleControl, {
                            key: 'show-form-title',
                            label: __('Show Form Title', 'gf-user-entries'),
                            checked: showFormTitle,
                            onChange: function(value) {
                                setAttributes({ showFormTitle: value });
                            },
                        }),
                        el(ToggleControl, {
                            key: 'show-date',
                            label: __('Show Submission Date', 'gf-user-entries'),
                            checked: showDate,
                            onChange: function(value) {
                                setAttributes({ showDate: value });
                            },
                        }),
                        el(ToggleControl, {
                            key: 'show-status',
                            label: __('Show Entry Status', 'gf-user-entries'),
                            checked: showStatus,
                            onChange: function(value) {
                                setAttributes({ showStatus: value });
                            },
                        }),
                        el(RangeControl, {
                            key: 'entries-per-page',
                            label: __('Entries Per Page', 'gf-user-entries'),
                            value: entriesPerPage,
                            onChange: function(value) {
                                setAttributes({ entriesPerPage: value });
                            },
                            min: 1,
                            max: 50,
                        }),
                    ]),
                    el(PanelBody, {
                        key: 'form-selection',
                        title: __('Form Selection', 'gf-user-entries'),
                        initialOpen: false,
                    }, [
                        el('p', { key: 'form-help' }, __('Leave all unchecked to show entries from all forms.', 'gf-user-entries')),
                        availableForms.map(function(form, index) {
                            return el(CheckboxControl, {
                                key: 'form-' + form.value,
                                label: form.label,
                                checked: formIds.includes(form.value),
                                onChange: function(checked) {
                                    let newFormIds = [...formIds];
                                    if (checked) {
                                        if (!newFormIds.includes(form.value)) {
                                            newFormIds.push(form.value);
                                        }
                                    } else {
                                        newFormIds = newFormIds.filter(id => id !== form.value);
                                    }
                                    setAttributes({ formIds: newFormIds });
                                },
                            });
                        }),
                    ]),
                ]),
                el('div', {
                    key: 'preview',
                    className: 'gf-user-entries-preview',
                }, [
                    el('div', {
                        key: 'preview-header',
                        className: 'gf-user-entries-preview-header',
                    }, [
                        el('h3', { key: 'title' }, __('User Form Entries', 'gf-user-entries')),
                        el('p', { key: 'description' }, __('This block will display form entries for the currently logged-in user.', 'gf-user-entries')),
                    ]),
                    el('div', {
                        key: 'preview-settings',
                        className: 'gf-user-entries-preview-settings',
                    }, [
                        el('h4', { key: 'settings-title' }, __('Current Settings:', 'gf-user-entries')),
                        el('ul', { key: 'settings-list' }, [
                            el('li', { key: 'setting-title' }, 
                                __('Show Form Title: ', 'gf-user-entries') + (showFormTitle ? __('Yes', 'gf-user-entries') : __('No', 'gf-user-entries'))
                            ),
                            el('li', { key: 'setting-date' }, 
                                __('Show Date: ', 'gf-user-entries') + (showDate ? __('Yes', 'gf-user-entries') : __('No', 'gf-user-entries'))
                            ),
                            el('li', { key: 'setting-status' }, 
                                __('Show Status: ', 'gf-user-entries') + (showStatus ? __('Yes', 'gf-user-entries') : __('No', 'gf-user-entries'))
                            ),
                            el('li', { key: 'setting-per-page' }, 
                                __('Entries Per Page: ', 'gf-user-entries') + entriesPerPage
                            ),
                            el('li', { key: 'setting-forms' }, 
                                __('Selected Forms: ', 'gf-user-entries') + 
                                (formIds.length === 0 ? __('All Forms', 'gf-user-entries') : 
                                    availableForms
                                        .filter(form => formIds.includes(form.value))
                                        .map(form => form.label)
                                        .join(', ')
                                )
                            ),
                        ]),
                    ]),
                    el('div', {
                        key: 'preview-note',
                        className: 'gf-user-entries-preview-note',
                    }, [
                        el('p', { key: 'note' }, __('Note: The actual entries will be displayed on the frontend based on the logged-in user.', 'gf-user-entries')),
                    ]),
                ]),
            ]);
        },
        save: function() {
            // Return null since this is a dynamic block
            return null;
        },
    });
})();

(function ($) {
  "use strict";

  $(document).ready(function () {
    // Handle fill form button clicks
    $(".gf-fill-form-button").on("click", function (e) {
      e.preventDefault();

      var $button = $(this);
      var formId = $button.data("form-id");
      var formUrl = $button.data("form-url");
      var entryData = $button.data("entry-data");
      var fieldMapping = $button.data("field-mapping");

      if (!formId || !formUrl || !entryData) {
        console.error("Missing required data for form pre-population");
        return;
      }

      // Store the entry data and field mapping in localStorage
      var storageKey = "gf_prepopulate_" + formId;
      var storageData = {
        entryData: entryData,
        fieldMapping: fieldMapping || {},
      };
      try {
        localStorage.setItem(storageKey, JSON.stringify(storageData));

        // Provide user feedback
        $button.text("Redirecting...");
        $button.prop("disabled", true);

        // Redirect to the form page
        window.location.href = formUrl;
      } catch (error) {
        console.error("Failed to store form data:", error);
        alert("Failed to prepare form data. Please try again.");
      }
    });

    // Auto-populate forms when page loads
    populateFormsFromStorage();
  });

  /**
   * Populate forms from localStorage data
   */
  function populateFormsFromStorage() {
    // Find all Gravity Forms on the page
    $(".gform_wrapper").each(function () {
      var $form = $(this);
      var formId = extractFormId($form);

      if (!formId) {
        return;
      }

      var storageKey = "gf_prepopulate_" + formId;
      var storedData = localStorage.getItem(storageKey);

      if (!storedData) {
        return;
      }

      try {
        var storageData = JSON.parse(storedData);
        var entryData = storageData.entryData || storageData; // Backward compatibility
        var fieldMapping = storageData.fieldMapping || {};

        populateForm($form, entryData, fieldMapping);

        // Clear the stored data after use
        // localStorage.removeItem(storageKey);

        // Show success message
        showPopulationMessage($form);
      } catch (error) {
        console.error("Failed to parse stored form data:", error);
        localStorage.removeItem(storageKey);
      }
    });
  }

  /**
   * Extract form ID from form wrapper
   */
  function extractFormId($form) {
    // Try to get form ID from various possible sources
    var formId = null;

    // Method 1: From form element ID
    var $formElement = $form.find("form");
    if ($formElement.length) {
      var formElementId = $formElement.attr("id");
      if (formElementId) {
        var matches = formElementId.match(/gform_(\d+)/);
        if (matches) {
          formId = matches[1];
        }
      }
    }

    // Method 2: From wrapper class
    if (!formId) {
      var classes = $form.attr("class").split(/\s+/);
      for (var i = 0; i < classes.length; i++) {
        var matches = classes[i].match(/gform_wrapper_(\d+)/);
        if (matches) {
          formId = matches[1];
          break;
        }
      }
    }

    return formId;
  }

  /**
   * Populate form fields with entry data
   */
  function populateForm($form, entryData, fieldMapping) {
    fieldMapping = fieldMapping || {};

    for (var paramName in entryData) {
      if (!entryData.hasOwnProperty(paramName)) {
        continue;
      }

      var value = entryData[paramName];

      // Handle different field types
      populateFieldByParameter(
        $form,
        paramName,
        value,
        fieldMapping[paramName]
      );
    }
  }

  /**
   * Populate a specific field by parameter name
   */
  function populateFieldByParameter($form, paramName, value, fieldInfo) {
    var $fields = $();

    // First try to find fields using the field mapping information
    if (fieldInfo && fieldInfo.field_id) {
      if (fieldInfo.input_id) {
        // Multi-input field (like name, address)
        // Note: Gravity Forms uses dots in input names, not underscores
        var inputName = "input_" + fieldInfo.input_id.toString();
        $fields = $form.find('[name="' + inputName + '"]');
      } else {
        // Single input field
        var fieldName = "input_" + fieldInfo.field_id;
        $fields = $form.find('[name="' + fieldName + '"]');

        // For checkboxes, we need to find all related inputs
        if (fieldInfo.field_type === "checkbox") {
          $fields = $form.find('[name^="input_' + fieldInfo.field_id + '."]');
        }
      }
    }

    // Fallback: Find fields that match this parameter name using data-parameter attribute
    if ($fields.length === 0) {
      $fields = $form.find('[data-parameter="' + paramName + '"]');
    }

    // Last resort: Try to find by name attribute patterns
    if ($fields.length === 0) {
      $fields = $form.find(
        'input[name*="' +
          paramName +
          '"], select[name*="' +
          paramName +
          '"], textarea[name*="' +
          paramName +
          '"]'
      );
    }

    $fields.each(function () {
      var $field = $(this);
      var fieldType =
        $field.attr("type") || $field.prop("tagName").toLowerCase();

      switch (fieldType.toLowerCase()) {
        case "text":
        case "email":
        case "tel":
        case "url":
        case "number":
        case "textarea":
          $field.val(value);
          break;

        case "checkbox":
          // Handle comma-separated checkbox values
          if (typeof value === "string" && value.indexOf(",") !== -1) {
            var checkboxValues = value.split(",");

            for (var i = 0; i < checkboxValues.length; i++) {
              var checkboxValue = checkboxValues[i].trim();
              if ($field.val() === checkboxValue) {
                $field.prop("checked", true);
              }
            }
          } else {
            // Single checkbox value
            if ($field.val() === value) {
              $field.prop("checked", true);
            }
          }
          break;

        case "radio":
          if ($field.val() === value) {
            $field.prop("checked", true);
          }
          break;

        case "select":
          $field.val(value);
          break;
      }

      // Trigger change event to ensure any conditional logic updates
      $field.trigger("change");
    });

    // Handle repeater fields separately
    if (
      fieldInfo &&
      fieldInfo.field_type === "repeater" &&
      Array.isArray(value)
    ) {
      populateRepeaterField($form, fieldInfo.field_id, value);
    }
  }

  /**
   * Populate repeater field with data
   */
  function populateRepeaterField($form, fieldId, repeaterData) {
    // Find the repeater field container
    var $repeaterContainer = $form.find(
      "#field_" + $form.attr("id").replace("gform_", "") + "_" + fieldId
    );

    if ($repeaterContainer.length === 0) {
      // Try alternative selector
      $repeaterContainer = $form.find('[data-field-id="' + fieldId + '"]');
    }

    if ($repeaterContainer.length === 0) {
      console.log("Repeater container not found for field", fieldId);
      return;
    }

    // Clear existing rows (except the first template row)
    $repeaterContainer.find(".gfield_repeater_item:not(:first)").remove();

    // Populate each row
    for (var i = 0; i < repeaterData.length; i++) {
      var rowData = repeaterData[i];

      if (i > 0) {
        // Add new row (click the add button)
        var $addButton = $repeaterContainer.find(".add_list_item");
        if ($addButton.length) {
          $addButton.trigger("click");
        }
      }

      // Get the current row
      var $currentRow = $repeaterContainer.find(".gfield_repeater_item").eq(i);

      if ($currentRow.length) {
        // Populate fields in this row
        for (var subFieldId in rowData) {
          if (rowData.hasOwnProperty(subFieldId)) {
            var $subField = $currentRow.find('[name*="' + subFieldId + '"]');
            if ($subField.length) {
              $subField.val(rowData[subFieldId]).trigger("change");
            }
          }
        }
      }
    }
  }

  /**
   * Show a message indicating the form was populated
   */
  function showPopulationMessage($form) {
    var $message = $('<div class="gf-population-message">')
      .text("Form has been pre-filled with your previous submission data.")
      .css({
        "background-color": "#d4edda",
        border: "1px solid #c3e6cb",
        color: "#155724",
        padding: "10px 15px",
        "margin-bottom": "15px",
        "border-radius": "4px",
        "font-size": "14px",
      });

    $form.prepend($message);

    // Auto-hide the message after 5 seconds
    setTimeout(function () {
      $message.fadeOut(500, function () {
        $message.remove();
      });
    }, 5000);
  }
})(jQuery);

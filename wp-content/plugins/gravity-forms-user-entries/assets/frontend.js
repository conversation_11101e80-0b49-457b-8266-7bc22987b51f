(function ($) {
  "use strict";

  $(document).ready(function () {
    // Handle fill form button clicks
    $(".gf-fill-form-button").on("click", function (e) {
      e.preventDefault();

      var $button = $(this);
      var formId = $button.data("form-id");
      var formUrl = $button.data("form-url");
      var entryData = $button.data("entry-data");

      if (!formId || !formUrl || !entryData) {
        console.error("Missing required data for form pre-population");
        return;
      }

      // Store the entry data in localStorage
      var storageKey = "gf_prepopulate_" + formId;
      try {
        localStorage.setItem(storageKey, JSON.stringify(entryData));

        // Provide user feedback
        $button.text("Redirecting...");
        $button.prop("disabled", true);

        // Redirect to the form page
        window.location.href = formUrl;
      } catch (error) {
        console.error("Failed to store form data:", error);
        alert("Failed to prepare form data. Please try again.");
      }
    });

    // Auto-populate forms when page loads
    populateFormsFromStorage();
  });

  /**
   * Populate forms from localStorage data
   */
  function populateFormsFromStorage() {
    // Find all Gravity Forms on the page
    $(".gform_wrapper").each(function () {
      var $form = $(this);
      var formId = extractFormId($form);

      if (!formId) {
        return;
      }

      var storageKey = "gf_prepopulate_" + formId;
      var storedData = localStorage.getItem(storageKey);

      if (!storedData) {
        return;
      }

      try {
        var entryData = JSON.parse(storedData);

        console.log("insert", $form, entryData);

        populateForm($form, entryData);

        // Clear the stored data after use
        // localStorage.removeItem(storageKey);

        // Show success message
        showPopulationMessage($form);
      } catch (error) {
        console.error("Failed to parse stored form data:", error);
        localStorage.removeItem(storageKey);
      }
    });
  }

  /**
   * Extract form ID from form wrapper
   */
  function extractFormId($form) {
    // Try to get form ID from various possible sources
    var formId = null;

    // Method 1: From form element ID
    var $formElement = $form.find("form");
    if ($formElement.length) {
      var formElementId = $formElement.attr("id");
      if (formElementId) {
        var matches = formElementId.match(/gform_(\d+)/);
        if (matches) {
          formId = matches[1];
        }
      }
    }

    // Method 2: From wrapper class
    if (!formId) {
      var classes = $form.attr("class").split(/\s+/);
      for (var i = 0; i < classes.length; i++) {
        var matches = classes[i].match(/gform_wrapper_(\d+)/);
        if (matches) {
          formId = matches[1];
          break;
        }
      }
    }

    return formId;
  }

  /**
   * Populate form fields with entry data
   */
  function populateForm($form, entryData) {
    for (var paramName in entryData) {
      if (!entryData.hasOwnProperty(paramName)) {
        continue;
      }

      var value = entryData[paramName];

      // Handle different field types
      populateFieldByParameter($form, paramName, value);
    }
  }

  /**
   * Populate a specific field by parameter name
   */
  function populateFieldByParameter($form, paramName, value) {
    // Find fields that match this parameter name
    var $fields = $form.find('[data-parameter="' + paramName + '"]');

    // If no fields found with data-parameter, try other methods
    if ($fields.length === 0) {
      // Try to find by name attribute patterns
      $fields = $form.find(
        'input[name*="' +
          paramName +
          '"], select[name*="' +
          paramName +
          '"], textarea[name*="' +
          paramName +
          '"]'
      );
    }
      
    cons

    $fields.each(function () {
      var $field = $(this);
      var fieldType =
        $field.attr("type") || $field.prop("tagName").toLowerCase();

      switch (fieldType.toLowerCase()) {
        case "text":
        case "email":
        case "tel":
        case "url":
        case "number":
        case "textarea":
          $field.val(value);
          break;

        case "checkbox":
          // Handle comma-separated checkbox values
          var checkboxValues = value.split(",");
          for (var i = 0; i < checkboxValues.length; i++) {
            var checkboxValue = checkboxValues[i].trim();
            if ($field.val() === checkboxValue) {
              $field.prop("checked", true);
            }
          }
          break;

        case "radio":
          if ($field.val() === value) {
            $field.prop("checked", true);
          }
          break;

        case "select":
          $field.val(value);
          break;
      }

      // Trigger change event to ensure any conditional logic updates
      $field.trigger("change");
    });
  }

  /**
   * Show a message indicating the form was populated
   */
  function showPopulationMessage($form) {
    var $message = $('<div class="gf-population-message">')
      .text("Form has been pre-filled with your previous submission data.")
      .css({
        "background-color": "#d4edda",
        border: "1px solid #c3e6cb",
        color: "#155724",
        padding: "10px 15px",
        "margin-bottom": "15px",
        "border-radius": "4px",
        "font-size": "14px",
      });

    $form.prepend($message);

    // Auto-hide the message after 5 seconds
    setTimeout(function () {
      $message.fadeOut(500, function () {
        $message.remove();
      });
    }, 5000);
  }
})(jQuery);

/* Frontend Styles for Gravity Forms User Entries Block */

.gf-user-entries-block {
  margin: 20px 0;
  padding: 0;
}

.gf-user-entries-title {
  margin: 0 0 20px 0;
  padding: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #0073aa;
  padding-bottom: 10px;
}

.gf-user-entries-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.gf-user-entry-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.gf-user-entry-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.gf-entry-form-title {
  margin: 0 0 15px 0;
  padding: 0;
  font-size: 18px;
  font-weight: 600;
  color: #0073aa;
}

.gf-entry-date,
.gf-entry-status {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.gf-entry-status {
  font-weight: 500;
}

.gf-entry-actions {
  margin: 15px 0;
  padding: 10px 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.gf-fill-form-button {
  display: inline-block;
  background-color: #0073aa;
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.gf-fill-form-button:hover {
  background-color: #005a87;
  color: #fff;
  text-decoration: none;
}

.gf-fill-form-button:focus {
  outline: 2px solid #005a87;
  outline-offset: 2px;
}

.gf-fill-form-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.gf-population-message {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  font-size: 14px;
}

.gf-entry-fields {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.gf-entry-field {
  margin-bottom: 12px;
  padding: 8px 0;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
}

.gf-entry-field:last-child {
  margin-bottom: 0;
}

.gf-field-label {
  font-weight: 600;
  color: #333;
  min-width: 120px;
  flex-shrink: 0;
}

.gf-field-value {
  color: #555;
  flex: 1;
  word-break: break-word;
}

.gf-field-value a {
  color: #0073aa;
  text-decoration: none;
}

.gf-field-value a:hover {
  text-decoration: underline;
}

.gf-user-entries-notice {
  padding: 15px 20px;
  background-color: #f0f6fc;
  border: 1px solid #c3d9ff;
  border-radius: 4px;
  color: #0c5460;
  font-weight: 500;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gf-user-entries-block {
    margin: 15px 0;
  }

  .gf-user-entry-item {
    padding: 15px;
  }

  .gf-entry-form-title {
    font-size: 16px;
  }

  .gf-entry-field {
    flex-direction: column;
    gap: 4px;
  }

  .gf-field-label {
    min-width: auto;
    margin-bottom: 4px;
  }
}

@media (max-width: 480px) {
  .gf-user-entries-title {
    font-size: 20px;
  }

  .gf-user-entry-item {
    padding: 12px;
  }

  .gf-entry-form-title {
    font-size: 15px;
  }

  .gf-entry-date,
  .gf-entry-status {
    font-size: 13px;
  }

  .gf-field-label,
  .gf-field-value {
    font-size: 14px;
  }
}

/* Editor Styles for Gravity Forms User Entries Block */

.gf-user-entries-editor {
    padding: 20px;
    border: 2px dashed #ddd;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.gf-user-entries-preview {
    max-width: 100%;
}

.gf-user-entries-preview-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.gf-user-entries-preview-header h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.gf-user-entries-preview-header p {
    margin: 0;
    color: #666;
    font-style: italic;
}

.gf-user-entries-preview-settings {
    margin-bottom: 20px;
}

.gf-user-entries-preview-settings h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.gf-user-entries-preview-settings ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
}

.gf-user-entries-preview-settings li {
    margin-bottom: 5px;
    color: #555;
    font-size: 13px;
}

.gf-user-entries-preview-note {
    padding: 15px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
}

.gf-user-entries-preview-note p {
    margin: 0;
    color: #0073aa;
    font-size: 13px;
    font-weight: 500;
}

/* Inspector Controls Styling */
.components-panel__body .components-base-control {
    margin-bottom: 16px;
}

.components-panel__body .components-base-control:last-child {
    margin-bottom: 0;
}

/* Form selection checkboxes */
.components-panel__body .components-checkbox-control {
    margin-bottom: 8px;
}

.components-panel__body .components-checkbox-control:last-child {
    margin-bottom: 0;
}

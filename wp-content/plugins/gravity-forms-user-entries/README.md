# Gravity Forms User Entries Block

A WordPress plugin that adds a custom Gutenberg block to display all Gravity Forms entries submitted by the current logged-in user.

## Features

- **Custom Gutenberg Block**: Easy to add to any page or post using the block editor
- **User-Specific Entries**: Only shows entries submitted by the currently logged-in user
- **Customizable Display**: Control what information is shown for each entry
- **Form Filtering**: Option to show entries from specific forms only
- **Responsive Design**: Mobile-friendly styling
- **Security**: Proper user authentication and data sanitization

## Requirements

- WordPress 5.0 or higher
- Gravity Forms plugin (active)
- PHP 7.4 or higher

## Installation

1. Upload the `gravity-forms-user-entries` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The "User Form Entries" block will be available in the block editor under the "Widgets" category

## Usage

### Adding the Block

1. Edit any page or post in the block editor
2. Click the "+" button to add a new block
3. Search for "User Form Entries" or find it in the "Widgets" category
4. Add the block to your content

### Block Settings

The block includes several customization options available in the block inspector (sidebar):

#### Display Settings
- **Show Form Title**: Display the title of the form for each entry
- **Show Submission Date**: Display when the entry was submitted
- **Show Entry Status**: Display the status of the entry (active, spam, trash)
- **Entries Per Page**: Control how many entries to display (1-50)

#### Form Selection
- **Form Filtering**: Choose specific forms to display entries from
- Leave all unchecked to show entries from all forms

### Frontend Display

When a logged-in user views a page with this block, they will see:

- A list of their form submissions
- Form titles (if enabled)
- Submission dates (if enabled)
- Entry status (if enabled)
- All form field values in a clean, organized layout

If the user is not logged in, they will see a message prompting them to log in.

## Styling

The plugin includes responsive CSS that works well with most themes. You can customize the appearance by adding CSS to your theme:

### CSS Classes Available

- `.gf-user-entries-block` - Main container
- `.gf-user-entries-title` - Block title
- `.gf-user-entries-list` - Entries container
- `.gf-user-entry-item` - Individual entry container
- `.gf-entry-form-title` - Form title
- `.gf-entry-date` - Submission date
- `.gf-entry-status` - Entry status
- `.gf-entry-fields` - Fields container
- `.gf-entry-field` - Individual field
- `.gf-field-label` - Field label
- `.gf-field-value` - Field value

## Field Types Supported

The plugin handles various Gravity Forms field types:

- **Text fields**: Name, email, phone, etc.
- **Choice fields**: Radio buttons, dropdowns, checkboxes
- **File uploads**: Shows downloadable links
- **Multi-select**: Displays comma-separated values
- **And more**: Most standard Gravity Forms field types

## Security Features

- User authentication required
- Proper data sanitization and escaping
- Only shows entries created by the current user
- No direct database queries exposed

## Troubleshooting

### Block Not Appearing
- Ensure Gravity Forms is installed and activated
- Check that you're using WordPress 5.0+ with Gutenberg enabled

### No Entries Showing
- Verify the user is logged in
- Check that the user has actually submitted forms
- Ensure the selected forms (if any) contain user entries

### Styling Issues
- Check for theme conflicts
- Verify CSS files are loading properly
- Use browser developer tools to inspect styling

## Changelog

### Version 1.0.0
- Initial release
- Basic block functionality
- User entry display
- Customizable settings
- Responsive design

## Support

For support and feature requests, please contact the plugin developer.

## License

This plugin is licensed under the GPL v2 or later.

"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[647],{5584:function(e,t,r){r.r(t);var a,o,i=r(7616),n=r(1533),l=r.n(n),d=r(5798),m=r(7174),u=i.ReactDOM.createRoot,c=(null===l()||void 0===l()||null===(a=l().components)||void 0===a?void 0:a.template_library)||{},g=(null===l()||void 0===l()||null===(o=l().apps)||void 0===o?void 0:o.template_library)||{},p={templateLibraryTrigger:(0,d.getNode)("gform-add-new-form"),root:(0,d.getNode)(g.root_element)},s=function(){(0,d.trigger)({event:"gform/template_library/set_open_status",el:document,data:{isOpen:!0},native:!1})};t.default=function(){u(p.root).render(i.React.createElement(m.default,c)),p.templateLibraryTrigger&&p.templateLibraryTrigger.addEventListener("click",s)}}}]);
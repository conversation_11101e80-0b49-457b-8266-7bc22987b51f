"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[91],{91:function(e,t,r){r.r(t),r.d(t,{default:function(){return x}});var o=r(7940),a=r(2492),n=r(6111),c=r(7616),i=r(783),s=r(3294),p=r(6917),l=r(9542),d=r(37),u=r(4505),y=r(9104),f=r(3878),m=r(9492),v=r(2734),h=function(e){var t=e.cx,r=e.cy,o=e.stroke;return c.React.createElement("svg",{x:t-6,y:r-6,width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},c.React.createElement("circle",{cx:"6",cy:"6",r:"5",stroke:o,strokeWidth:"2",fill:"white"}))},P={container:{backgroundColor:"#242748",color:"#ffffff",padding:"8px",borderRadius:"3px",position:"relative",textAlign:"left",fontSize:"12px"},label:{fontWeight:"600"},content:{fontWeight:"normal"}};var b=function(e){var t=e.active,r=e.payload,o=e.label;return t&&r&&r.length?c.React.createElement("div",{style:P.container},c.React.createElement("div",{style:P.label},o),r.map(function(e,t){return c.React.createElement("div",{key:t,style:P.content},c.React.createElement("span",null,(r=e.name).charAt(0).toUpperCase()+r.slice(1),": "),c.React.createElement("span",null,e.value));var r})):null};function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){(0,n.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var k=c.React.forwardRef,w=c.React.useState,E=c.React.useEffect,R=k(function(e,t){var r=e.animationDuration,P=void 0===r?1e3:r,g=e.areaProps,k=void 0===g?{}:g,R=e.cartesianGridProps,x=void 0===R?{}:R,O=e.checkboxProps,A=void 0===O?{}:O,j=e.children,C=void 0===j?null:j,K=e.cursorColor,D=void 0===K?"#9092b2":K,L=e.customAttributes,W=void 0===L?{}:L,G=e.customClasses,S=void 0===G?{}:G,_=e.customInterval,z=void 0===_?null:_,N=e.data,B=void 0===N?[]:N,M=e.gridColor,I=void 0===M?"#ecedf8":M,Q=e.height,U=void 0===Q?400:Q,q=e.legendProps,F=void 0===q?{}:q,H=e.options,J=void 0===H?[]:H,V=e.showCheckboxes,X=void 0===V||V,Y=e.showLegend,Z=void 0!==Y&&Y,$=e.tooltipProps,ee=void 0===$?{}:$,te=e.width,re=void 0===te?"100%":te,oe=e.xAxisProps,ae=void 0===oe?{}:oe,ne=e.yAxisProps,ce=void 0===ne?{}:ne,ie=T(T({className:(0,c.classnames)({"gform-chart--area":!0,"gform-chart__wrapper":!0},S)},W),{},{ref:t}),se=J.reduce(function(e,t){return e[t.dataKey]=t.defaultChecked||!0,e},{}),pe=w(se),le=(0,a.A)(pe,2),de=le[0],ue=le[1],ye=w(!0),fe=(0,a.A)(ye,2),me=fe[0],ve=fe[1],he=w(Math.floor(B.length/10)),Pe=(0,a.A)(he,2),be=Pe[0],ge=Pe[1],Te=function(){var e=window.innerWidth;ge(Math.floor(B.length/(e/180)))};E(function(){var e=setTimeout(function(){ve(!1)},P);return function(){return clearTimeout(e)}},[]),E(function(){if(!z)return Te(),window.addEventListener("resize",Te),function(){window.removeEventListener("resize",Te)};z(ge,B.length)},[B.length,z]);var ke=T({stroke:I,strokeDasharray:"0",vertical:!1},x),we=T({axisLine:{stroke:I},padding:{top:10},tickLine:{stroke:I}},ce),Ee=T({axisLine:{stroke:I},tickLine:{stroke:I},interval:be},ae),Re=T({content:c.React.createElement(b,null),cursor:{stroke:D}},ee),xe=T({},F);return c.React.createElement("div",ie,X&&c.React.createElement("div",{className:"gform-chart__checkboxes"},c.React.createElement(m.A,{display:"flex",spacing:[0,5,0,0]},J.map(function(e){var t=T({externalChecked:de[e.dataKey],externalControl:!0,labelAttributes:{label:e.label,weight:"normal"},onChange:function(){return t=e.dataKey,void ue(function(e){return T(T({},e),{},(0,n.A)({},t,!e[t]))});var t},spacing:[0,0,4,3]},A);return c.React.createElement(v.A,(0,o.A)({key:e.dataKey},t))}))),c.React.createElement(i.u,{width:re,height:U},c.React.createElement(s.Q,{data:B,margin:{top:0,right:20,left:0,bottom:0}},c.React.createElement("defs",null,J.map(function(e){return c.React.createElement("linearGradient",{key:e.dataKey,id:"color".concat(e.dataKey),x1:"0",y1:"0",x2:"0",y2:"1"},c.React.createElement("stop",{offset:"5%",stopColor:e.color,stopOpacity:.1}),c.React.createElement("stop",{offset:"95%",stopColor:e.color,stopOpacity:0}))})),c.React.createElement(p.d,ke),c.React.createElement(l.W,Ee),c.React.createElement(d.h,we),c.React.createElement(u.m,Re),Z&&c.React.createElement(y.s,xe),J.map(function(e){var t=T({type:"monotone",dataKey:e.dataKey,stroke:e.color,fillOpacity:1,fill:"url(#color".concat(e.dataKey,")"),strokeWidth:2,dot:!1,activeDot:c.React.createElement(h,{stroke:e.color}),isAnimationActive:me,animationBegin:0,animationDuration:P},k);return de[e.dataKey]?c.React.createElement(f.G,(0,o.A)({key:e.dataKey},t)):null}))),C)});R.propTypes={animationDuration:c.PropTypes.number,areaProps:c.PropTypes.object,cartesianGridProps:c.PropTypes.object,checkboxProps:c.PropTypes.object,children:c.PropTypes.oneOfType([c.PropTypes.arrayOf(c.PropTypes.node),c.PropTypes.node]),cursorColor:c.PropTypes.string,customAttributes:c.PropTypes.object,customClasses:c.PropTypes.oneOfType([c.PropTypes.string,c.PropTypes.array,c.PropTypes.object]),data:c.PropTypes.array,gridColor:c.PropTypes.string,height:c.PropTypes.oneOfType([c.PropTypes.string,c.PropTypes.number]),legendProps:c.PropTypes.object,options:c.PropTypes.array,showCheckboxes:c.PropTypes.bool,showLegend:c.PropTypes.bool,tooltipProps:c.PropTypes.object,width:c.PropTypes.oneOfType([c.PropTypes.string,c.PropTypes.number]),xAxisProps:c.PropTypes.object,yAxisProps:c.PropTypes.object},R.displayName="AreaChart";var x=R}}]);
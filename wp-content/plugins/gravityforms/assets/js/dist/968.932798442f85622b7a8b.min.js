(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[968],{7:function(t,e,n){var r=n(1960);t.exports=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=r&&void 0===e?"__lodash_hash_undefined__":e,this}},31:function(t,e,n){var r=n(8037);t.exports=function(t){return r(this,t).get(t)}},37:function(t,e,n){"use strict";n.d(e,{h:function(){return s}});var r=n(1609),o=n.n(r),i=n(7064),a=n(3499),u=n(684),c=n(9162);function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},l.apply(this,arguments)}var s=function(t){var e=t.yAxisId,n=(0,a.yi)(),r=(0,a.rY)(),s=(0,a.Nk)(e);return null==s?null:o().createElement(u.u,l({},s,{className:(0,i.A)("recharts-".concat(s.axisType," ").concat(s.axisType),s.className),viewBox:{x:0,y:0,width:n,height:r},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))};s.displayName="YAxis",s.defaultProps={allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1}},128:function(t){t.exports=function(t){return function(e,n,r){for(var o=-1,i=Object(e),a=r(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===n(i[c],c,i))break}return e}}},142:function(t){t.exports=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}},165:function(t,e,n){var r=n(3771),o=n(2499),i=n(4207),a=n(4383),u=n(6218);t.exports=function(t,e,n){var c=a(t)?r:o;return n&&u(t,e,n)&&(e=void 0),c(t,i(e,3))}},186:function(t,e,n){"use strict";n.d(e,{CG:function(){return b},Dj:function(){return x},Et:function(){return h},F4:function(){return m},NF:function(){return v},_3:function(){return p},eP:function(){return w},lX:function(){return g},sA:function(){return f},vh:function(){return d}});var r=n(7561),o=n.n(r),i=n(3859),a=n.n(i),u=n(9650),c=n.n(u),l=n(3237),s=n.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return o()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},d=function(t){return h(t)||o()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!o()(t))return r;if(p(t)){var u=t.indexOf("%");n=e*parseFloat(t.slice(0,u))/100}else n=+t;return a()(n)&&(n=r),i&&n>e&&(n=e),n},g=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},b=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,n={},r=0;r<e;r++){if(n[t[r]])return!0;n[t[r]]=!0}return!1},x=function(t,e){return h(t)&&h(e)?function(n){return t+n*(e-t)}:function(){return e}};function w(t,e,n){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===n}):null}},275:function(t){var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},412:function(t,e,n){var r=n(2110),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=r(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,n,r,o){e.push(r?o.replace(i,"$1"):n||t)}),e});t.exports=a},435:function(t,e,n){var r=n(9830);t.exports=function(t,e){for(var n=t.length;n--;)if(r(t[n][0],e))return n;return-1}},661:function(t,e,n){var r=n(142),o=n(5962),i=n(4383),a=n(8098),u=n(4683),c=n(3905),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var n=i(t),s=!n&&o(t),f=!n&&!s&&a(t),p=!n&&!s&&!f&&c(t),h=n||s||f||p,d=h?r(t.length,String):[],y=d.length;for(var v in t)!e&&!l.call(t,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y))||d.push(v);return d}},684:function(t,e,n){"use strict";n.d(e,{u:function(){return I}});var r=n(1609),o=n.n(r),i=n(4360),a=n.n(i),u=n(9650),c=n.n(u),l=n(7064),s=n(9783),f=n(6799),p=n(3704),h=n(7724),d=n(186),y=n(3038),v=n(6075),m=n(5706),g=["viewBox"],b=["viewBox"],x=["ticks"];function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},O.apply(this,arguments)}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function S(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach(function(e){T(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function A(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function E(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,C(r.key),r)}}function P(t,e,n){return e=M(e),function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,k()?Reflect.construct(e,n||[],M(t).constructor):e.apply(t,n))}function k(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(k=function(){return!!t})()}function M(t){return M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},M(t)}function _(t,e){return _=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_(t,e)}function T(t,e,n){return(e=C(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function C(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=w(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:String(e)}var I=function(t){function e(t){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=P(this,e,[t])).state={fontSize:"",letterSpacing:""},n}var n,r,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_(t,e)}(e,t),n=e,i=[{key:"renderTickItem",value:function(t,e,n){return o().isValidElement(t)?o().cloneElement(t,e):a()(t)?t(e):o().createElement(p.E,O({},e,{className:"recharts-cartesian-axis-tick-value"}),n)}}],(r=[{key:"shouldComponentUpdate",value:function(t,e){var n=t.viewBox,r=A(t,g),o=this.props,i=o.viewBox,a=A(o,b);return!(0,s.b)(n,i)||!(0,s.b)(r,a)||!(0,s.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,n,r,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,h=u.tickSize,y=u.mirror,v=u.tickMargin,m=y?-1:1,g=t.tickSize||h,b=(0,d.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=n=t.coordinate,a=(r=(o=l+ +!y*f)-m*g)-m*v,i=b;break;case"left":r=o=t.coordinate,i=(e=(n=c+ +!y*s)-m*g)-m*v,a=b;break;case"right":r=o=t.coordinate,i=(e=(n=c+ +y*s)+m*g)+m*v,a=b;break;default:e=n=t.coordinate,a=(r=(o=l+ +y*f)+m*g)+m*v,i=b}return{line:{x1:e,y1:r,x2:n,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,n=e.orientation,r=e.mirror;switch(n){case"left":t=r?"start":"end";break;case"right":t=r?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,n=t.mirror,r="end";switch(e){case"left":case"right":r="middle";break;case"top":r=n?"start":"end";break;default:r=n?"end":"start"}return r}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,n=t.y,r=t.width,i=t.height,a=t.orientation,u=t.mirror,s=t.axisLine,f=S(S(S({},(0,v.J9)(this.props,!1)),(0,v.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!u||"bottom"===a&&u);f=S(S({},f),{},{x1:e,y1:n+p*i,x2:e+r,y2:n+p*i})}else{var h=+("left"===a&&!u||"right"===a&&u);f=S(S({},f),{},{x1:e+h*r,y1:n,x2:e+h*r,y2:n+i})}return o().createElement("line",O({},f,{className:(0,l.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,n,r){var i=this,u=this.props,s=u.tickLine,p=u.stroke,h=u.tick,d=u.tickFormatter,g=u.unit,b=(0,m.f)(S(S({},this.props),{},{ticks:t}),n,r),x=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),j=(0,v.J9)(this.props,!1),A=(0,v.J9)(h,!1),E=S(S({},j),{},{fill:"none"},(0,v.J9)(s,!1)),P=b.map(function(t,n){var r=i.getTickLineCoord(t),u=r.line,v=r.tick,m=S(S(S(S({textAnchor:x,verticalAnchor:w},j),{},{stroke:"none",fill:p},A),v),{},{index:n,payload:t,visibleTicksCount:b.length,tickFormatter:d});return o().createElement(f.W,O({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,y.XC)(i.props,t,n)),s&&o().createElement("line",O({},E,u,{className:(0,l.A)("recharts-cartesian-axis-tick-line",c()(s,"className"))})),h&&e.renderTickItem(h,m,"".concat(a()(d)?d(t.value,n):t.value).concat(g||"")))});return o().createElement("g",{className:"recharts-cartesian-axis-ticks"},P)}},{key:"render",value:function(){var t=this,e=this.props,n=e.axisLine,r=e.width,i=e.height,u=e.ticksGenerator,c=e.className;if(e.hide)return null;var s=this.props,p=s.ticks,d=A(s,x),y=p;return a()(u)&&(y=p&&p.length>0?u(this.props):u(d)),r<=0||i<=0||!y||!y.length?null:o().createElement(f.W,{className:(0,l.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},n&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),h.J.renderCallByParent(this.props))}}])&&E(n.prototype,r),i&&E(n,i),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.Component);T(I,"displayName","CartesianAxis"),T(I,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},718:function(t,e,n){var r=n(435),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,n=r(e,t);return!(n<0)&&(n==e.length-1?e.pop():o.call(e,n,1),--this.size,!0)}},764:function(t,e,n){var r=n(6138),o=n(4207),i=n(5218),a=n(4383);t.exports=function(t,e){return(a(t)?r:i)(t,o(e,3))}},783:function(t,e,n){"use strict";n.d(e,{u:function(){return g}});var r=n(7064),o=n(1609),i=n.n(o),a=n(1444),u=n.n(a),c=n(9085),l=n(186),s=n(928),f=n(6075);function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach(function(e){y(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function y(t,e,n){var r;return r=function(t,e){if("object"!=p(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==p(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function v(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return m(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var g=(0,o.forwardRef)(function(t,e){var n=t.aspect,a=t.initialDimension,p=void 0===a?{width:-1,height:-1}:a,h=t.width,y=void 0===h?"100%":h,m=t.height,g=void 0===m?"100%":m,b=t.minWidth,x=void 0===b?0:b,w=t.minHeight,O=t.maxHeight,j=t.children,S=t.debounce,A=void 0===S?0:S,E=t.id,P=t.className,k=t.onResize,M=t.style,_=void 0===M?{}:M,T=(0,o.useRef)(null),C=(0,o.useRef)();C.current=k,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(T.current,"current",{get:function(){return T.current},configurable:!0})});var I=v((0,o.useState)({containerWidth:p.width,containerHeight:p.height}),2),D=I[0],N=I[1],B=(0,o.useCallback)(function(t,e){N(function(n){var r=Math.round(t),o=Math.round(e);return n.containerWidth===r&&n.containerHeight===o?n:{containerWidth:r,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,n=t[0].contentRect,r=n.width,o=n.height;B(r,o),null===(e=C.current)||void 0===e||e.call(C,r,o)};A>0&&(t=u()(t,A,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),n=T.current.getBoundingClientRect(),r=n.width,o=n.height;return B(r,o),e.observe(T.current),function(){e.disconnect()}},[B,A]);var R=(0,o.useMemo)(function(){var t=D.containerWidth,e=D.containerHeight;if(t<0||e<0)return null;(0,s.R)((0,l._3)(y)||(0,l._3)(g),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,g),(0,s.R)(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=(0,l._3)(y)?t:y,a=(0,l._3)(g)?e:g;n&&n>0&&(r?a=r/n:a&&(r=a*n),O&&a>O&&(a=O)),(0,s.R)(r>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,a,y,g,x,w,n);var u=!Array.isArray(j)&&(0,c.isElement)(j)&&(0,f.Mn)(j.type).endsWith("Chart");return i().Children.map(j,function(t){return(0,c.isElement)(t)?(0,o.cloneElement)(t,d({width:r,height:a},u?{style:d({height:"100%",width:"100%",maxHeight:a,maxWidth:r},t.props.style)}:{})):t})},[n,j,g,O,w,x,D,y]);return i().createElement("div",{id:E?"".concat(E):void 0,className:(0,r.A)("recharts-responsive-container",P),style:d(d({},_),{},{width:y,height:g,minWidth:x,minHeight:w,maxHeight:O}),ref:T},R)})},789:function(t,e,n){var r=n(7752),o=n(718),i=n(7849),a=n(3957),u=n(845);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},836:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=0,i=[];++n<r;){var a=t[n];e(a,n,t)&&(i[o++]=a)}return i}},845:function(t,e,n){var r=n(435);t.exports=function(t,e){var n=this.__data__,o=r(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this}},856:function(t,e,n){"use strict";var r=n(9564);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,n,o,i,a){if(a!==r){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},928:function(t,e,n){"use strict";n.d(e,{R:function(){return r}});var r=function(t,e){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o]}},958:function(t){"use strict";var e=Object.prototype.hasOwnProperty,n="~";function r(){}function o(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function i(t,e,r,i,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var u=new o(r,i||t,a),c=n?n+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new r:delete t._events[e]}function u(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),u.prototype.eventNames=function(){var t,r,o=[];if(0===this._eventsCount)return o;for(r in t=this._events)e.call(t,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},u.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},u.prototype.emit=function(t,e,r,o,i,a){var u=n?n+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,r),!0;case 4:return s.fn.call(s.context,e,r,o),!0;case 5:return s.fn.call(s.context,e,r,o,i),!0;case 6:return s.fn.call(s.context,e,r,o,i,a),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,r);break;case 4:s[l].fn.call(s[l].context,e,r,o);break;default:if(!c)for(p=1,c=new Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,n){return i(this,t,e,n,!1)},u.prototype.once=function(t,e,n){return i(this,t,e,n,!0)},u.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||r&&u.context!==r||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||r&&u[c].context!==r)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&a(this,e)):(this._events=new r,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=n,u.EventEmitter=u,t.exports=u},971:function(t,e,n){var r=n(8037);t.exports=function(t,e){var n=r(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this}},1029:function(t,e,n){var r=n(7187),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=r?r.toStringTag:void 0;t.exports=function(t){var e=i.call(t,u),n=t[u];try{t[u]=void 0;var r=!0}catch(t){}var o=a.call(t);return r&&(e?t[u]=n:delete t[u]),o}},1036:function(t,e,n){var r=n(8037);t.exports=function(t){var e=r(this,t).delete(t);return this.size-=e?1:0,e}},1158:function(t){var e=/\s/;t.exports=function(t){for(var n=t.length;n--&&e.test(t.charAt(n)););return n}},1192:function(t,e,n){var r=n(3536);t.exports=function(t,e){if(t!==e){var n=void 0!==t,o=null===t,i=t==t,a=r(t),u=void 0!==e,c=null===e,l=e==e,s=r(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!n&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&n&&i&&!o&&!a||c&&n&&i||!u&&i||!l)return-1}return 0}},1308:function(t,e,n){var r=n(1482);t.exports=function(t,e,n){var o=t.length;return n=void 0===n?o:n,!e&&n>=o?t:r(t,e,n)}},1311:function(t,e,n){var r=n(128)();t.exports=r},1329:function(t){t.exports=function(){return!1}},1366:function(t,e,n){var r=n(3188)(n(7183),"WeakMap");t.exports=r},1429:function(t){t.exports=function(t,e){return function(n){return t(e(n))}}},1444:function(t,e,n){var r=n(3803),o=n(6015);t.exports=function(t,e,n){var i=!0,a=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return o(n)&&(i="leading"in n?!!n.leading:i,a="trailing"in n?!!n.trailing:a),r(t,e,{leading:i,maxWait:e,trailing:a})}},1447:function(t,e,n){"use strict";n.d(e,{c:function(){return l}});var r=n(1609),o=n.n(r),i=n(7064),a=n(3038),u=n(6075);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},c.apply(this,arguments)}var l=function(t){var e=t.cx,n=t.cy,r=t.r,l=t.className,s=(0,i.A)("recharts-dot",l);return e===+e&&n===+n&&r===+r?o().createElement("circle",c({},(0,u.J9)(t,!1),(0,a._U)(t),{className:s,cx:e,cy:n,r:r})):null}},1451:function(t,e,n){var r=n(5674),o=n(1036),i=n(31),a=n(1907),u=n(971);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},1482:function(t){t.exports=function(t,e,n){var r=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(n=n>o?o:n)<0&&(n+=o),o=e>n?0:n-e>>>0,e>>>=0;for(var i=Array(o);++r<o;)i[r]=t[r+e];return i}},1490:function(t,e,n){var r=n(3188)(n(7183),"Promise");t.exports=r},1574:function(t,e,n){var r=n(9142),o=n(6895),i=n(4207);t.exports=function(t,e){var n={};return e=i(e,3),o(t,function(t,o,i){r(n,o,e(t,o,i))}),n}},1596:function(t,e,n){"use strict";n.d(e,{h:function(){return m}});var r=n(1609),o=n.n(r),i=n(7064),a=n(6075),u=n(6006),c=n(186);function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function s(){return s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},s.apply(this,arguments)}function f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?f(Object(n),!0).forEach(function(e){h(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function h(t,e,n){var r;return r=function(t,e){if("object"!=l(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==l(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var d=function(t){var e=t.cx,n=t.cy,r=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(a?1:-1)+r,f=Math.asin(c/s)/u.Kg,p=l?o:o+i*f,h=l?o-i*f:o;return{center:(0,u.IZ)(e,n,s,p),circleTangency:(0,u.IZ)(e,n,r,p),lineTangency:(0,u.IZ)(e,n,s*Math.cos(f*u.Kg),h),theta:f}},y=function(t){var e=t.cx,n=t.cy,r=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=function(t,e){return(0,c.sA)(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),l=i+a,s=(0,u.IZ)(e,n,o,i),f=(0,u.IZ)(e,n,o,l),p="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(i>l),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(r>0){var h=(0,u.IZ)(e,n,r,i),d=(0,u.IZ)(e,n,r,l);p+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(r,",").concat(r,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(i<=l),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(e,",").concat(n," Z");return p},v={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},m=function(t){var e=p(p({},v),t),n=e.cx,r=e.cy,u=e.innerRadius,l=e.outerRadius,f=e.cornerRadius,h=e.forceCornerRadius,m=e.cornerIsExternal,g=e.startAngle,b=e.endAngle,x=e.className;if(l<u||g===b)return null;var w,O=(0,i.A)("recharts-sector",x),j=l-u,S=(0,c.F4)(f,j,0,!0);return w=S>0&&Math.abs(g-b)<360?function(t){var e=t.cx,n=t.cy,r=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.sA)(s-l),p=d({cx:e,cy:n,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:u}),h=p.circleTangency,v=p.lineTangency,m=p.theta,g=d({cx:e,cy:n,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:u}),b=g.circleTangency,x=g.lineTangency,w=g.theta,O=u?Math.abs(l-s):Math.abs(l-s)-m-w;if(O<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):y({cx:e,cy:n,innerRadius:r,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(O>180),",").concat(+(f<0),",").concat(b.x,",").concat(b.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(r>0){var S=d({cx:e,cy:n,radius:r,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),A=S.circleTangency,E=S.lineTangency,P=S.theta,k=d({cx:e,cy:n,radius:r,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=u?Math.abs(l-s):Math.abs(l-s)-P-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(n,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(r,",").concat(r,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(E.x,",").concat(E.y,"Z")}else j+="L".concat(e,",").concat(n,"Z");return j}({cx:n,cy:r,innerRadius:u,outerRadius:l,cornerRadius:Math.min(S,j/2),forceCornerRadius:h,cornerIsExternal:m,startAngle:g,endAngle:b}):y({cx:n,cy:r,innerRadius:u,outerRadius:l,startAngle:g,endAngle:b}),o().createElement("path",s({},(0,a.J9)(e,!0),{className:O,d:w,role:"img"}))}},1607:function(t){t.exports=function(t,e){return null!=t&&e in Object(t)}},1784:function(t){t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},1870:function(t){t.exports=function(t,e){return null==t?void 0:t[e]}},1907:function(t,e,n){var r=n(8037);t.exports=function(t){return r(this,t).has(t)}},1960:function(t,e,n){var r=n(3188)(Object,"create");t.exports=r},1989:function(t,e,n){"use strict";n.d(e,{u:function(){return s}});var r=n(1609),o=n.n(r),i=n(7064),a=n(6075),u=["children","width","height","viewBox","className","style","title","desc"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},c.apply(this,arguments)}function l(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function s(t){var e=t.children,n=t.width,r=t.height,s=t.viewBox,f=t.className,p=t.style,h=t.title,d=t.desc,y=l(t,u),v=s||{width:n,height:r,x:0,y:0},m=(0,i.A)("recharts-surface",f);return o().createElement("svg",c({},(0,a.J9)(y,!0,"svg"),{className:m,width:n,height:r,style:p,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height)}),o().createElement("title",null,h),o().createElement("desc",null,d),e)}},2004:function(t,e,n){var r=n(4207),o=n(3247);t.exports=function(t,e){return t&&t.length?o(t,r(e,2)):[]}},2110:function(t,e,n){var r=n(9098);t.exports=function(t){var e=r(t,function(t){return 500===n.size&&n.clear(),t}),n=e.cache;return e}},2139:function(t,e,n){var r=n(7316),o=n(5369)(r);t.exports=o},2157:function(t,e,n){"use strict";n.d(e,{s:function(){return u}});var r=n(2004),o=n.n(r),i=n(4360),a=n.n(i);function u(t,e,n){return!0===e?o()(t,n):a()(e)?o()(t,e):t}},2193:function(t,e){"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,u=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,l=n?Symbol.for("react.context"):60110,s=n?Symbol.for("react.async_mode"):60111,f=n?Symbol.for("react.concurrent_mode"):60111,p=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,d=n?Symbol.for("react.suspense_list"):60120,y=n?Symbol.for("react.memo"):60115,v=n?Symbol.for("react.lazy"):60116,m=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,x=n?Symbol.for("react.scope"):60119;function w(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case s:case f:case i:case u:case a:case h:return t;default:switch(t=t&&t.$$typeof){case l:case p:case v:case y:case c:return t;default:return e}}case o:return e}}}function O(t){return w(t)===f}e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===r},e.isFragment=function(t){return w(t)===i}},2208:function(t,e,n){var r=n(3028),o=n(9650),i=n(2993),a=n(9668),u=n(8938),c=n(6459),l=n(4671);t.exports=function(t,e){return a(t)&&u(e)?c(l(t),e):function(n){var a=o(n,t);return void 0===a&&a===e?i(n,t):r(e,a,3)}}},2261:function(t,e,n){"use strict";function r(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}n.d(e,{C:function(){return r},K:function(){return o}})},2285:function(t,e,n){var r=n(7187),o=n(5962),i=n(4383),a=r?r.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},2381:function(t){t.exports=function(t){return null==t}},2499:function(t,e,n){var r=n(4183);t.exports=function(t,e){var n=!0;return r(t,function(t,r,o){return n=!!e(t,r,o)}),n}},2680:function(t){t.exports=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}},2717:function(t,e,n){var r=n(6138),o=n(2748),i=n(4207),a=n(5218),u=n(3111),c=n(6143),l=n(6323),s=n(9090),f=n(4383);t.exports=function(t,e,n){e=e.length?r(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;e=r(e,c(i));var h=a(t,function(t,n,o){return{criteria:r(e,function(e){return e(t)}),index:++p,value:t}});return u(h,function(t,e){return l(t,e,n)})}},2729:function(t,e,n){"use strict";n.d(e,{J:function(){return v},M:function(){return g}});var r=n(1609),o=n.n(r),i=n(7064),a=n(4227),u=n(6075);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach(function(e){d(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function d(t,e,n){var r;return r=function(t,e){if("object"!=c(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==c(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var y=function(t,e,n,r,o){var i,a=Math.min(Math.abs(n)/2,Math.abs(r)/2),u=r>=0?1:-1,c=n>=0?1:-1,l=r>=0&&n>=0||r<0&&n<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+n-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+n,",").concat(e+u*s[1])),i+="L ".concat(t+n,",").concat(e+r-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+n-c*s[2],",").concat(e+r)),i+="L ".concat(t+c*s[3],",").concat(e+r),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+r-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+n-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+n,",").concat(e+u*p,"\n            L ").concat(t+n,",").concat(e+r-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+n-c*p,",").concat(e+r,"\n            L ").concat(t+c*p,",").concat(e+r,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+r-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(n," v ").concat(r," h ").concat(-n," Z");return i},v=function(t,e){if(!t||!e)return!1;var n=t.x,r=t.y,o=e.x,i=e.y,a=e.width,u=e.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+u),f=Math.max(i,i+u);return n>=c&&n<=l&&r>=s&&r<=f}return!1},m={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},g=function(t){var e=h(h({},m),t),n=(0,r.useRef)(),c=s((0,r.useState)(-1),2),f=c[0],p=c[1];(0,r.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&p(t)}catch(t){}},[]);var d=e.x,v=e.y,g=e.width,b=e.height,x=e.radius,w=e.className,O=e.animationEasing,j=e.animationDuration,S=e.animationBegin,A=e.isAnimationActive,E=e.isUpdateAnimationActive;if(d!==+d||v!==+v||g!==+g||b!==+b||0===g||0===b)return null;var P=(0,i.A)("recharts-rectangle",w);return E?o().createElement(a.Ay,{canBegin:f>0,from:{width:g,height:b,x:d,y:v},to:{width:g,height:b,x:d,y:v},duration:j,animationEasing:O,isActive:E},function(t){var r=t.width,i=t.height,c=t.x,s=t.y;return o().createElement(a.Ay,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:A,easing:O},o().createElement("path",l({},(0,u.J9)(e,!0),{className:P,d:y(c,s,r,i,x),ref:n})))}):o().createElement("path",l({},(0,u.J9)(e,!0),{className:P,d:y(d,v,g,b,x)}))}},2748:function(t,e,n){var r=n(4679),o=n(4671);t.exports=function(t,e){for(var n=0,i=(e=r(e,t)).length;null!=t&&n<i;)t=t[o(e[n++])];return n&&n==i?t:void 0}},2801:function(t,e,n){var r=n(9121),o=n(9406),i=n(6459);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(n){return n===t||r(n,t,e)}}},2804:function(t,e,n){"use strict";n.d(e,{P2:function(){return ut},pr:function(){return rt},bx:function(){return ct},vh:function(){return it},sl:function(){return ot}});var r=n(1574),o=n.n(r),i=n(165),a=n.n(i),u=n(9162),c=n(6075),l=n(186),s=n(1609),f=n.n(s),p=n(7064),h=n(4227),d=n(8434),y=n.n(d),v=n(2381),m=n.n(v),g=n(6799),b=n(6352),x=function(t){return null};x.displayName="Cell";var w=n(5396),O=n(9e3),j=n(3038),S=n(9685),A=n(9653),E=["x","y"];function P(t){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function k(){return k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},k.apply(this,arguments)}function M(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?M(Object(n),!0).forEach(function(e){T(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function T(t,e,n){var r;return r=function(t,e){if("object"!=P(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=P(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==P(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function C(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function I(t,e){var n=t.x,r=t.y,o=C(t,E),i="".concat(n),a=parseInt(i,10),u="".concat(r),c=parseInt(u,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return _(_(_(_(_({},e),o),a?{x:a}:{}),c?{y:c}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function D(t){return f().createElement(A.yp,k({shapeType:"rectangle",propTransformer:I,activeClassName:"recharts-active-bar"},t))}var N,B=["value","background"];function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function L(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},z.apply(this,arguments)}function U(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function F(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?U(Object(n),!0).forEach(function(e){G(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):U(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function $(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Y(r.key),r)}}function W(t,e,n){return e=X(e),function(t,e){if(e&&("object"===R(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return H(t)}(t,q()?Reflect.construct(e,n||[],X(t).constructor):e.apply(t,n))}function q(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(q=function(){return!!t})()}function X(t){return X=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},X(t)}function H(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function V(t,e){return V=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},V(t,e)}function G(t,e,n){return(e=Y(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Y(t){var e=function(t,e){if("object"!=R(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=R(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==R(e)?e:String(e)}var K=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return G(H(t=W(this,e,[].concat(r))),"state",{isAnimationFinished:!1}),G(H(t),"id",(0,l.NF)("recharts-bar-")),G(H(t),"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),G(H(t),"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}var n,r,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&V(t,e)}(e,t),n=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(r=[{key:"renderRectanglesStatically",value:function(t){var e=this,n=this.props,r=n.shape,o=n.dataKey,i=n.activeIndex,a=n.activeBar,u=(0,c.J9)(this.props,!1);return t&&t.map(function(t,n){var c=n===i,l=c?a:r,s=F(F(F({},u),t),{},{isActive:c,option:l,index:n,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return f().createElement(g.W,z({className:"recharts-bar-rectangle"},(0,j.XC)(e.props,t,n),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),f().createElement(D,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,n=e.data,r=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,u=e.animationEasing,c=e.animationId,s=this.state.prevData;return f().createElement(h.Ay,{begin:i,duration:a,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=n.map(function(t,e){var n=s&&s[e];if(n){var i=(0,l.Dj)(n.x,t.x),a=(0,l.Dj)(n.y,t.y),u=(0,l.Dj)(n.width,t.width),c=(0,l.Dj)(n.height,t.height);return F(F({},t),{},{x:i(o),y:a(o),width:u(o),height:c(o)})}if("horizontal"===r){var f=(0,l.Dj)(0,t.height)(o);return F(F({},t),{},{y:t.y+t.height-f,height:f})}var p=(0,l.Dj)(0,t.width)(o);return F(F({},t),{},{width:p})});return f().createElement(g.W,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,n=t.isAnimationActive,r=this.state.prevData;return!(n&&e&&e.length)||r&&y()(r,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,n=e.data,r=e.dataKey,o=e.activeIndex,i=(0,c.J9)(this.props.background,!1);return n.map(function(e,n){e.value;var a=e.background,u=L(e,B);if(!a)return null;var c=F(F(F(F(F({},u),{},{fill:"#eee"},a),i),(0,j.XC)(t.props,e,n)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:r,index:n,key:"background-bar-".concat(n),className:"recharts-bar-background-rectangle"});return f().createElement(D,z({option:t.props.background,isActive:n===o},c))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,r=n.data,o=n.xAxis,i=n.yAxis,a=n.layout,l=n.children,s=(0,c.aS)(l,b.u);if(!s)return null;var p="vertical"===a?r[0].height/2:r[0].width/2,h=function(t,e){var n=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:n,errorVal:(0,u.kr)(t,e)}},d={clipPath:t?"url(#clipPath-".concat(e,")"):null};return f().createElement(g.W,d,s.map(function(t){return f().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:r,xAxis:o,yAxis:i,layout:a,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.data,r=t.className,o=t.xAxis,i=t.yAxis,a=t.left,u=t.top,c=t.width,l=t.height,s=t.isAnimationActive,h=t.background,d=t.id;if(e||!n||!n.length)return null;var y=this.state.isAnimationFinished,v=(0,p.A)("recharts-bar",r),b=o&&o.allowDataOverflow,x=i&&i.allowDataOverflow,O=b||x,j=m()(d)?this.id:d;return f().createElement(g.W,{className:v},b||x?f().createElement("defs",null,f().createElement("clipPath",{id:"clipPath-".concat(j)},f().createElement("rect",{x:b?a:a-c/2,y:x?u:u-l/2,width:b?c:2*c,height:x?l:2*l}))):null,f().createElement(g.W,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(j,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,j),(!s||y)&&w.Z.renderCallByParent(this.props,n))}}])&&$(n.prototype,r),o&&$(n,o),Object.defineProperty(n,"prototype",{writable:!1}),e}(s.PureComponent);function J(t){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},J(t)}function Z(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,nt(r.key),r)}}function Q(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Q(Object(n),!0).forEach(function(e){et(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function et(t,e,n){return(e=nt(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function nt(t){var e=function(t,e){if("object"!=J(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=J(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==J(e)?e:String(e)}N=K,G(K,"displayName","Bar"),G(K,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!O.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),G(K,"getComposedData",function(t){var e=t.props,n=t.item,r=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,s=t.xAxisTicks,f=t.yAxisTicks,p=t.stackedData,h=t.dataStartIndex,d=t.displayedData,y=t.offset,v=(0,u.xi)(r,n);if(!v)return null;var m=e.layout,g=n.props,b=g.dataKey,w=g.children,O=g.minPointSize,j="horizontal"===m?a:i,A=p?j.scale.domain():null,E=(0,u.DW)({numericAxis:j}),P=(0,c.aS)(w,x),k=d.map(function(t,e){var r,c,d,y,g,x;p?r=(0,u._f)(p[h+e],A):(r=(0,u.kr)(t,b),Array.isArray(r)||(r=[E,r]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(n,r){if("number"==typeof t)return t;var o="number"==typeof n;return o?t(n,r):(o||(0,S.A)(!1),e)}}(O,N.defaultProps.minPointSize)(r[1],e);if("horizontal"===m){var j,k=[a.scale(r[0]),a.scale(r[1])],M=k[0],_=k[1];c=(0,u.y2)({axis:i,ticks:s,bandSize:o,offset:v.offset,entry:t,index:e}),d=null!==(j=null!=_?_:M)&&void 0!==j?j:void 0,y=v.size;var T=M-_;if(g=Number.isNaN(T)?0:T,x={x:c,y:a.y,width:y,height:a.height},Math.abs(w)>0&&Math.abs(g)<Math.abs(w)){var C=(0,l.sA)(g||w)*(Math.abs(w)-Math.abs(g));d-=C,g+=C}}else{var I=[i.scale(r[0]),i.scale(r[1])],D=I[0],B=I[1];if(c=D,d=(0,u.y2)({axis:a,ticks:f,bandSize:o,offset:v.offset,entry:t,index:e}),y=B-D,g=v.size,x={x:i.x,y:d,width:i.width,height:g},Math.abs(w)>0&&Math.abs(y)<Math.abs(w))y+=(0,l.sA)(y||w)*(Math.abs(w)-Math.abs(y))}return F(F(F({},t),{},{x:c,y:d,width:y,height:g,value:p?r:r[1],payload:t,background:x},P&&P[e]&&P[e].props),{},{tooltipPayload:[(0,u.zb)(n,t)],tooltipPosition:{x:c+y/2,y:d+g/2}})});return F({data:k,layout:m},y)});var rt=function(t,e,n,r,o){var i=t.width,a=t.height,s=t.layout,f=t.children,p=Object.keys(e),h={left:n.left,leftMirror:n.left,right:i-n.right,rightMirror:i-n.right,top:n.top,topMirror:n.top,bottom:a-n.bottom,bottomMirror:a-n.bottom},d=!!(0,c.BU)(f,K);return p.reduce(function(i,a){var c,f,p,y,v,m=e[a],g=m.orientation,b=m.domain,x=m.padding,w=void 0===x?{}:x,O=m.mirror,j=m.reversed,S="".concat(g).concat(O?"Mirror":"");if("number"===m.type&&("gap"===m.padding||"no-gap"===m.padding)){var A=b[1]-b[0],E=1/0,P=m.categoricalDomain.sort();if(P.forEach(function(t,e){e>0&&(E=Math.min((t||0)-(P[e-1]||0),E))}),Number.isFinite(E)){var k=E/A,M="vertical"===m.layout?n.height:n.width;if("gap"===m.padding&&(c=k*M/2),"no-gap"===m.padding){var _=(0,l.F4)(t.barCategoryGap,k*M),T=k*M/2;c=T-_-(T-_)/M*_}}}f="xAxis"===r?[n.left+(w.left||0)+(c||0),n.left+n.width-(w.right||0)-(c||0)]:"yAxis"===r?"horizontal"===s?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(c||0),n.top+n.height-(w.bottom||0)-(c||0)]:m.range,j&&(f=[f[1],f[0]]);var C=(0,u.W7)(m,o,d),I=C.scale,D=C.realScaleType;I.domain(b).range(f),(0,u.YB)(I);var N=(0,u.w7)(I,tt(tt({},m),{},{realScaleType:D}));"xAxis"===r?(v="top"===g&&!O||"bottom"===g&&O,p=n.left,y=h[S]-v*m.height):"yAxis"===r&&(v="left"===g&&!O||"right"===g&&O,p=h[S]-v*m.width,y=n.top);var B=tt(tt(tt({},m),N),{},{realScaleType:D,x:p,y:y,scale:I,width:"xAxis"===r?n.width:m.width,height:"yAxis"===r?n.height:m.height});return B.bandSize=(0,u.Hj)(B,N),m.hide||"xAxis"!==r?m.hide||(h[S]+=(v?-1:1)*B.width):h[S]+=(v?-1:1)*B.height,tt(tt({},i),{},et({},a,B))},{})},ot=function(t,e){var n=t.x,r=t.y,o=e.x,i=e.y;return{x:Math.min(n,o),y:Math.min(r,i),width:Math.abs(o-n),height:Math.abs(i-r)}},it=function(t){var e=t.x1,n=t.y1,r=t.x2,o=t.y2;return ot({x:e,y:n},{x:r,y:o})},at=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}var e,n,r;return e=t,n=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.bandAware,r=e.position;if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(n){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),n=e[0],r=e[e.length-1];return n<=r?t>=n&&t<=r:t>=r&&t<=n}}],r=[{key:"create",value:function(e){return new t(e)}}],n&&Z(e.prototype,n),r&&Z(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();et(at,"EPS",1e-4);var ut=function(t){var e=Object.keys(t).reduce(function(e,n){return tt(tt({},e),{},et({},n,at.create(t[n])))},{});return tt(tt({},e),{},{apply:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.bandAware,i=n.position;return o()(t,function(t,n){return e[n].apply(t,{bandAware:r,position:i})})},isInRange:function(t){return a()(t,function(t,n){return e[n].isInRange(t)})}})};var ct=function(t){var e=t.width,n=t.height,r=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=r*Math.PI/180,i=Math.atan(n/e),a=o>i&&o<Math.PI-i?n/Math.sin(o):e/Math.cos(o);return Math.abs(a)}},2823:function(t,e,n){var r=n(789),o=n(8926),i=n(2680),a=n(3851),u=n(8423),c=n(3967);function l(t){var e=this.__data__=new r(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,t.exports=l},2832:function(t,e,n){var r=n(4742);t.exports=function(t){return null==t?"":r(t)}},2993:function(t,e,n){var r=n(1607),o=n(3852);t.exports=function(t,e){return null!=t&&o(t,e,r)}},3028:function(t,e,n){var r=n(7114),o=n(6184);t.exports=function t(e,n,i,a,u){return e===n||(null==e||null==n||!o(e)&&!o(n)?e!=e&&n!=n:r(e,n,i,a,t,u))}},3038:function(t,e,n){"use strict";n.d(e,{QQ:function(){return u},VU:function(){return l},XC:function(){return p},_U:function(){return f},j2:function(){return s}});var r=n(1609),o=n(6015),i=n.n(o);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,r.isValidElement)(t)&&(n=t.props),!i()(n))return null;var o={};return Object.keys(n).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return n[t](n,e)})}),o},p=function(t,e,n){if(!i()(t)||"object"!==a(t))return null;var r=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(r||(r={}),r[o]=function(t,e,n){return function(r){return t(e,n,r),null}}(i,e,n))}),r}},3111:function(t){t.exports=function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}},3188:function(t,e,n){var r=n(3829),o=n(1870);t.exports=function(t,e){var n=o(t,e);return r(n)?n:void 0}},3232:function(t,e,n){var r=n(7938),o=n(4207),i=n(7098),a=n(4383),u=n(6218);t.exports=function(t,e,n){var c=a(t)?r:i;return n&&u(t,e,n)&&(e=void 0),c(t,o(e,3))}},3234:function(t,e,n){var r=n(5862),o=n(2285);t.exports=function t(e,n,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];n>0&&i(s)?n>1?t(s,n-1,i,a,u):r(u,s):a||(u[u.length]=s)}return u}},3237:function(t,e,n){var r=n(6990),o=n(6184);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==r(t)}},3247:function(t,e,n){var r=n(8869),o=n(5739),i=n(4003),a=n(7773),u=n(5891),c=n(8629);t.exports=function(t,e,n){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(n)p=!1,s=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,d=new r}else d=e?[]:h;t:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=n||0!==v?v:0,p&&m==m){for(var g=d.length;g--;)if(d[g]===m)continue t;e&&d.push(m),h.push(v)}else s(d,m,n)||(d!==h&&d.push(m),h.push(v))}return h}},3271:function(t,e,n){t=n.nmd(t);var r=n(5194),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&r.process,u=function(){try{var t=i&&i.require&&i.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},3294:function(t,e,n){"use strict";n.d(e,{Q:function(){return Je}});var r=n(1609),o=n.n(r),i=n(2381),a=n.n(i),u=n(4360),c=n.n(u),l=n(7736),s=n.n(l),f=n(9650),p=n.n(f),h=n(7493),d=n.n(h),y=n(1444),v=n.n(y),m=n(7064),g=n(9685),b=n(1989),x=n(6799),w=n(4505),O=n(9104),j=n(1447),S=n(2729),A=n(6075),E=n(5084),P=n(3704),k=n(9162),M=n(186);function _(t){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}function T(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function C(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?T(Object(n),!0).forEach(function(e){I(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function I(t,e,n){var r;return r=function(t,e){if("object"!=_(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var D=["Webkit","Moz","O","ms"];function N(t){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(t)}function B(){return B=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},B.apply(this,arguments)}function R(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function L(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?R(Object(n),!0).forEach(function(e){X(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function z(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,H(r.key),r)}}function U(t,e,n){return e=$(e),function(t,e){if(e&&("object"===N(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return W(t)}(t,F()?Reflect.construct(e,n||[],$(t).constructor):e.apply(t,n))}function F(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(F=function(){return!!t})()}function $(t){return $=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},$(t)}function W(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function q(t,e){return q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},q(t,e)}function X(t,e,n){return(e=H(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function H(t){var e=function(t,e){if("object"!=N(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=N(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==N(e)?e:String(e)}var V=function(t){return t.changedTouches&&!!t.changedTouches.length},G=function(t){function e(t){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),X(W(n=U(this,e,[t])),"handleDrag",function(t){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(t):n.state.isSlideMoving&&n.handleSlideDrag(t)}),X(W(n),"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&n.handleDrag(t.changedTouches[0])}),X(W(n),"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=n.props,e=t.endIndex,r=t.onDragEnd,o=t.startIndex;null==r||r({endIndex:e,startIndex:o})}),n.detachDragEndListener()}),X(W(n),"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),X(W(n),"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),X(W(n),"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),X(W(n),"handleSlideDragStart",function(t){var e=V(t)?t.changedTouches[0]:t;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(W(n),"startX"),endX:n.handleTravellerDragStart.bind(W(n),"endX")},n.state={},n}var n,i,a;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&q(t,e)}(e,t),n=e,a=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,n=t.y,r=t.width,i=t.height,a=t.stroke,u=Math.floor(n+i/2)-1;return o().createElement(o().Fragment,null,o().createElement("rect",{x:e,y:n,width:r,height:i,fill:a,stroke:"none"}),o().createElement("line",{x1:e+1,y1:u,x2:e+r-1,y2:u,fill:"none",stroke:"#fff"}),o().createElement("line",{x1:e+1,y1:u+2,x2:e+r-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,n){return o().isValidElement(t)?o().cloneElement(t,n):c()(t)?t(n):e.renderDefaultTraveller(n)}},{key:"getDerivedStateFromProps",value:function(t,e){var n=t.data,r=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(n!==e.prevData||a!==e.prevUpdateId)return L({prevData:n,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:r},n&&n.length?function(t){var e=t.data,n=t.startIndex,r=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,E.z)().domain(s()(0,u)).range([o,o+i-a]),l=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(r),scale:c,scaleValues:l}}({data:n,width:r,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(r!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+r-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:n,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:r,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var n=0,r=t.length-1;r-n>1;){var o=Math.floor((n+r)/2);t[o]>e?r=o:n=o}return e>=t[r]?r:n}}],(i=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var n=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=Math.min(n,r),l=Math.max(n,r),s=e.getIndexInRange(o,c),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,n=e.data,r=e.tickFormatter,o=e.dataKey,i=(0,k.kr)(n[t],o,t);return c()(r)?r(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,n=e.slideMoveStartX,r=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-n;p>0?p=Math.min(p,a+u-c-o,a+u-c-r):p<0&&(p=Math.max(p,a-r,a-o));var h=this.getIndex({startX:r+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:r+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var n=V(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:n.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,n=e.brushMoveStartX,r=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[r],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-n;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[r]=a+y;var v=this.getIndex(d),m=v.startIndex,g=v.endIndex;this.setState(X(X({},r,a+y),"brushMoveStartX",t.pageX),function(){var t;f&&(t=h.length-1,("startX"===r&&(o>i?m%p===0:g%p===0)||o<i&&g===t||"endX"===r&&(o>i?g%p===0:m%p===0)||o>i&&g===t)&&f(v))})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var n=this,r=this.state,o=r.scaleValues,i=r.startX,a=r.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(X({},e,s),function(){n.props.onChange(n.getIndex({startX:n.state.startX,endX:n.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,n=t.y,r=t.width,i=t.height,a=t.fill,u=t.stroke;return o().createElement("rect",{stroke:u,fill:a,x:e,y:n,width:r,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,n=t.y,i=t.width,a=t.height,u=t.data,c=t.children,l=t.padding,s=r.Children.only(c);return s?o().cloneElement(s,{x:e,y:n,width:i,height:a,margin:l,compact:!0,data:u}):null}},{key:"renderTravellerLayer",value:function(t,n){var r,i,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,d=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=L(L({},(0,A.J9)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),g=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(i=h[y])||void 0===i?void 0:i.name);return o().createElement(x.W,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[n],onTouchStart:this.travellerDragStartHandlers[n],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,n))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var n=this.props,r=n.y,i=n.height,a=n.stroke,u=n.travellerWidth,c=Math.min(t,e)+u,l=Math.max(Math.abs(e-t)-u,0);return o().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:r,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,n=t.endIndex,r=t.y,i=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return o().createElement(x.W,{className:"recharts-brush-texts"},o().createElement(P.E,B({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:r+i/2},f),this.getTextOfTick(e)),o().createElement(P.E,B({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:r+i/2},f),this.getTextOfTick(n)))}},{key:"render",value:function(){var t=this.props,e=t.data,n=t.className,r=t.children,i=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!(0,M.Et)(i)||!(0,M.Et)(a)||!(0,M.Et)(u)||!(0,M.Et)(c)||u<=0||c<=0)return null;var g=(0,m.A)("recharts-brush",n),b=1===o().Children.count(r),w=function(t,e){if(!t)return null;var n=t.replace(/(\w)/,function(t){return t.toUpperCase()}),r=D.reduce(function(t,r){return C(C({},t),{},I({},r+n,e))},{});return r[t]=e,r}("userSelect","none");return o().createElement(x.W,{className:g,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:w},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}])&&z(n.prototype,i),a&&z(n,a),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.PureComponent);X(G,"displayName","Brush"),X(G,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Y=n(8742),K=n(6678),J=n(7724),Z=function(t,e){var n=t.alwaysShow,r=t.ifOverflow;return n&&(r="extendDomain"),r===e},Q=n(2804),tt=n(928);function et(t){return et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},et(t)}function nt(){return nt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},nt.apply(this,arguments)}function rt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ot(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rt(Object(n),!0).forEach(function(e){it(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function it(t,e,n){var r;return r=function(t,e){if("object"!=et(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=et(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==et(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function at(t){var e=t.x,n=t.y,r=t.r,i=t.alwaysShow,a=t.clipPathId,u=(0,M.vh)(e),c=(0,M.vh)(n);if((0,tt.R)(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!c)return null;var l=function(t){var e=t.x,n=t.y,r=t.xAxis,o=t.yAxis,i=(0,Q.P2)({x:r.scale,y:o.scale}),a=i.apply({x:e,y:n},{bandAware:!0});return Z(t,"discard")&&!i.isInRange(a)?null:a}(t);if(!l)return null;var s=l.x,f=l.y,p=t.shape,h=t.className,d=ot(ot({clipPath:Z(t,"hidden")?"url(#".concat(a,")"):void 0},(0,A.J9)(t,!0)),{},{cx:s,cy:f});return o().createElement(x.W,{className:(0,m.A)("recharts-reference-dot",h)},at.renderDot(p,d),J.J.renderCallByParent(t,{x:s-r,y:f-r,width:2*r,height:2*r}))}at.displayName="ReferenceDot",at.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1},at.renderDot=function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(j.c,nt({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))};var ut=n(3232),ct=n.n(ut),lt=n(3499);function st(t){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},st(t)}function ft(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function pt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ft(Object(n),!0).forEach(function(e){ht(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ft(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function ht(t,e,n){var r;return r=function(t,e){if("object"!=st(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=st(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==st(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function dt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return yt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function vt(){return vt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},vt.apply(this,arguments)}function mt(t){var e=t.x,n=t.y,r=t.segment,i=t.xAxisId,a=t.yAxisId,u=t.shape,l=t.className,s=t.alwaysShow,f=(0,lt.Yp)(),p=(0,lt.AF)(i),h=(0,lt.Nk)(a),d=(0,lt.sk)();if(!f||!d)return null;(0,tt.R)(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var y=function(t,e,n,r,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(n){var h=c.y,d=t.y.apply(h,{position:i});if(Z(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(Z(c,"discard")&&!t.x.isInRange(m))return null;var g=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?g.reverse():g}if(r){var b=c.segment.map(function(e){return t.apply(e,{position:i})});return Z(c,"discard")&&ct()(b,function(e){return!t.isInRange(e)})?null:b}return null}((0,Q.P2)({x:p.scale,y:h.scale}),(0,M.vh)(e),(0,M.vh)(n),r&&2===r.length,d,t.position,p.orientation,h.orientation,t);if(!y)return null;var v=dt(y,2),g=v[0],b=g.x,w=g.y,O=v[1],j=O.x,S=O.y,E=pt(pt({clipPath:Z(t,"hidden")?"url(#".concat(f,")"):void 0},(0,A.J9)(t,!0)),{},{x1:b,y1:w,x2:j,y2:S});return o().createElement(x.W,{className:(0,m.A)("recharts-reference-line",l)},function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement("line",vt({},e,{className:"recharts-reference-line-line"}))}(u,E),J.J.renderCallByParent(t,(0,Q.vh)({x1:b,y1:w,x2:j,y2:S})))}function gt(t){return gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gt(t)}function bt(){return bt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},bt.apply(this,arguments)}function xt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function wt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(n),!0).forEach(function(e){Ot(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Ot(t,e,n){var r;return r=function(t,e){if("object"!=gt(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=gt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==gt(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}mt.displayName="ReferenceLine",mt.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"};function jt(t){var e=t.x1,n=t.x2,r=t.y1,i=t.y2,a=t.className,u=t.alwaysShow,c=t.clipPathId;(0,tt.R)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=(0,M.vh)(e),s=(0,M.vh)(n),f=(0,M.vh)(r),p=(0,M.vh)(i),h=t.shape;if(!(l||s||f||p||h))return null;var d=function(t,e,n,r,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,Q.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:n?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:r?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!Z(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Q.sl)(p,h):null}(l,s,f,p,t);if(!d&&!h)return null;var y=Z(t,"hidden")?"url(#".concat(c,")"):void 0;return o().createElement(x.W,{className:(0,m.A)("recharts-reference-area",a)},jt.renderRect(h,wt(wt({clipPath:y},(0,A.J9)(t,!0)),d)),J.J.renderCallByParent(t,d))}function St(t){return function(t){if(Array.isArray(t))return At(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return At(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return At(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function At(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}jt.displayName="ReferenceArea",jt.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1},jt.renderRect=function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(S.M,bt({},e,{className:"recharts-reference-area-rect"}))};var Et=function(t,e,n,r,o){var i=(0,A.aS)(t,mt),a=(0,A.aS)(t,at),u=[].concat(St(i),St(a)),c=(0,A.aS)(t,jt),l="".concat(r,"Id"),s=r[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===n&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[s])){var r=e.props[s];return[Math.min(t[0],r),Math.max(t[1],r)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===n&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[p])&&(0,M.Et)(e.props[h])){var r=e.props[p],o=e.props[h];return[Math.min(t[0],r,o),Math.max(t[1],r,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,M.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},Pt=n(6006),kt=n(9783),Mt=n(958),_t=new(n.n(Mt)()),Tt="recharts.syncMouseEvents",Ct=n(3038);function It(t){return It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},It(t)}function Dt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Bt(r.key),r)}}function Nt(t,e,n){return(e=Bt(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Bt(t){var e=function(t,e){if("object"!=It(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=It(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==It(e)?e:String(e)}var Rt=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Nt(this,"activeIndex",0),Nt(this,"coordinateList",[]),Nt(this,"layout","horizontal")}var e,n,r;return e=t,(n=[{key:"setDetails",value:function(t){var e,n=t.coordinateList,r=void 0===n?null:n,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=r?r:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var n=this.container.getBoundingClientRect(),r=n.x,o=n.y,i=n.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=r+a+u,s=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&Dt(e.prototype,n),r&&Dt(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();var Lt=n(9653),zt=n(8291);function Ut(t){return Ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ut(t)}var Ft=["x","y","top","left","width","height","className"];function $t(){return $t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},$t.apply(this,arguments)}function Wt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function qt(t,e,n){var r;return r=function(t,e){if("object"!=Ut(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Ut(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ut(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Xt(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var Ht=function(t,e,n,r,o,i){return"M".concat(t,",").concat(o,"v").concat(r,"M").concat(i,",").concat(e,"h").concat(n)},Vt=function(t){var e=t.x,n=void 0===e?0:e,r=t.y,i=void 0===r?0:r,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Wt(Object(n),!0).forEach(function(e){qt(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({x:n,y:i,top:u,left:l,width:f,height:h},Xt(t,Ft));return(0,M.Et)(n)&&(0,M.Et)(i)&&(0,M.Et)(f)&&(0,M.Et)(h)&&(0,M.Et)(u)&&(0,M.Et)(l)?o().createElement("path",$t({},(0,A.J9)(y,!0),{className:(0,m.A)("recharts-cross",d),d:Ht(n,i,f,h,u,l)})):null};function Gt(t){var e=t.cx,n=t.cy,r=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,Pt.IZ)(e,n,r,o),(0,Pt.IZ)(e,n,r,i)],cx:e,cy:n,radius:r,startAngle:o,endAngle:i}}var Yt=n(1596);function Kt(t,e,n){var r,o,i,a;if("horizontal"===t)i=r=e.x,o=n.top,a=n.top+n.height;else if("vertical"===t)a=o=e.y,r=n.left,i=n.left+n.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return Gt(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,Pt.IZ)(u,c,l,f),h=(0,Pt.IZ)(u,c,s,f);r=p.x,o=p.y,i=h.x,a=h.y}return[{x:r,y:o},{x:i,y:a}]}function Jt(t){return Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jt(t)}function Zt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function Qt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Zt(Object(n),!0).forEach(function(e){te(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Zt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function te(t,e,n){var r;return r=function(t,e){if("object"!=Jt(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Jt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Jt(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ee(t){var e,n=t.element,o=t.tooltipEventType,i=t.isActive,a=t.activeCoordinate,u=t.activePayload,c=t.offset,l=t.activeTooltipIndex,s=t.tooltipAxisBandSize,f=t.layout,p=t.chartName;if(!n||!n.props.cursor||!i||!a||"ScatterChart"!==p&&"axis"!==o)return null;var h=zt.I;if("ScatterChart"===p)e=a,h=Vt;else if("BarChart"===p)e=function(t,e,n,r){var o=r/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:n.left+.5,y:"horizontal"===t?n.top+.5:e.y-o,width:"horizontal"===t?r:n.width-1,height:"horizontal"===t?n.height-1:r}}(f,a,c,s),h=S.M;else if("radial"===f){var d=Gt(a),y=d.cx,v=d.cy,g=d.radius;e={cx:y,cy:v,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:g,outerRadius:g},h=Yt.h}else e={points:Kt(f,a,c)},h=zt.I;var b=Qt(Qt(Qt(Qt({stroke:"#ccc",pointerEvents:"none"},c),e),(0,A.J9)(n.props.cursor,!1)),{},{payload:u,payloadIndex:l,className:(0,m.A)("recharts-tooltip-cursor",n.props.cursor.className)});return(0,r.isValidElement)(n.props.cursor)?(0,r.cloneElement)(n.props.cursor,b):(0,r.createElement)(h,b)}var ne=["item"],re=["children","className","width","height","style","compact","title","desc"];function oe(t){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oe(t)}function ie(){return ie=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ie.apply(this,arguments)}function ae(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ye(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ue(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function ce(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,xe(r.key),r)}}function le(t,e,n){return e=fe(e),function(t,e){if(e&&("object"===oe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return pe(t)}(t,se()?Reflect.construct(e,n||[],fe(t).constructor):e.apply(t,n))}function se(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(se=function(){return!!t})()}function fe(t){return fe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},fe(t)}function pe(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function he(t,e){return he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},he(t,e)}function de(t){return function(t){if(Array.isArray(t))return ve(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ye(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ye(t,e){if(t){if("string"==typeof t)return ve(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ve(t,e):void 0}}function ve(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function me(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ge(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?me(Object(n),!0).forEach(function(e){be(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):me(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function be(t,e,n){return(e=xe(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function xe(t){var e=function(t,e){if("object"!=oe(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=oe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==oe(e)?e:String(e)}var we={xAxis:["bottom","top"],yAxis:["left","right"]},Oe={width:"100%",height:"100%"},je={x:0,y:0};function Se(t){return t}var Ae=function(t,e){var n=e.graphicalItems,r=e.dataStartIndex,o=e.dataEndIndex,i=(null!=n?n:[]).reduce(function(t,e){var n=e.props.data;return n&&n.length?[].concat(de(t),de(n)):t},[]);return i.length>0?i:t&&t.length&&(0,M.Et)(r)&&(0,M.Et)(o)?t.slice(r,o+1):[]};function Ee(t){return"number"===t?[0,"auto"]:void 0}var Pe,ke,Me,_e,Te,Ce,Ie,De,Ne,Be,Re,Le,ze,Ue,Fe=function(t,e,n,r){var o=t.graphicalItems,i=t.tooltipAxis,a=Ae(e,t);return n<0||!o||!o.length||n>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,M.eP)(f,i.dataKey,r)}else l=s&&s[n]||a[n];return l?[].concat(de(o),[(0,k.zb)(u,l)]):o},[])},$e=function(t,e,n,r){var o=r||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,n),a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,k.gH)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=Fe(t,e,l,s),p=function(t,e,n,r){var o=e.find(function(t){return t&&t.index===n});if(o){if("horizontal"===t)return{x:o.coordinate,y:r.y};if("vertical"===t)return{x:r.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=r.radius;return ge(ge(ge({},r),(0,Pt.IZ)(r.cx,r.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=r.angle;return ge(ge(ge({},r),(0,Pt.IZ)(r.cx,r.cy,u,c)),{},{angle:c,radius:u})}return je}(n,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},We=function(t,e){var n=e.axes,r=e.graphicalItems,o=e.axisType,i=e.axisIdKey,u=e.stackGroups,c=e.dataStartIndex,l=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,k._L)(f,o);return n.reduce(function(e,n){var y,v=n.props,m=v.type,g=v.dataKey,b=v.allowDataOverflow,x=v.allowDuplicatedCategory,w=v.scale,O=v.ticks,j=v.includeHidden,S=n.props[i];if(e[S])return e;var A,E,P,_=Ae(t.data,{graphicalItems:r.filter(function(t){return t.props[i]===S}),dataStartIndex:c,dataEndIndex:l}),T=_.length;(function(t,e,n){if("number"===n&&!0===e&&Array.isArray(t)){var r=null==t?void 0:t[0],o=null==t?void 0:t[1];if(r&&o&&(0,M.Et)(r)&&(0,M.Et)(o))return!0}return!1})(n.props.domain,b,m)&&(A=(0,k.AQ)(n.props.domain,null,b),!d||"number"!==m&&"auto"===w||(P=(0,k.Ay)(_,g,"category")));var C=Ee(m);if(!A||0===A.length){var I,D=null!==(I=n.props.domain)&&void 0!==I?I:C;if(g){if(A=(0,k.Ay)(_,g,m),"category"===m&&d){var N=(0,M.CG)(A);x&&N?(E=A,A=s()(0,T)):x||(A=(0,k.KC)(D,A,n).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(de(t),[e])},[]))}else if("category"===m)A=x?A.filter(function(t){return""!==t&&!a()(t)}):(0,k.KC)(D,A,n).reduce(function(t,e){return t.indexOf(e)>=0||""===e||a()(e)?t:[].concat(de(t),[e])},[]);else if("number"===m){var B=(0,k.A1)(_,r.filter(function(t){return t.props[i]===S&&(j||!t.props.hide)}),g,o,f);B&&(A=B)}!d||"number"!==m&&"auto"===w||(P=(0,k.Ay)(_,g,"category"))}else A=d?s()(0,T):u&&u[S]&&u[S].hasStack&&"number"===m?"expand"===h?[0,1]:(0,k.Mk)(u[S].stackGroups,c,l):(0,k.vf)(_,r.filter(function(t){return t.props[i]===S&&(j||!t.props.hide)}),m,f,!0);if("number"===m)A=Et(p,A,S,o,O),D&&(A=(0,k.AQ)(D,A,b));else if("category"===m&&D){var R=D;A.every(function(t){return R.indexOf(t)>=0})&&(A=R)}}return ge(ge({},e),{},be({},S,ge(ge({},n.props),{},{axisType:o,domain:A,categoricalDomain:P,duplicateDomain:E,originalDomain:null!==(y=n.props.domain)&&void 0!==y?y:C,isCategorical:d,layout:f})))},{})},qe=function(t,e){var n=e.axisType,r=void 0===n?"xAxis":n,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,f="".concat(r,"Id"),h=(0,A.aS)(l,o),d={};return h&&h.length?d=We(t,{axes:h,graphicalItems:i,axisType:r,axisIdKey:f,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(d=function(t,e){var n=e.graphicalItems,r=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,f=t.children,h=Ae(t.data,{graphicalItems:n,dataStartIndex:u,dataEndIndex:c}),d=h.length,y=(0,k._L)(l,o),v=-1;return n.reduce(function(t,e){var m,g=e.props[i],b=Ee("number");return t[g]?t:(v++,y?m=s()(0,d):a&&a[g]&&a[g].hasStack?(m=(0,k.Mk)(a[g].stackGroups,u,c),m=Et(f,m,g,o)):(m=(0,k.AQ)(b,(0,k.vf)(h,n.filter(function(t){return t.props[i]===g&&!t.props.hide}),"number",l),r.defaultProps.allowDataOverflow),m=Et(f,m,g,o)),ge(ge({},t),{},be({},g,ge(ge({axisType:o},r.defaultProps),{},{hide:!0,orientation:p()(we,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:b,isCategorical:y,layout:l}))))},{})}(t,{Axis:o,graphicalItems:i,axisType:r,axisIdKey:f,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),d},Xe=function(t){var e=t.children,n=t.defaultShowTooltip,r=(0,A.BU)(e,G),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),r&&r.props&&(r.props.startIndex>=0&&(o=r.props.startIndex),r.props.endIndex>=0&&(i=r.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(n)}},He=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Ve=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Ge=n(3878),Ye=n(9542),Ke=n(37),Je=(Pe={chartName:"AreaChart",GraphicalChild:Ge.G,axisComponents:[{axisType:"xAxis",AxisComp:Ye.W},{axisType:"yAxis",AxisComp:Ke.h}],formatAxisMap:Q.pr},Me=Pe.chartName,_e=Pe.GraphicalChild,Te=Pe.defaultTooltipEventType,Ce=void 0===Te?"axis":Te,Ie=Pe.validateTooltipEventTypes,De=void 0===Ie?["axis"]:Ie,Ne=Pe.axisComponents,Be=Pe.legendContent,Re=Pe.formatAxisMap,Le=Pe.defaultProps,ze=function(t,e){var n=e.graphicalItems,r=e.stackGroups,o=e.offset,i=e.updateId,u=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=He(s),y=d.numericAxisName,v=d.cateAxisName,m=function(t){return!(!t||!t.length)&&t.some(function(t){var e=(0,A.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0})}(n),b=[];return n.forEach(function(n,d){var x=Ae(t.data,{graphicalItems:[n],dataStartIndex:u,dataEndIndex:c}),w=n.props,O=w.dataKey,j=w.maxBarSize,S=n.props["".concat(y,"Id")],E=n.props["".concat(v,"Id")],P=Ne.reduce(function(t,r){var o=e["".concat(r.axisType,"Map")],i=n.props["".concat(r.axisType,"Id")];o&&o[i]||"zAxis"===r.axisType||(0,g.A)(!1);var a=o[i];return ge(ge({},t),{},be(be({},r.axisType,a),"".concat(r.axisType,"Ticks"),(0,k.Rh)(a)))},{}),M=P[v],_=P["".concat(v,"Ticks")],T=r&&r[S]&&r[S].hasStack&&(0,k.kA)(n,r[S].stackGroups),C=(0,A.Mn)(n.type).indexOf("Bar")>=0,I=(0,k.Hj)(M,_),D=[],N=m&&(0,k.tA)({barSize:l,stackGroups:r,totalSize:Ve(P,v)});if(C){var B,R,L=a()(j)?h:j,z=null!==(B=null!==(R=(0,k.Hj)(M,_,!0))&&void 0!==R?R:L)&&void 0!==B?B:0;D=(0,k.BX)({barGap:f,barCategoryGap:p,bandSize:z!==I?z:I,sizeList:N[E],maxBarSize:L}),z!==I&&(D=D.map(function(t){return ge(ge({},t),{},{position:ge(ge({},t.position),{},{offset:t.position.offset-z/2})})}))}var U=n&&n.type&&n.type.getComposedData;U&&b.push({props:ge(ge({},U(ge(ge({},P),{},{displayedData:x,props:t,dataKey:O,item:n,bandSize:I,barPosition:D,offset:o,stackedData:T,layout:s,dataStartIndex:u,dataEndIndex:c}))),{},be(be(be({key:n.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:(0,A.AW)(n,t.children),item:n})}),b},Ue=function(t,e){var n=t.props,r=t.dataStartIndex,o=t.dataEndIndex,i=t.updateId;if(!(0,A.Me)({props:n}))return null;var a=n.children,u=n.layout,c=n.stackOffset,l=n.data,s=n.reverseStackOrder,f=He(u),h=f.numericAxisName,y=f.cateAxisName,v=(0,A.aS)(a,_e),m=(0,k.Mn)(l,v,"".concat(h,"Id"),"".concat(y,"Id"),c,s),g=Ne.reduce(function(t,e){var i="".concat(e.axisType,"Map");return ge(ge({},t),{},be({},i,qe(n,ge(ge({},e),{},{graphicalItems:v,stackGroups:e.axisType===h&&m,dataStartIndex:r,dataEndIndex:o}))))},{}),b=function(t,e){var n=t.props,r=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=n.width,l=n.height,s=n.children,f=n.margin||{},h=(0,A.BU)(s,G),d=(0,A.BU)(s,O.s),y=Object.keys(u).reduce(function(t,e){var n=u[e],r=n.orientation;return n.mirror||n.hide?t:ge(ge({},t),{},be({},r,t[r]+n.width))},{left:f.left||0,right:f.right||0}),v=Object.keys(i).reduce(function(t,e){var n=i[e],r=n.orientation;return n.mirror||n.hide?t:ge(ge({},t),{},be({},r,p()(t,"".concat(r))+n.height))},{top:f.top||0,bottom:f.bottom||0}),m=ge(ge({},v),y),g=m.bottom;h&&(m.bottom+=h.props.height||G.defaultProps.height),d&&e&&(m=(0,k.s0)(m,r,n,e));var b=c-m.left-m.right,x=l-m.top-m.bottom;return ge(ge({brushBottom:g},m),{},{width:Math.max(b,0),height:Math.max(x,0)})}(ge(ge({},g),{},{props:n,graphicalItems:v}),null==e?void 0:e.legendBBox);Object.keys(g).forEach(function(t){g[t]=Re(n,g[t],b,t.replace("Map",""),Me)});var x,w,j,S=g["".concat(y,"Map")],E=(x=S,w=(0,M.lX)(x),{tooltipTicks:j=(0,k.Rh)(w,!1,!0),orderedTooltipTicks:d()(j,function(t){return t.coordinate}),tooltipAxis:w,tooltipAxisBandSize:(0,k.Hj)(w,j)}),P=ze(n,ge(ge({},g),{},{dataStartIndex:r,dataEndIndex:o,updateId:i,graphicalItems:v,stackGroups:m,offset:b}));return ge(ge({formattedGraphicalItems:P,graphicalItems:v,offset:b,stackGroups:m},E),g)},ke=function(t){function e(t){var n,i,u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),be(pe(u=le(this,e,[t])),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),be(pe(u),"accessibilityManager",new Rt),be(pe(u),"handleLegendBBoxUpdate",function(t){if(t){var e=u.state,n=e.dataStartIndex,r=e.dataEndIndex,o=e.updateId;u.setState(ge({legendBBox:t},Ue({props:u.props,dataStartIndex:n,dataEndIndex:r,updateId:o},ge(ge({},u.state),{},{legendBBox:t}))))}}),be(pe(u),"handleReceiveSyncEvent",function(t,e,n){if(u.props.syncId===t){if(n===u.eventEmitterSymbol&&"function"!=typeof u.props.syncMethod)return;u.applySyncEvent(e)}}),be(pe(u),"handleBrushChange",function(t){var e=t.startIndex,n=t.endIndex;if(e!==u.state.dataStartIndex||n!==u.state.dataEndIndex){var r=u.state.updateId;u.setState(function(){return ge({dataStartIndex:e,dataEndIndex:n},Ue({props:u.props,dataStartIndex:e,dataEndIndex:n,updateId:r},u.state))}),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:n})}}),be(pe(u),"handleMouseEnter",function(t){var e=u.getMouseInfo(t);if(e){var n=ge(ge({},e),{},{isTooltipActive:!0});u.setState(n),u.triggerSyncEvent(n);var r=u.props.onMouseEnter;c()(r)&&r(n,t)}}),be(pe(u),"triggeredAfterMouseMove",function(t){var e=u.getMouseInfo(t),n=e?ge(ge({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(n),u.triggerSyncEvent(n);var r=u.props.onMouseMove;c()(r)&&r(n,t)}),be(pe(u),"handleItemMouseEnter",function(t){u.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),be(pe(u),"handleItemMouseLeave",function(){u.setState(function(){return{isTooltipActive:!1}})}),be(pe(u),"handleMouseMove",function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)}),be(pe(u),"handleMouseLeave",function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var n=u.props.onMouseLeave;c()(n)&&n(e,t)}),be(pe(u),"handleOuterEvent",function(t){var e,n=(0,A.X_)(t),r=p()(u.props,"".concat(n));n&&c()(r)&&r(null!==(e=/.*touch.*/i.test(n)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))&&void 0!==e?e:{},t)}),be(pe(u),"handleClick",function(t){var e=u.getMouseInfo(t);if(e){var n=ge(ge({},e),{},{isTooltipActive:!0});u.setState(n),u.triggerSyncEvent(n);var r=u.props.onClick;c()(r)&&r(n,t)}}),be(pe(u),"handleMouseDown",function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)}),be(pe(u),"handleMouseUp",function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)}),be(pe(u),"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),be(pe(u),"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])}),be(pe(u),"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])}),be(pe(u),"triggerSyncEvent",function(t){void 0!==u.props.syncId&&_t.emit(Tt,u.props.syncId,t,u.eventEmitterSymbol)}),be(pe(u),"applySyncEvent",function(t){var e=u.props,n=e.layout,r=e.syncMethod,o=u.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(ge({dataStartIndex:i,dataEndIndex:a},Ue({props:u.props,dataStartIndex:i,dataEndIndex:a,updateId:o},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof r)s=r(h,t);else if("value"===r){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ge(ge({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),m=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,b=Fe(u.state,u.props.data,s),x=h[s]?{x:"horizontal"===n?h[s].coordinate:v,y:"horizontal"===n?m:h[s].coordinate}:je;u.setState(ge(ge({},t),{},{activeLabel:g,activeCoordinate:x,activePayload:b,activeTooltipIndex:s}))}else u.setState(t)}),be(pe(u),"renderCursor",function(t){var e,n=u.state,r=n.isTooltipActive,i=n.activeCoordinate,a=n.activePayload,c=n.offset,l=n.activeTooltipIndex,s=n.tooltipAxisBandSize,f=u.getTooltipEventType(),p=null!==(e=t.props.active)&&void 0!==e?e:r,h=u.props.layout,d=t.key||"_recharts-cursor";return o().createElement(ee,{key:d,activeCoordinate:i,activePayload:a,activeTooltipIndex:l,chartName:Me,element:t,isActive:p,layout:h,offset:c,tooltipAxisBandSize:s,tooltipEventType:f})}),be(pe(u),"renderPolarAxis",function(t,e,n){var o=p()(t,"type.axisType"),i=p()(u.state,"".concat(o,"Map")),a=i&&i[t.props["".concat(o,"Id")]];return(0,r.cloneElement)(t,ge(ge({},a),{},{className:(0,m.A)(o,a.className),key:t.key||"".concat(e,"-").concat(n),ticks:(0,k.Rh)(a,!0)}))}),be(pe(u),"renderPolarGrid",function(t){var e=t.props,n=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,M.lX)(c),f=(0,M.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,r.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,k.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,k.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:n})}),be(pe(u),"renderLegend",function(){var t=u.state.formattedGraphicalItems,e=u.props,n=e.children,o=e.width,i=e.height,a=u.props.margin||{},c=o-(a.left||0)-(a.right||0),l=(0,K.g)({children:n,formattedGraphicalItems:t,legendWidth:c,legendContent:Be});if(!l)return null;var s=l.item,f=ue(l,ne);return(0,r.cloneElement)(s,ge(ge({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))}),be(pe(u),"renderTooltip",function(){var t,e=u.props,n=e.children,o=e.accessibilityLayer,i=(0,A.BU)(n,w.m);if(!i)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:c;return(0,r.cloneElement)(i,{viewBox:ge(ge({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),be(pe(u),"renderBrush",function(t){var e=u.props,n=e.margin,o=e.data,i=u.state,a=i.offset,c=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,r.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,k.HQ)(u.handleBrushChange,t.props.onChange),data:o,x:(0,M.Et)(t.props.x)?t.props.x:a.left,y:(0,M.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:(0,M.Et)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})}),be(pe(u),"renderReferenceElement",function(t,e,n){if(!t)return null;var o=pe(u).clipPathId,i=u.state,a=i.xAxisMap,c=i.yAxisMap,l=i.offset,s=t.props,f=s.xAxisId,p=s.yAxisId;return(0,r.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(n),xAxis:a[f],yAxis:c[p],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),be(pe(u),"renderActivePoints",function(t){var n=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=n.props.key,l=n.item.props,s=l.activeDot,f=ge(ge({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,k.Ps)(n.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value,key:"".concat(c,"-activePoint-").concat(i)},(0,A.J9)(s,!1)),(0,Ct._U)(s));return u.push(e.renderActiveDot(s,f)),o?u.push(e.renderActiveDot(s,ge(ge({},f),{},{cx:o.x,cy:o.y,key:"".concat(c,"-basePoint-").concat(i)}))):a&&u.push(null),u}),be(pe(u),"renderGraphicChild",function(t,e,n){var o=u.filterFormatItem(t,e,n);if(!o)return null;var i=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=u.props.children,d=(0,A.BU)(h,w.m),y=o.props,v=y.points,m=y.isRange,g=y.baseLine,b=o.item.props,x=b.activeDot,O=b.hide,j=b.activeBar,S=b.activeShape,E=Boolean(!O&&l&&d&&(x||j||S)),P={};"axis"!==i&&d&&"click"===d.props.trigger?P={onClick:(0,k.HQ)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(P={onMouseLeave:(0,k.HQ)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,k.HQ)(u.handleItemMouseEnter,t.props.onMouseEnter)});var _=(0,r.cloneElement)(t,ge(ge({},o.props),P));if(E){if(!(f>=0)){var T,C=(null!==(T=u.getItemByXY(u.state.activeCoordinate))&&void 0!==T?T:{graphicalItem:_}).graphicalItem,I=C.item,D=void 0===I?t:I,N=C.childIndex,B=ge(ge(ge({},o.props),P),{},{activeIndex:N});return[(0,r.cloneElement)(D,B),null,null]}var R,L;if(s.dataKey&&!s.allowDuplicatedCategory){var z="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());R=(0,M.eP)(v,z,p),L=m&&g&&(0,M.eP)(g,z,p)}else R=null==v?void 0:v[f],L=m&&g&&g[f];if(S||j){var U=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,r.cloneElement)(t,ge(ge(ge({},o.props),P),{},{activeIndex:U})),null,null]}if(!a()(R))return[_].concat(de(u.renderActivePoints({item:o,activePoint:R,basePoint:L,childIndex:f,isRange:m})))}return m?[_,null,null]:[_,null]}),be(pe(u),"renderCustomized",function(t,e,n){return(0,r.cloneElement)(t,ge(ge({key:"recharts-customized-".concat(n)},u.props),u.state))}),be(pe(u),"renderMap",{CartesianGrid:{handler:Se,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:Se},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:Se},YAxis:{handler:Se},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!==(n=t.id)&&void 0!==n?n:(0,M.NF)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=v()(u.triggeredAfterMouseMove,null!==(i=t.throttleDelay)&&void 0!==i?i:1e3/60),u.state={},u}var n,i,u;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&he(t,e)}(e,t),n=e,i=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,n=t.data,r=t.height,o=t.layout,i=(0,A.BU)(e,w.m);if(i){var a=i.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=Fe(this.state,n,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+r)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ge(ge({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(n=this.props.margin.left)&&void 0!==n?n:0,top:null!==(r=this.props.margin.top)&&void 0!==r?r:0}}),null):null;var n,r}},{key:"componentDidUpdate",value:function(t){(0,A.OV)([(0,A.BU)(t.children,w.m)],[(0,A.BU)(this.props.children,w.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,A.BU)(this.props.children,w.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return De.indexOf(e)>=0?e:Ce}return Ce}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,n=e.getBoundingClientRect(),r=(0,Y.A3)(n),o={chartX:Math.round(t.pageX-r.left),chartY:Math.round(t.pageY-r.top)},i=n.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap;if("axis"!==this.getTooltipEventType()&&c&&l){var s=(0,M.lX)(c).scale,f=(0,M.lX)(l).scale,p=s&&s.invert?s.invert(o.chartX):null,h=f&&f.invert?f.invert(o.chartY):null;return ge(ge({},o),{},{xValue:p,yValue:h})}var d=$e(this.state,this.props.data,this.props.layout,a);return d?ge(ge({},o),d):null}},{key:"inRange",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=this.props.layout,o=t/n,i=e/n;if("horizontal"===r||"vertical"===r){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,M.lX)(c);return(0,Pt.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),n=(0,A.BU)(t,w.m),r={};return n&&"axis"===e&&(r="click"===n.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd}),ge(ge({},(0,Ct._U)(this.props,this.handleOuterEvent)),r)}},{key:"addListener",value:function(){_t.on(Tt,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){_t.removeListener(Tt,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,n){for(var r=this.state.formattedGraphicalItems,o=0,i=r.length;o<i;o++){var a=r[o];if(a.item===t||a.props.key===t.key||e===(0,A.Mn)(a.item.type)&&n===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,n=e.left,r=e.top,i=e.height,a=e.width;return o().createElement("defs",null,o().createElement("clipPath",{id:t},o().createElement("rect",{x:n,y:r,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var n=ae(e,2),r=n[0],o=n[1];return ge(ge({},t),{},be({},r,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var n=ae(e,2),r=n[0],o=n[1];return ge(ge({},t),{},be({},r,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,n=e.formattedGraphicalItems,r=e.activeItem;if(n&&n.length)for(var o=0,i=n.length;o<i;o++){var a=n[o],u=a.props,c=a.item,l=(0,A.Mn)(c.type);if("Bar"===l){var s=(u.data||[]).find(function(e){return(0,S.J)(t,e)});if(s)return{graphicalItem:a,payload:s}}else if("RadialBar"===l){var f=(u.data||[]).find(function(e){return(0,Pt.yy)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if((0,Lt.NE)(a,r)||(0,Lt.nZ)(a,r)||(0,Lt.xQ)(a,r)){var p=(0,Lt.GG)({graphicalItem:a,activeTooltipItem:r,itemData:c.props.data}),h=void 0===c.props.activeIndex?p:c.props.activeIndex;return{graphicalItem:ge(ge({},a),{},{childIndex:h}),payload:(0,Lt.xQ)(a,r)?c.props.data[p]:a.props.data[p]}}}return null}},{key:"render",value:function(){var t=this;if(!(0,A.Me)(this))return null;var e,n,r=this.props,i=r.children,a=r.className,u=r.width,c=r.height,l=r.style,s=r.compact,f=r.title,p=r.desc,h=ue(r,re),d=(0,A.J9)(h,!1);if(s)return o().createElement(lt.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o().createElement(b.u,ie({},d,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,A.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(n=this.props.role)&&void 0!==n?n:"application",d.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){t.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return o().createElement(lt.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o().createElement("div",ie({className:(0,m.A)("recharts-wrapper",a),style:ge({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(e){t.container=e}}),o().createElement(b.u,ie({},d,{width:u,height:c,title:f,desc:p,style:Oe}),this.renderClipPath(),(0,A.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],i&&ce(n.prototype,i),u&&ce(n,u),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.Component),be(ke,"displayName",Me),be(ke,"defaultProps",ge({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},Le)),be(ke,"getDerivedStateFromProps",function(t,e){var n=t.dataKey,r=t.data,o=t.children,i=t.width,u=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=Xe(t);return ge(ge(ge({},h),{},{updateId:0},Ue(ge(ge({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:n,prevData:r,prevWidth:i,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(n!==e.prevDataKey||r!==e.prevData||i!==e.prevWidth||u!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,kt.b)(s,e.prevMargin)){var d=Xe(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ge(ge({},$e(e,r,c)),{},{updateId:e.updateId+1}),m=ge(ge(ge({},d),y),v);return ge(ge(ge({},m),Ue(ge({props:t},m),e)),{},{prevDataKey:n,prevData:r,prevWidth:i,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,A.OV)(o,e.prevChildren)){var g,b,x,w,O=(0,A.BU)(o,G),j=O&&null!==(g=null===(b=O.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:f,S=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:p,E=j!==f||S!==p,P=a()(r)||E?e.updateId+1:e.updateId;return ge(ge({updateId:P},Ue(ge(ge({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),be(ke,"renderActiveDot",function(t,e){var n;return n=(0,r.isValidElement)(t)?(0,r.cloneElement)(t,e):c()(t)?t(e):o().createElement(j.c,e),o().createElement(x.W,{className:"recharts-active-dot",key:e.key},n)}),ke)},3300:function(t,e,n){var r=n(9169),o=n(9946),i=n(8420);t.exports=function(t){return r(t,i,o)}},3368:function(t){t.exports=function(t){return function(){return t}}},3499:function(t,e,n){"use strict";n.d(e,{DR:function(){return x},pj:function(){return j},rY:function(){return M},yi:function(){return k},Yp:function(){return w},hj:function(){return P},sk:function(){return E},AF:function(){return O},Nk:function(){return A},$G:function(){return S}});var r=n(1609),o=n.n(r),i=n(9685),a=n(9475),u=n.n(a),c=n(165),l=n.n(c),s=n(9098),f=n.n(s)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),p=n(186);var h=(0,r.createContext)(void 0),d=(0,r.createContext)(void 0),y=(0,r.createContext)(void 0),v=(0,r.createContext)({}),m=(0,r.createContext)(void 0),g=(0,r.createContext)(0),b=(0,r.createContext)(0),x=function(t){var e=t.state,n=e.xAxisMap,r=e.yAxisMap,i=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,s=f(i);return o().createElement(h.Provider,{value:n},o().createElement(d.Provider,{value:r},o().createElement(v.Provider,{value:i},o().createElement(y.Provider,{value:s},o().createElement(m.Provider,{value:a},o().createElement(g.Provider,{value:l},o().createElement(b.Provider,{value:c},u)))))))},w=function(){return(0,r.useContext)(m)};var O=function(t){var e=(0,r.useContext)(h);null==e&&(0,i.A)(!1);var n=e[t];return null==n&&(0,i.A)(!1),n},j=function(){var t=(0,r.useContext)(h);return(0,p.lX)(t)},S=function(){var t=(0,r.useContext)(d);return u()(t,function(t){return l()(t.domain,Number.isFinite)})||(0,p.lX)(t)},A=function(t){var e=(0,r.useContext)(d);null==e&&(0,i.A)(!1);var n=e[t];return null==n&&(0,i.A)(!1),n},E=function(){return(0,r.useContext)(y)},P=function(){return(0,r.useContext)(v)},k=function(){return(0,r.useContext)(b)},M=function(){return(0,r.useContext)(g)}},3536:function(t,e,n){var r=n(6990),o=n(6184);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==r(t)}},3572:function(t,e,n){var r=n(7066),o=n(6015),i=n(3536),a=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,c=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=r(t);var n=u.test(t);return n||c.test(t)?l(t.slice(2),n?2:8):a.test(t)?NaN:+t}},3615:function(t,e,n){var r=n(8342),o=n(9184),i=n(7747),a=n(4287),u=n(7);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},3681:function(t,e,n){var r=n(5351),o=n(4953),i=n(9668),a=n(4671);t.exports=function(t){return i(t)?r(a(t)):o(t)}},3704:function(t,e,n){"use strict";n.d(e,{E:function(){return U}});var r=n(1609),o=n.n(r),i=n(2381),a=n.n(i),u=n(7064),c=n(186),l=n(9e3),s=n(6075),f=n(8742);function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,v(r.key),r)}}function v(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:String(e)}var m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,g=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,x=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,w={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},O=Object.keys(w),j="NaN";var S=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=n,this.num=e,this.unit=n,Number.isNaN(e)&&(this.unit=""),""===n||b.test(n)||(this.num=NaN,this.unit=""),O.includes(n)&&(this.num=function(t,e){return t*w[e]}(e,n),this.unit="px")}var e,n,r;return e=t,r=[{key:"parse",value:function(e){var n,r=h(null!==(n=x.exec(e))&&void 0!==n?n:[],3),o=r[1],i=r[2];return new t(parseFloat(o),null!=i?i:"")}}],(n=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&y(e.prototype,n),r&&y(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function A(t){if(t.includes(j))return j;for(var e=t;e.includes("*")||e.includes("/");){var n,r=h(null!==(n=m.exec(e))&&void 0!==n?n:[],4),o=r[1],i=r[2],a=r[3],u=S.parse(null!=o?o:""),c=S.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return j;e=e.replace(m,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=h(null!==(s=g.exec(e))&&void 0!==s?s:[],4),p=f[1],d=f[2],y=f[3],v=S.parse(null!=p?p:""),b=S.parse(null!=y?y:""),x="+"===d?v.add(b):v.subtract(b);if(x.isNaN())return j;e=e.replace(g,x.toString())}return e}var E=/\(([^()]*)\)/;function P(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var n=h(E.exec(e),2)[1];e=e.replace(E,A(n))}return e}(e),e=A(e)}function k(t){var e=function(t){try{return P(t)}catch(t){return j}}(t.slice(5,-1));return e===j?"":e}var M=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],_=["dx","dy","angle","className","breakAll"];function T(){return T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},T.apply(this,arguments)}function C(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function I(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return D(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var N=/[ \f\n\r\t\v\u2028\u2029]+/,B=function(t){var e=t.children,n=t.breakAll,r=t.style;try{var o=[];return a()(e)||(o=n?e.toString().split(""):e.toString().split(N)),{wordsWithComputedWidth:o.map(function(t){return{word:t,width:(0,f.Pu)(t,r).width}}),spaceWidth:n?0:(0,f.Pu)(" ",r).width}}catch(t){return null}},R=function(t){return[{words:a()(t)?[]:t.toString().split(N)}]},L=function(t){var e=t.width,n=t.scaleToFit,r=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||n)&&!l.m.isSsr){var u=B({breakAll:i,children:r,style:o});return u?function(t,e,n,r,o){var i=t.maxLines,a=t.children,u=t.style,l=t.breakAll,s=(0,c.Et)(i),f=a,p=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];if(u&&(null==r||o||u.width+a+n<Number(r)))u.words.push(i),u.width+=a+n;else{var c={words:[i],width:a};t.push(c)}return t},[])},h=p(e);if(!s)return h;for(var d,y=function(t){var e=f.slice(0,t),n=B({breakAll:l,style:u,children:e+"…"}).wordsWithComputedWidth,o=p(n),a=o.length>i||function(t){return t.reduce(function(t,e){return t.width>e.width?t:e})}(o).width>Number(r);return[a,o]},v=0,m=f.length-1,g=0;v<=m&&g<=f.length-1;){var b=Math.floor((v+m)/2),x=I(y(b-1),2),w=x[0],O=x[1],j=I(y(b),1)[0];if(w||j||(v=b+1),w&&j&&(m=b-1),!w&&j){d=O;break}g++}return d||h}({breakAll:i,children:r,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,n):R(r)}return R(r)},z="#808080",U=function(t){var e=t.x,n=void 0===e?0:e,i=t.y,a=void 0===i?0:i,l=t.lineHeight,f=void 0===l?"1em":l,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=void 0===v?"start":v,g=t.verticalAnchor,b=void 0===g?"end":g,x=t.fill,w=void 0===x?z:x,O=C(t,M),j=(0,r.useMemo)(function(){return L({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:y,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,y,O.style,O.width]),S=O.dx,A=O.dy,E=O.angle,P=O.className,I=O.breakAll,D=C(O,_);if(!(0,c.vh)(n)||!(0,c.vh)(a))return null;var N,B=n+((0,c.Et)(S)?S:0),R=a+((0,c.Et)(A)?A:0);switch(b){case"start":N=k("calc(".concat(h,")"));break;case"middle":N=k("calc(".concat((j.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:N=k("calc(".concat(j.length-1," * -").concat(f,")"))}var U=[];if(y){var F=j[0].width,$=O.width;U.push("scale(".concat(((0,c.Et)($)?$/F:1)/F,")"))}return E&&U.push("rotate(".concat(E,", ").concat(B,", ").concat(R,")")),U.length&&(D.transform=U.join(" ")),o().createElement("text",T({},(0,s.J9)(D,!0),{x:B,y:R,className:(0,u.A)("recharts-text",P),textAnchor:m,fill:w.includes("url")?z:w}),j.map(function(t,e){var n=t.words.join(I?"":" ");return o().createElement("tspan",{x:B,dy:0===e?N:f,key:n},n)}))}},3705:function(t){t.exports=function(t,e,n,r){for(var o=t.length,i=n+(r?1:-1);r?i--:++i<o;)if(e(t[i],i,t))return i;return-1}},3728:function(t,e,n){var r=n(7187),o=n(6846),i=n(9830),a=n(5237),u=n(8887),c=n(8629),l=r?r.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,n,r,l,f,p){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=u;case"[object Set]":var d=1&r;if(h||(h=c),t.size!=e.size&&!d)return!1;var y=p.get(t);if(y)return y==e;r|=2,p.set(t,e);var v=a(h(t),h(e),r,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},3733:function(t,e,n){var r=n(3188),o=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},3771:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}},3803:function(t,e,n){var r=n(6015),o=n(6642),i=n(3572),a=Math.max,u=Math.min;t.exports=function(t,e,n){var c,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function g(e){var n=c,r=l;return c=l=void 0,d=e,f=t.apply(r,n)}function b(t){var n=t-h;return void 0===h||n>=e||n<0||v&&t-d>=s}function x(){var t=o();if(b(t))return w(t);p=setTimeout(x,function(t){var n=e-(t-h);return v?u(n,s-(t-d)):n}(t))}function w(t){return p=void 0,m&&c?g(t):(c=l=void 0,f)}function O(){var t=o(),n=b(t);if(c=arguments,l=this,h=t,n){if(void 0===p)return function(t){return d=t,p=setTimeout(x,e),y?g(t):f}(h);if(v)return clearTimeout(p),p=setTimeout(x,e),g(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,r(n)&&(y=!!n.leading,s=(v="maxWait"in n)?a(i(n.maxWait)||0,e):s,m="trailing"in n?!!n.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},3829:function(t,e,n){var r=n(4360),o=n(7234),i=n(6015),a=n(275),u=/^\[object .+?Constructor\]$/,c=Function.prototype,l=Object.prototype,s=c.toString,f=l.hasOwnProperty,p=RegExp("^"+s.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(r(t)?p:u).test(a(t))}},3851:function(t){t.exports=function(t){return this.__data__.get(t)}},3852:function(t,e,n){var r=n(4679),o=n(5962),i=n(4383),a=n(4683),u=n(1784),c=n(4671);t.exports=function(t,e,n){for(var l=-1,s=(e=r(e,t)).length,f=!1;++l<s;){var p=c(e[l]);if(!(f=null!=t&&n(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},3859:function(t,e,n){var r=n(3237);t.exports=function(t){return r(t)&&t!=+t}},3878:function(t,e,n){"use strict";n.d(e,{G:function(){return U}});var r,o=n(1609),i=n.n(o),a=n(7064),u=n(4227),c=n(4360),l=n.n(c),s=n(7156),f=n.n(s),p=n(2381),h=n.n(p),d=n(3859),y=n.n(d),v=n(8434),m=n.n(v),g=n(8291),b=n(1447),x=n(6799),w=n(5396),O=n(9e3),j=n(186),S=n(9162),A=n(6075),E=["layout","type","stroke","connectNulls","isRange","ref"];function P(t){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function k(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function M(){return M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},M.apply(this,arguments)}function _(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function T(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?_(Object(n),!0).forEach(function(e){L(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function C(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,z(r.key),r)}}function I(t,e,n){return e=N(e),function(t,e){if(e&&("object"===P(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return B(t)}(t,D()?Reflect.construct(e,n||[],N(t).constructor):e.apply(t,n))}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D=function(){return!!t})()}function N(t){return N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},N(t)}function B(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function R(t,e){return R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},R(t,e)}function L(t,e,n){return(e=z(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function z(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=P(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:String(e)}var U=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return L(B(t=I(this,e,[].concat(r))),"state",{isAnimationFinished:!0}),L(B(t),"id",(0,j.NF)("recharts-area-")),L(B(t),"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()}),L(B(t),"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()}),t}var n,r,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&R(t,e)}(e,t),n=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],(r=[{key:"renderDots",value:function(t,n,r){var o=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(o&&!a)return null;var u=this.props,c=u.dot,l=u.points,s=u.dataKey,f=(0,A.J9)(this.props,!1),p=(0,A.J9)(c,!0),h=l.map(function(t,n){var r=T(T(T({key:"dot-".concat(n),r:3},f),p),{},{index:n,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return e.renderDotItem(c,r)}),d={clipPath:t?"url(#clipPath-".concat(n?"":"dots-").concat(r,")"):null};return i().createElement(x.W,M({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,n=e.baseLine,r=e.points,o=e.strokeWidth,a=r[0].x,u=r[r.length-1].x,c=t*Math.abs(a-u),l=f()(r.map(function(t){return t.y||0}));return(0,j.Et)(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(f()(n.map(function(t){return t.y||0})),l)),(0,j.Et)(l)?i().createElement("rect",{x:a<u?a:a-c,y:0,width:c,height:Math.floor(l+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,n=e.baseLine,r=e.points,o=e.strokeWidth,a=r[0].y,u=r[r.length-1].y,c=t*Math.abs(a-u),l=f()(r.map(function(t){return t.x||0}));return(0,j.Et)(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(f()(n.map(function(t){return t.x||0})),l)),(0,j.Et)(l)?i().createElement("rect",{x:0,y:a<u?a:a-c,width:l+(o?parseInt("".concat(o),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,n,r){var o=this.props,a=o.layout,u=o.type,c=o.stroke,l=o.connectNulls,s=o.isRange,f=(o.ref,k(o,E));return i().createElement(x.W,{clipPath:n?"url(#clipPath-".concat(r,")"):null},i().createElement(g.I,M({},(0,A.J9)(f,!0),{points:t,connectNulls:l,type:u,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==c&&i().createElement(g.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:u,connectNulls:l,fill:"none",points:t})),"none"!==c&&s&&i().createElement(g.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:u,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var n=this,r=this.props,o=r.points,a=r.baseLine,c=r.isAnimationActive,l=r.animationBegin,s=r.animationDuration,f=r.animationEasing,p=r.animationId,d=this.state,v=d.prevPoints,m=d.prevBaseLine;return i().createElement(u.Ay,{begin:l,duration:s,isActive:c,easing:f,from:{t:0},to:{t:1},key:"area-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(r){var u=r.t;if(v){var c,l=v.length/o.length,s=o.map(function(t,e){var n=Math.floor(e*l);if(v[n]){var r=v[n],o=(0,j.Dj)(r.x,t.x),i=(0,j.Dj)(r.y,t.y);return T(T({},t),{},{x:o(u),y:i(u)})}return t});return c=(0,j.Et)(a)&&"number"==typeof a?(0,j.Dj)(m,a)(u):h()(a)||y()(a)?(0,j.Dj)(m,0)(u):a.map(function(t,e){var n=Math.floor(e*l);if(m[n]){var r=m[n],o=(0,j.Dj)(r.x,t.x),i=(0,j.Dj)(r.y,t.y);return T(T({},t),{},{x:o(u),y:i(u)})}return t}),n.renderAreaStatically(s,c,t,e)}return i().createElement(x.W,null,i().createElement("defs",null,i().createElement("clipPath",{id:"animationClipPath-".concat(e)},n.renderClipRect(u))),i().createElement(x.W,{clipPath:"url(#animationClipPath-".concat(e,")")},n.renderAreaStatically(o,a,t,e)))})}},{key:"renderArea",value:function(t,e){var n=this.props,r=n.points,o=n.baseLine,i=n.isAnimationActive,a=this.state,u=a.prevPoints,c=a.prevBaseLine,l=a.totalLength;return i&&r&&r.length&&(!u&&l>0||!m()(u,r)||!m()(c,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(r,o,t,e)}},{key:"render",value:function(){var t,e=this.props,n=e.hide,r=e.dot,o=e.points,u=e.className,c=e.top,l=e.left,s=e.xAxis,f=e.yAxis,p=e.width,d=e.height,y=e.isAnimationActive,v=e.id;if(n||!o||!o.length)return null;var m=this.state.isAnimationFinished,g=1===o.length,b=(0,a.A)("recharts-area",u),O=s&&s.allowDataOverflow,j=f&&f.allowDataOverflow,S=O||j,E=h()(v)?this.id:v,P=null!==(t=(0,A.J9)(r,!1))&&void 0!==t?t:{r:3,strokeWidth:2},k=P.r,M=void 0===k?3:k,_=P.strokeWidth,T=void 0===_?2:_,C=((0,A.ON)(r)?r:{}).clipDot,I=void 0===C||C,D=2*M+T;return i().createElement(x.W,{className:b},O||j?i().createElement("defs",null,i().createElement("clipPath",{id:"clipPath-".concat(E)},i().createElement("rect",{x:O?l:l-p/2,y:j?c:c-d/2,width:O?p:2*p,height:j?d:2*d})),!I&&i().createElement("clipPath",{id:"clipPath-dots-".concat(E)},i().createElement("rect",{x:l-D/2,y:c-D/2,width:p+D,height:d+D}))):null,g?null:this.renderArea(S,E),(r||g)&&this.renderDots(S,I,E),(!y||m)&&w.Z.renderCallByParent(this.props,o))}}])&&C(n.prototype,r),o&&C(n,o),Object.defineProperty(n,"prototype",{writable:!1}),e}(o.PureComponent);r=U,L(U,"displayName","Area"),L(U,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!O.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),L(U,"getBaseValue",function(t,e,n,r){var o=t.layout,i=t.baseValue,a=e.props.baseValue,u=null!=a?a:i;if((0,j.Et)(u)&&"number"==typeof u)return u;var c="horizontal"===o?r:n,l=c.scale.domain();if("number"===c.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===u?f:"dataMax"===u||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===u?l[0]:"dataMax"===u?l[1]:l[0]}),L(U,"getComposedData",function(t){var e,n=t.props,o=t.item,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.bandSize,s=t.dataKey,f=t.stackedData,p=t.dataStartIndex,h=t.displayedData,d=t.offset,y=n.layout,v=f&&f.length,m=r.getBaseValue(n,o,i,a),g="horizontal"===y,b=!1,x=h.map(function(t,e){var n;v?n=f[p+e]:(n=(0,S.kr)(t,s),Array.isArray(n)?b=!0:n=[m,n]);var r=null==n[1]||v&&null==(0,S.kr)(t,s);return g?{x:(0,S.nb)({axis:i,ticks:u,bandSize:l,entry:t,index:e}),y:r?null:a.scale(n[1]),value:n,payload:t}:{x:r?null:i.scale(n[1]),y:(0,S.nb)({axis:a,ticks:c,bandSize:l,entry:t,index:e}),value:n,payload:t}});return e=v||b?x.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return g?{x:t.x,y:null!=e&&null!=t.y?a.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}}):g?a.scale(m):i.scale(m),T({points:x,baseLine:e,layout:y,isRange:b},d)}),L(U,"renderDotItem",function(t,e){var n;if(i().isValidElement(t))n=i().cloneElement(t,e);else if(l()(t))n=t(e);else{var r=(0,a.A)("recharts-area-dot","boolean"!=typeof t?t.className:"");n=i().createElement(b.c,M({},e,{className:r}))}return n})},3903:function(t,e,n){var r=n(4235),o=Math.max;t.exports=function(t,e,n){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=n(c),r(t,this,l)}}},3905:function(t,e,n){var r=n(4271),o=n(6143),i=n(3271),a=i&&i.isTypedArray,u=a?o(a):r;t.exports=u},3957:function(t,e,n){var r=n(435);t.exports=function(t){return r(this.__data__,t)>-1}},3967:function(t,e,n){var r=n(789),o=n(8561),i=n(1451);t.exports=function(t,e){var n=this.__data__;if(n instanceof r){var a=n.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++n.size,this;n=this.__data__=new i(a)}return n.set(t,e),this.size=n.size,this}},4003:function(t){t.exports=function(t,e,n){for(var r=-1,o=null==t?0:t.length;++r<o;)if(n(e,t[r]))return!0;return!1}},4157:function(t){var e=Math.ceil,n=Math.max;t.exports=function(t,r,o,i){for(var a=-1,u=n(e((r-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},4183:function(t,e,n){var r=n(6895),o=n(9835)(r);t.exports=o},4204:function(t,e,n){"use strict";n.d(e,{i:function(){return $}});var r=n(1609),o=n.n(r),i=n(4254),a=n.n(i);Math.abs,Math.atan2;const u=Math.cos,c=(Math.max,Math.min,Math.sin),l=Math.sqrt,s=Math.PI,f=2*s;var p={draw(t,e){const n=l(e/s);t.moveTo(n,0),t.arc(0,0,n,0,f)}},h={draw(t,e){const n=l(e/5)/2;t.moveTo(-3*n,-n),t.lineTo(-n,-n),t.lineTo(-n,-3*n),t.lineTo(n,-3*n),t.lineTo(n,-n),t.lineTo(3*n,-n),t.lineTo(3*n,n),t.lineTo(n,n),t.lineTo(n,3*n),t.lineTo(-n,3*n),t.lineTo(-n,n),t.lineTo(-3*n,n),t.closePath()}};const d=l(1/3),y=2*d;var v={draw(t,e){const n=l(e/y),r=n*d;t.moveTo(0,-n),t.lineTo(r,0),t.lineTo(0,n),t.lineTo(-r,0),t.closePath()}},m={draw(t,e){const n=l(e),r=-n/2;t.rect(r,r,n,n)}};const g=c(s/10)/c(7*s/10),b=c(f/10)*g,x=-u(f/10)*g;var w={draw(t,e){const n=l(.8908130915292852*e),r=b*n,o=x*n;t.moveTo(0,-n),t.lineTo(r,o);for(let e=1;e<5;++e){const i=f*e/5,a=u(i),l=c(i);t.lineTo(l*n,-a*n),t.lineTo(a*r-l*o,l*r+a*o)}t.closePath()}};const O=l(3);var j={draw(t,e){const n=-l(e/(3*O));t.moveTo(0,2*n),t.lineTo(-O*n,-n),t.lineTo(O*n,-n),t.closePath()}};const S=-.5,A=l(3)/2,E=1/l(12),P=3*(E/2+1);var k={draw(t,e){const n=l(e/P),r=n/2,o=n*E,i=r,a=n*E+n,u=-i,c=a;t.moveTo(r,o),t.lineTo(i,a),t.lineTo(u,c),t.lineTo(S*r-A*o,A*r+S*o),t.lineTo(S*i-A*a,A*i+S*a),t.lineTo(S*u-A*c,A*u+S*c),t.lineTo(S*r+A*o,S*o-A*r),t.lineTo(S*i+A*a,S*a-A*i),t.lineTo(S*u+A*c,S*c-A*u),t.closePath()}},M=n(9886),_=n(5137);l(3);l(3);var T=n(7064),C=n(6075);function I(t){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},I(t)}var D=["type","size","sizeType"];function N(){return N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},N.apply(this,arguments)}function B(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function R(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?B(Object(n),!0).forEach(function(e){L(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function L(t,e,n){var r;return r=function(t,e){if("object"!=I(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=I(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==I(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function z(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var U={symbolCircle:p,symbolCross:h,symbolDiamond:v,symbolSquare:m,symbolStar:w,symbolTriangle:j,symbolWye:k},F=Math.PI/180,$=function(t){var e,n,r=t.type,i=void 0===r?"circle":r,u=t.size,c=void 0===u?64:u,l=t.sizeType,s=void 0===l?"area":l,f=R(R({},z(t,D)),{},{type:i,size:c,sizeType:s}),h=f.className,d=f.cx,y=f.cy,v=(0,C.J9)(f,!0);return d===+d&&y===+y&&c===+c?o().createElement("path",N({},v,{className:(0,T.A)("recharts-symbols",h),transform:"translate(".concat(d,", ").concat(y,")"),d:(e=function(t){var e="symbol".concat(a()(t));return U[e]||p}(i),n=function(t,e){let n=null,r=(0,_.i)(o);function o(){let o;if(n||(n=o=r()),t.apply(this,arguments).draw(n,+e.apply(this,arguments)),o)return n=null,o+""||null}return t="function"==typeof t?t:(0,M.A)(t||p),e="function"==typeof e?e:(0,M.A)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),o):e},o.context=function(t){return arguments.length?(n=null==t?null:t,o):n},o}().type(e).size(function(t,e,n){if("area"===e)return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var r=18*F;return 1.25*t*t*(Math.tan(r)-Math.tan(2*r)*Math.pow(Math.tan(r),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,s,i)),n())})):null};$.registerSymbol=function(t,e){U["symbol".concat(a()(t))]=e}},4207:function(t,e,n){var r=n(2801),o=n(2208),i=n(9090),a=n(4383),u=n(3681);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):r(t):u(t)}},4214:function(t){t.exports=function(t,e){return t<e}},4227:function(t,e,n){"use strict";n.d(e,{Ay:function(){return It}});var r=n(1609),o=n.n(r),i=n(7598),a=n.n(i),u=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty;function s(t,e){return function(n,r,o){return t(n,r,o)&&e(n,r,o)}}function f(t){return function(e,n,r){if(!e||!n||"object"!=typeof e||"object"!=typeof n)return t(e,n,r);var o=r.cache,i=o.get(e),a=o.get(n);if(i&&a)return i===n&&a===e;o.set(e,n),o.set(n,e);var u=t(e,n,r);return o.delete(e),o.delete(n),u}}function p(t){return u(t).concat(c(t))}var h=Object.hasOwn||function(t,e){return l.call(t,e)};function d(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var y=Object.getOwnPropertyDescriptor,v=Object.keys;function m(t,e,n){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(!n.equals(t[r],e[r],r,r,t,e,n))return!1;return!0}function g(t,e){return d(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function x(t,e){return t===e}function w(t,e,n){var r=t.size;if(r!==e.size)return!1;if(!r)return!0;for(var o,i,a=new Array(r),u=t.entries(),c=0;(o=u.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(n.equals(p[0],h[0],c,f,t,e,n)&&n.equals(p[1],h[1],p[0],h[0],t,e,n)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}var O=d;function j(t,e,n){var r=v(t),o=r.length;if(v(e).length!==o)return!1;for(;o-- >0;)if(!_(t,e,n,r[o]))return!1;return!0}function S(t,e,n){var r,o,i,a=p(t),u=a.length;if(p(e).length!==u)return!1;for(;u-- >0;){if(!_(t,e,n,r=a[u]))return!1;if(o=y(t,r),i=y(e,r),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function A(t,e){return d(t.valueOf(),e.valueOf())}function E(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,n){var r=t.size;if(r!==e.size)return!1;if(!r)return!0;for(var o,i,a=new Array(r),u=t.values();(o=u.next())&&!o.done;){for(var c=e.values(),l=!1,s=0;(i=c.next())&&!i.done;){if(!a[s]&&n.equals(o.value,i.value,o.value,i.value,t,e,n)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function k(t,e){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(t[n]!==e[n])return!1;return!0}function M(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function _(t,e,n,r){return!("_owner"!==r&&"__o"!==r&&"__v"!==r||!t.$$typeof&&!e.$$typeof)||h(e,r)&&n.equals(t[r],e[r],r,r,t,e,n)}var T=Array.isArray,C="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,I=Object.assign,D=Object.prototype.toString.call.bind(Object.prototype.toString);var N=B();B({strict:!0}),B({circular:!0}),B({circular:!0,strict:!0}),B({createInternalComparator:function(){return d}}),B({strict:!0,createInternalComparator:function(){return d}}),B({circular:!0,createInternalComparator:function(){return d}}),B({circular:!0,createInternalComparator:function(){return d},strict:!0});function B(t){void 0===t&&(t={});var e,n=t.circular,r=void 0!==n&&n,o=t.createInternalComparator,i=t.createState,a=t.strict,u=void 0!==a&&a,c=function(t){var e=t.circular,n=t.createCustomConfig,r=t.strict,o={areArraysEqual:r?S:m,areDatesEqual:g,areErrorsEqual:b,areFunctionsEqual:x,areMapsEqual:r?s(w,S):w,areNumbersEqual:O,areObjectsEqual:r?S:j,arePrimitiveWrappersEqual:A,areRegExpsEqual:E,areSetsEqual:r?s(P,S):P,areTypedArraysEqual:r?S:k,areUrlsEqual:M};if(n&&(o=I({},o,n(o))),e){var i=f(o.areArraysEqual),a=f(o.areMapsEqual),u=f(o.areObjectsEqual),c=f(o.areSetsEqual);o=I({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t),l=function(t){var e=t.areArraysEqual,n=t.areDatesEqual,r=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,u=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,d){if(t===h)return!0;if(null==t||null==h)return!1;var y=typeof t;if(y!==typeof h)return!1;if("object"!==y)return"number"===y?a(t,h,d):"function"===y&&o(t,h,d);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return u(t,h,d);if(T(t))return e(t,h,d);if(null!=C&&C(t))return f(t,h,d);if(v===Date)return n(t,h,d);if(v===RegExp)return l(t,h,d);if(v===Map)return i(t,h,d);if(v===Set)return s(t,h,d);var m=D(t);return"[object Date]"===m?n(t,h,d):"[object RegExp]"===m?l(t,h,d):"[object Map]"===m?i(t,h,d):"[object Set]"===m?s(t,h,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof h.then&&u(t,h,d):"[object URL]"===m?p(t,h,d):"[object Error]"===m?r(t,h,d):"[object Arguments]"===m?u(t,h,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&c(t,h,d)}}(c);return function(t){var e=t.circular,n=t.comparator,r=t.createState,o=t.equals,i=t.strict;if(r)return function(t,a){var u=r(),c=u.cache,l=void 0===c?e?new WeakMap:void 0:c,s=u.meta;return n(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return n(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return n(t,e,a)}}({circular:r,comparator:l,createState:i,equals:o?o(l):(e=l,function(t,n,r,o,i,a,u){return e(t,n,u)}),strict:u})}function R(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1;requestAnimationFrame(function r(o){n<0&&(n=o),o-n>e?(t(o),n=-1):function(t){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(t)}(r)})}function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function z(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return U(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return U(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function F(){var t=function(){return null},e=!1,n=function n(r){if(!e){if(Array.isArray(r)){if(!r.length)return;var o=z(r),i=o[0],a=o.slice(1);return"number"==typeof i?void R(n.bind(null,a),i):(n(i),void R(n.bind(null,a)))}"object"===L(r)&&t(r),"function"==typeof r&&r()}};return{stop:function(){e=!0},start:function(t){e=!1,n(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function $(t){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(t)}function W(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function q(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?W(Object(n),!0).forEach(function(e){X(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function X(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==$(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==$(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===$(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var H=function(t){return t},V=function(t,e){return Object.keys(e).reduce(function(n,r){return q(q({},n),{},X({},r,t(r,e[r])))},{})},G=function(t,e,n){return t.map(function(t){return"".concat((r=t,r.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())}))," ").concat(e,"ms ").concat(n);var r}).join(",")};function Y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||J(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(t){return function(t){if(Array.isArray(t))return Z(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||J(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(t,e){if(t){if("string"==typeof t)return Z(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(t,e):void 0}}function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Q=1e-4,tt=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},et=function(t,e){return t.map(function(t,n){return t*Math.pow(e,n)}).reduce(function(t,e){return t+e})},nt=function(t,e){return function(n){var r=tt(t,e);return et(r,n)}},rt=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":r=0,o=0,i=1,a=1;break;case"ease":r=.25,o=.1,i=.25,a=1;break;case"ease-in":r=.42,o=0,i=1,a=1;break;case"ease-out":r=.42,o=0,i=.58,a=1;break;case"ease-in-out":r=0,o=0,i=.58,a=1;break;default:var u=e[0].split("(");if("cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length){var c=Y(u[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}),4);r=c[0],o=c[1],i=c[2],a=c[3]}}[r,i,o,a].every(function(t){return"number"==typeof t&&t>=0&&t<=1});var l,s,f=nt(r,i),p=nt(o,a),h=(l=r,s=i,function(t){var e=tt(l,s),n=[].concat(K(e.map(function(t,e){return t*e}).slice(1)),[0]);return et(n,t)}),d=function(t){return t>1?1:t<0?0:t},y=function(t){for(var e=t>1?1:t,n=e,r=0;r<8;++r){var o=f(n)-e,i=h(n);if(Math.abs(o-e)<Q||i<Q)return p(n);n=d(n-o/i)}return p(n)};return y.isStepper=!1,y},ot=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0];if("string"==typeof r)switch(r){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rt(r);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,n=void 0===e?100:e,r=t.damping,o=void 0===r?8:r,i=t.dt,a=void 0===i?17:i,u=function(t,e,r){var i=r+(-(t-e)*n-r*o)*a/1e3,u=r*a/1e3+t;return Math.abs(u-e)<Q&&Math.abs(i)<Q?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u}();default:if("cubic-bezier"===r.split("(")[0])return rt(r)}return"function"==typeof r?r:null};function it(t){return it="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},it(t)}function at(t){return function(t){if(Array.isArray(t))return pt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ft(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ut(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ct(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ut(Object(n),!0).forEach(function(e){lt(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ut(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function lt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==it(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==it(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===it(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function st(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ft(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(t,e){if(t){if("string"==typeof t)return pt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pt(t,e):void 0}}function pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var ht=function(t,e,n){return t+(e-t)*n},dt=function(t){return t.from!==t.to},yt=function t(e,n,r){var o=V(function(t,n){if(dt(n)){var r=st(e(n.from,n.to,n.velocity),2),o=r[0],i=r[1];return ct(ct({},n),{},{from:o,velocity:i})}return n},n);return r<1?V(function(t,e){return dt(e)?ct(ct({},e),{},{velocity:ht(e.velocity,o[t].velocity,r),from:ht(e.from,o[t].from,r)}):e},n):t(e,o,r-1)},vt=function(t,e,n,r,o){var i,a,u,c,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})),s=l.reduce(function(n,r){return ct(ct({},n),{},lt({},r,[t[r],e[r]]))},{}),f=l.reduce(function(n,r){return ct(ct({},n),{},lt({},r,{from:t[r],velocity:0,to:e[r]}))},{}),p=-1,h=function(){return null};return h=n.isStepper?function(r){u||(u=r);var i=(r-u)/n.dt;f=yt(n,f,i),o(ct(ct(ct({},t),e),V(function(t,e){return e.from},f))),u=r,Object.values(f).filter(dt).length&&(p=requestAnimationFrame(h))}:function(i){c||(c=i);var a=(i-c)/r,u=V(function(t,e){return ht.apply(void 0,at(e).concat([n(a)]))},s);if(o(ct(ct(ct({},t),e),u)),a<1)p=requestAnimationFrame(h);else{var l=V(function(t,e){return ht.apply(void 0,at(e).concat([n(1)]))},s);o(ct(ct(ct({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function mt(t){return mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mt(t)}var gt=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function bt(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function xt(t){return function(t){if(Array.isArray(t))return wt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return wt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Ot(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function jt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ot(Object(n),!0).forEach(function(e){St(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ot(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function St(t,e,n){return(e=Et(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function At(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Et(r.key),r)}}function Et(t){var e=function(t,e){if("object"!==mt(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==mt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===mt(e)?e:String(e)}function Pt(t,e){return Pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Pt(t,e)}function kt(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var n,r=Tt(t);if(e){var o=Tt(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Mt(this,n)}}function Mt(t,e){if(e&&("object"===mt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _t(t)}function _t(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Tt(t){return Tt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Tt(t)}var Ct=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pt(t,e)}(u,t);var e,n,i,a=kt(u);function u(t,e){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);var r=(n=a.call(this,t,e)).props,o=r.isActive,i=r.attributeName,c=r.from,l=r.to,s=r.steps,f=r.children,p=r.duration;if(n.handleStyleChange=n.handleStyleChange.bind(_t(n)),n.changeStyle=n.changeStyle.bind(_t(n)),!o||p<=0)return n.state={style:{}},"function"==typeof f&&(n.state={style:l}),Mt(n);if(s&&s.length)n.state={style:s[0].style};else if(c){if("function"==typeof f)return n.state={style:c},Mt(n);n.state={style:i?St({},i,c):c}}else n.state={style:{}};return n}return e=u,(n=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,n=t.canBegin;this.mounted=!0,e&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,n=e.isActive,r=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(r)if(n){if(!(N(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?u:t.to;if(this.state&&c){var f={style:o?St({},o,s):s};(o&&c[o]!==s||!o&&c!==s)&&this.setState(f)}this.runAnimation(jt(jt({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?St({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,n=t.from,r=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=vt(n,r,ot(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,n=t.steps,r=t.begin,o=t.onAnimationStart,i=n[0],a=i.style,u=i.duration,c=void 0===u?0:u;return this.manager.start([o].concat(xt(n.reduce(function(t,r,o){if(0===o)return t;var i=r.duration,a=r.easing,u=void 0===a?"ease":a,c=r.style,l=r.properties,s=r.onAnimationEnd,f=o>0?n[o-1]:r,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(xt(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=G(p,i,u),d=jt(jt(jt({},f.style),c),{},{transition:h});return[].concat(xt(t),[d,i,s]).filter(H)},[a,Math.max(c,r)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=F());var e=t.begin,n=t.duration,r=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,u=t.onAnimationEnd,c=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!=typeof i&&"function"!=typeof l&&"spring"!==i)if(c.length>1)this.runStepAnimation(t);else{var f=r?St({},r,o):o,p=G(Object.keys(f),n,i);s.start([a,e,jt(jt({},f),{},{transition:p}),n,u])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,e=t.children,n=(t.begin,t.duration),i=(t.attributeName,t.easing,t.isActive),a=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,bt(t,gt)),u=r.Children.count(e),c=this.state.style;if("function"==typeof e)return e(c);if(!i||0===u||n<=0)return e;var l=function(t){var e=t.props,n=e.style,o=void 0===n?{}:n,i=e.className;return(0,r.cloneElement)(t,jt(jt({},a),{},{style:jt(jt({},o),c),className:i}))};return 1===u?l(r.Children.only(e)):o().createElement("div",null,r.Children.map(e,function(t){return l(t)}))}}])&&At(e.prototype,n),i&&At(e,i),Object.defineProperty(e,"prototype",{writable:!1}),u}(r.PureComponent);Ct.displayName="Animate",Ct.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},Ct.propTypes={from:a().oneOfType([a().object,a().string]),to:a().oneOfType([a().object,a().string]),attributeName:a().string,duration:a().number,begin:a().number,easing:a().oneOfType([a().string,a().func]),steps:a().arrayOf(a().shape({duration:a().number.isRequired,style:a().object.isRequired,easing:a().oneOfType([a().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),a().func]),properties:a().arrayOf("string"),onAnimationEnd:a().func})),children:a().oneOfType([a().node,a().func]),isActive:a().bool,canBegin:a().bool,onAnimationEnd:a().func,shouldReAnimate:a().bool,onAnimationStart:a().func,onAnimationReStart:a().func};var It=Ct},4235:function(t){t.exports=function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}},4254:function(t,e,n){var r=n(6937)("toUpperCase");t.exports=r},4271:function(t,e,n){var r=n(6990),o=n(1784),i=n(6184),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[r(t)]}},4287:function(t,e,n){var r=n(1960),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return r?void 0!==e[t]:o.call(e,t)}},4321:function(t,e,n){var r=n(3536);t.exports=function(t,e,n){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!r(u):n(u,c)))var c=u,l=a}return l}},4360:function(t,e,n){var r=n(6990),o=n(6015);t.exports=function(t){if(!o(t))return!1;var e=r(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},4383:function(t){var e=Array.isArray;t.exports=e},4505:function(t,e,n){"use strict";n.d(e,{m:function(){return J}});var r=n(1609),o=n.n(r),i=n(7493),a=n.n(i),u=n(2381),c=n.n(u),l=n(7064),s=n(186);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},p.apply(this,arguments)}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function v(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach(function(e){m(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function m(t,e,n){var r;return r=function(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==f(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function g(t){return Array.isArray(t)&&(0,s.vh)(t[0])&&(0,s.vh)(t[1])?t.join(" ~ "):t}var b=function(t){var e=t.separator,n=void 0===e?" : ":e,r=t.contentStyle,i=void 0===r?{}:r,u=t.itemStyle,f=void 0===u?{}:u,d=t.labelStyle,y=void 0===d?{}:d,m=t.payload,b=t.formatter,x=t.itemSorter,w=t.wrapperClassName,O=t.labelClassName,j=t.label,S=t.labelFormatter,A=t.accessibilityLayer,E=void 0!==A&&A,P=v({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),k=v({margin:0},y),M=!c()(j),_=M?j:"",T=(0,l.A)("recharts-default-tooltip",w),C=(0,l.A)("recharts-tooltip-label",O);M&&S&&null!=m&&(_=S(j,m));var I=E?{role:"status","aria-live":"assertive"}:{};return o().createElement("div",p({className:T,style:P},I),o().createElement("p",{className:C,style:k},o().isValidElement(_)?_:"".concat(_)),function(){if(m&&m.length){var t=(x?a()(m,x):m).map(function(t,e){if("none"===t.type)return null;var r=v({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},f),i=t.formatter||b||g,a=t.value,u=t.name,c=a,l=u;if(i&&null!=c&&null!=l){var p=i(a,u,t,e,m);if(Array.isArray(p)){var d=h(p,2);c=d[0],l=d[1]}else c=p}return o().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:r},(0,s.vh)(l)?o().createElement("span",{className:"recharts-tooltip-item-name"},l):null,(0,s.vh)(l)?o().createElement("span",{className:"recharts-tooltip-item-separator"},n):null,o().createElement("span",{className:"recharts-tooltip-item-value"},c),o().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return o().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function w(t,e,n){var r;return r=function(t,e){if("object"!=x(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=x(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==x(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var O="recharts-tooltip-wrapper",j={visibility:"hidden"};function S(t){var e=t.coordinate,n=t.translateX,r=t.translateY;return(0,l.A)(O,w(w(w(w({},"".concat(O,"-right"),(0,s.Et)(n)&&e&&(0,s.Et)(e.x)&&n>=e.x),"".concat(O,"-left"),(0,s.Et)(n)&&e&&(0,s.Et)(e.x)&&n<e.x),"".concat(O,"-bottom"),(0,s.Et)(r)&&e&&(0,s.Et)(e.y)&&r>=e.y),"".concat(O,"-top"),(0,s.Et)(r)&&e&&(0,s.Et)(e.y)&&r<e.y))}function A(t){var e=t.allowEscapeViewBox,n=t.coordinate,r=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(i&&(0,s.Et)(i[r]))return i[r];var f=n[r]-u-o,p=n[r]+o;return e[r]?a[r]?f:p:a[r]?f<c[r]?Math.max(p,c[r]):Math.max(f,c[r]):p+u>c[r]+l?Math.max(f,c[r]):Math.max(p,c[r])}function E(t){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(t)}function P(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function k(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?P(Object(n),!0).forEach(function(e){N(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function M(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,B(r.key),r)}}function _(t,e,n){return e=C(e),function(t,e){if(e&&("object"===E(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return I(t)}(t,T()?Reflect.construct(e,n||[],C(t).constructor):e.apply(t,n))}function T(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(T=function(){return!!t})()}function C(t){return C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},C(t)}function I(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function D(t,e){return D=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},D(t,e)}function N(t,e,n){return(e=B(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:String(e)}var R=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return N(I(t=_(this,e,[].concat(r))),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),N(I(t),"handleKeyDown",function(e){var n,r,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(n=null===(r=t.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==n?n:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}),t}var n,r,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&D(t,e)}(e,t),n=e,(r=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,n=e.active,r=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.children,c=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,d=e.useTranslate3d,y=e.viewBox,v=e.wrapperStyle,m=function(t){var e,n,r=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,u=t.reverseDirection,c=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:c.height>0&&c.width>0&&o?function(t){var e=t.translateX,n=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(n,"px, 0)"):"translate(".concat(e,"px, ").concat(n,"px)")}}({translateX:e=A({allowEscapeViewBox:r,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.width,viewBox:s,viewBoxDimension:s.width}),translateY:n=A({allowEscapeViewBox:r,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):j,cssClasses:S({translateX:e,translateY:n,coordinate:o})}}({allowEscapeViewBox:r,coordinate:c,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:y}),g=m.cssClasses,b=m.cssProperties,x=k(k({transition:s&&n?"transform ".concat(i,"ms ").concat(a):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&n&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return o().createElement("div",{tabIndex:-1,className:g,style:x,ref:function(e){t.wrapperNode=e}},u)}}])&&M(n.prototype,r),i&&M(n,i),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.PureComponent),L=n(9e3),z=n(2157);function U(t){return U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},U(t)}function F(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function $(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?F(Object(n),!0).forEach(function(e){G(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function W(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Y(r.key),r)}}function q(t,e,n){return e=H(e),function(t,e){if(e&&("object"===U(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,X()?Reflect.construct(e,n||[],H(t).constructor):e.apply(t,n))}function X(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(X=function(){return!!t})()}function H(t){return H=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},H(t)}function V(t,e){return V=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},V(t,e)}function G(t,e,n){return(e=Y(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Y(t){var e=function(t,e){if("object"!=U(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=U(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==U(e)?e:String(e)}function K(t){return t.dataKey}var J=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),q(this,e,arguments)}var n,r,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&V(t,e)}(e,t),n=e,(r=[{key:"render",value:function(){var t=this,e=this.props,n=e.active,r=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.content,c=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,d=e.position,y=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,g=e.wrapperStyle,x=null!=p?p:[];l&&x.length&&(x=(0,z.s)(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,K));var w=x.length>0;return o().createElement(R,{allowEscapeViewBox:r,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:c,hasPayload:w,offset:f,position:d,reverseDirection:y,useTranslate3d:v,viewBox:m,wrapperStyle:g},function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):"function"==typeof t?o().createElement(t,e):o().createElement(b,e)}(u,$($({},this.props),{},{payload:x})))}}])&&W(n.prototype,r),i&&W(n,i),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.PureComponent);G(J,"displayName","Tooltip"),G(J,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!L.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},4506:function(t,e,n){var r=n(4157),o=n(6218),i=n(5410);t.exports=function(t){return function(e,n,a){return a&&"number"!=typeof a&&o(e,n,a)&&(n=a=void 0),e=i(e),void 0===n?(n=e,e=0):n=i(n),a=void 0===a?e<n?1:-1:i(a),r(e,n,a,t)}}},4671:function(t,e,n){var r=n(3536);t.exports=function(t){if("string"==typeof t||r(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},4679:function(t,e,n){var r=n(4383),o=n(9668),i=n(412),a=n(2832);t.exports=function(t,e){return r(t)?t:o(t,e)?[t]:i(a(t))}},4683:function(t){var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,n){var r=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&e.test(t))&&t>-1&&t%1==0&&t<n}},4742:function(t,e,n){var r=n(7187),o=n(6138),i=n(4383),a=n(3536),u=r?r.prototype:void 0,c=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return c?c.call(e):"";var n=e+"";return"0"==n&&1/e==-1/0?"-0":n}},4873:function(t){t.exports=function(t,e){return t>e}},4953:function(t,e,n){var r=n(2748);t.exports=function(t){return function(e){return r(e,t)}}},5084:function(t,e,n){"use strict";n.d(e,{A:function(){return i},z:function(){return u}});var r=n(2261),o=n(6434);function i(){var t,e,n=(0,o.A)().unknown(void 0),a=n.domain,u=n.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var n=a().length,r=l<c,o=r?l:c,i=r?c:l;t=(i-o)/Math.max(1,n-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(n-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=function(t,e,n){t=+t,e=+e,n=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+n;for(var r=-1,o=0|Math.max(0,Math.ceil((e-t)/n)),i=new Array(o);++r<o;)i[r]=t+r*n;return i}(n).map(function(e){return o+t*e});return u(r?d.reverse():d)}return delete n.unknown,n.domain=function(t){return arguments.length?(a(t),d()):a()},n.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,d()):[c,l]},n.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,d()},n.bandwidth=function(){return e},n.step=function(){return t},n.round=function(t){return arguments.length?(s=!!t,d()):s},n.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},n.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},n.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},n.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},n.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},r.C.apply(d(),arguments)}function a(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return a(e())},t}function u(){return a(i.apply(null,arguments).paddingInner(1))}},5137:function(t,e,n){"use strict";n.d(e,{i:function(){return l}});const r=Math.PI,o=2*r,i=1e-6,a=o-i;function u(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}class c{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?u:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return u;const n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,o,i){this._append`C${+t},${+e},${+n},${+r},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,n,o,a){if(t=+t,e=+e,n=+n,o=+o,(a=+a)<0)throw new Error(`negative radius: ${a}`);let u=this._x1,c=this._y1,l=n-t,s=o-e,f=u-t,p=c-e,h=f*f+p*p;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>i)if(Math.abs(p*l-s*f)>i&&a){let d=n-u,y=o-c,v=l*l+s*s,m=d*d+y*y,g=Math.sqrt(v),b=Math.sqrt(h),x=a*Math.tan((r-Math.acos((v+h-m)/(2*g*b)))/2),w=x/b,O=x/g;Math.abs(w-1)>i&&this._append`L${t+w*f},${e+w*p}`,this._append`A${a},${a},0,0,${+(p*d>f*y)},${this._x1=t+O*l},${this._y1=e+O*s}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,n,u,c,l){if(t=+t,e=+e,l=!!l,(n=+n)<0)throw new Error(`negative radius: ${n}`);let s=n*Math.cos(u),f=n*Math.sin(u),p=t+s,h=e+f,d=1^l,y=l?u-c:c-u;null===this._x1?this._append`M${p},${h}`:(Math.abs(this._x1-p)>i||Math.abs(this._y1-h)>i)&&this._append`L${p},${h}`,n&&(y<0&&(y=y%o+o),y>a?this._append`A${n},${n},0,1,${d},${t-s},${e-f}A${n},${n},0,1,${d},${this._x1=p},${this._y1=h}`:y>i&&this._append`A${n},${n},0,${+(y>=r)},${d},${this._x1=t+n*Math.cos(c)},${this._y1=e+n*Math.sin(c)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function l(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(null==n)e=null;else{const t=Math.floor(n);if(!(t>=0))throw new RangeError(`invalid digits: ${n}`);e=t}return t},()=>new c(e)}c.prototype},5169:function(t,e,n){var r;!function(){"use strict";var o,i=1e9,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=1e7,y=9007199254740991,v=f(1286742750677284.5),m={};function g(t,e){var n,r,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?k(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(r=l,i=-i,c=s.length):(r=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,r=s,s=l,l=r),n=0;i;)n=(l[--i]=l[i]+s[i]+n)/d|0,l[i]%=d;for(n&&(l.unshift(n),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?k(e,p):e}function b(t,e,n){if(t!==~~t||t<e||t>n)throw Error(l+t)}function x(t){var e,n,r,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(n=7-(r=t[e]+"").length)&&(i+=A(n)),i+=r;(n=7-(r=(a=t[e])+"").length)&&(i+=A(n))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}m.absoluteValue=m.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},m.comparedTo=m.cmp=function(t){var e,n,r,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,n=(r=i.d.length)<(o=t.d.length)?r:o;e<n;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return r===o?0:r>o^i.s<0?1:-1},m.decimalPlaces=m.dp=function(){var t=this,e=t.d.length-1,n=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)n--;return n<0?0:n},m.dividedBy=m.div=function(t){return w(this,new this.constructor(t))},m.dividedToIntegerBy=m.idiv=function(t){var e=this.constructor;return k(w(this,new e(t),0,1),e.precision)},m.equals=m.eq=function(t){return!this.cmp(t)},m.exponent=function(){return j(this)},m.greaterThan=m.gt=function(t){return this.cmp(t)>0},m.greaterThanOrEqualTo=m.gte=function(t){return this.cmp(t)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(t){return this.cmp(t)<0},m.lessThanOrEqualTo=m.lte=function(t){return this.cmp(t)<1},m.logarithm=m.log=function(t){var e,n=this,r=n.constructor,i=r.precision,a=i+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(o))throw Error(c+"NaN");if(n.s<1)throw Error(c+(n.s?"NaN":"-Infinity"));return n.eq(o)?new r(0):(u=!1,e=w(E(n,a),E(t,a),a),u=!0,k(e,i))},m.minus=m.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?M(e,t):g(e,(t.s=-t.s,t))},m.modulo=m.mod=function(t){var e,n=this,r=n.constructor,o=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return n.s?(u=!1,e=w(n,t,0,1).times(t),u=!0,n.minus(e)):k(new r(n),o)},m.naturalExponential=m.exp=function(){return O(this)},m.naturalLogarithm=m.ln=function(){return E(this)},m.negated=m.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},m.plus=m.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?g(e,t):M(e,(t.s=-t.s,t))},m.precision=m.sd=function(t){var e,n,r,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(l+t);if(e=j(o)+1,n=7*(r=o.d.length-1)+1,r=o.d[r]){for(;r%10==0;r/=10)n--;for(r=o.d[0];r>=10;r/=10)n++}return t&&e>n?e:n},m.squareRoot=m.sqrt=function(){var t,e,n,r,o,i,a,l=this,s=l.constructor;if(l.s<1){if(!l.s)return new s(0);throw Error(c+"NaN")}for(t=j(l),u=!1,0==(o=Math.sqrt(+l))||o==1/0?(((e=x(l.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),r=new s(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):r=new s(o.toString()),o=a=(n=s.precision)+3;;)if(r=(i=r).plus(w(l,i,a+2)).times(.5),x(i.d).slice(0,a)===(e=x(r.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(k(i,n+1,0),i.times(i).eq(l)){r=i;break}}else if("9999"!=e)break;a+=4}return u=!0,k(r,n)},m.times=m.mul=function(t){var e,n,r,o,i,a,c,l,s,f=this,p=f.constructor,h=f.d,y=(t=new p(t)).d;if(!f.s||!t.s)return new p(0);for(t.s*=f.s,n=f.e+t.e,(l=h.length)<(s=y.length)&&(i=h,h=y,y=i,a=l,l=s,s=a),i=[],r=a=l+s;r--;)i.push(0);for(r=s;--r>=0;){for(e=0,o=l+r;o>r;)c=i[o]+y[r]*h[o-r-1]+e,i[o--]=c%d|0,e=c/d|0;i[o]=(i[o]+e)%d|0}for(;!i[--a];)i.pop();return e?++n:i.shift(),t.d=i,t.e=n,u?k(t,p.precision):t},m.toDecimalPlaces=m.todp=function(t,e){var n=this,r=n.constructor;return n=new r(n),void 0===t?n:(b(t,0,i),void 0===e?e=r.rounding:b(e,0,8),k(n,t+j(n)+1,e))},m.toExponential=function(t,e){var n,r=this,o=r.constructor;return void 0===t?n=_(r,!0):(b(t,0,i),void 0===e?e=o.rounding:b(e,0,8),n=_(r=k(new o(r),t+1,e),!0,t+1)),n},m.toFixed=function(t,e){var n,r,o=this,a=o.constructor;return void 0===t?_(o):(b(t,0,i),void 0===e?e=a.rounding:b(e,0,8),n=_((r=k(new a(o),t+j(o)+1,e)).abs(),!1,t+j(r)+1),o.isneg()&&!o.isZero()?"-"+n:n)},m.toInteger=m.toint=function(){var t=this,e=t.constructor;return k(new e(t),j(t)+1,e.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(t){var e,n,r,i,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(o);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(o))return s;if(r=p.precision,t.eq(o))return k(s,r);if(l=(e=t.e)>=(n=t.d.length-1),a=s.s,l){if((n=h<0?-h:h)<=y){for(i=new p(o),e=Math.ceil(r/7+4),u=!1;n%2&&T((i=i.times(s)).d,e),0!==(n=f(n/2));)T((s=s.times(s)).d,e);return u=!0,t.s<0?new p(o).div(i):k(i,r)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,n)]?-1:1,s.s=1,u=!1,i=t.times(E(s,r+12)),u=!0,(i=O(i)).s=a,i},m.toPrecision=function(t,e){var n,r,o=this,a=o.constructor;return void 0===t?r=_(o,(n=j(o))<=a.toExpNeg||n>=a.toExpPos):(b(t,1,i),void 0===e?e=a.rounding:b(e,0,8),r=_(o=k(new a(o),t,e),t<=(n=j(o))||n<=a.toExpNeg,t)),r},m.toSignificantDigits=m.tosd=function(t,e){var n=this.constructor;return void 0===t?(t=n.precision,e=n.rounding):(b(t,1,i),void 0===e?e=n.rounding:b(e,0,8)),k(new n(this),t,e)},m.toString=m.valueOf=m.val=m.toJSON=function(){var t=this,e=j(t),n=t.constructor;return _(t,e<=n.toExpNeg||e>=n.toExpPos)};var w=function(){function t(t,e){var n,r=0,o=t.length;for(t=t.slice();o--;)n=t[o]*e+r,t[o]=n%d|0,r=n/d|0;return r&&t.unshift(r),t}function e(t,e,n,r){var o,i;if(n!=r)i=n>r?1:-1;else for(o=i=0;o<n;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function n(t,e,n){for(var r=0;n--;)t[n]-=r,r=t[n]<e[n]?1:0,t[n]=r*d+t[n]-e[n];for(;!t[0]&&t.length>1;)t.shift()}return function(r,o,i,a){var u,l,s,f,p,h,y,v,m,g,b,x,w,O,S,A,E,P,M=r.constructor,_=r.s==o.s?1:-1,T=r.d,C=o.d;if(!r.s)return new M(r);if(!o.s)throw Error(c+"Division by zero");for(l=r.e-o.e,E=C.length,S=T.length,v=(y=new M(_)).d=[],s=0;C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--l,(x=null==i?i=M.precision:a?i+(j(r)-j(o))+1:i)<0)return new M(0);if(x=x/7+2|0,s=0,1==E)for(f=0,C=C[0],x++;(s<S||f)&&x--;s++)w=f*d+(T[s]||0),v[s]=w/C|0,f=w%C|0;else{for((f=d/(C[0]+1)|0)>1&&(C=t(C,f),T=t(T,f),E=C.length,S=T.length),O=E,g=(m=T.slice(0,E)).length;g<E;)m[g++]=0;(P=C.slice()).unshift(0),A=C[0],C[1]>=d/2&&++A;do{f=0,(u=e(C,m,E,g))<0?(b=m[0],E!=g&&(b=b*d+(m[1]||0)),(f=b/A|0)>1?(f>=d&&(f=d-1),1==(u=e(p=t(C,f),m,h=p.length,g=m.length))&&(f--,n(p,E<h?P:C,h))):(0==f&&(u=f=1),p=C.slice()),(h=p.length)<g&&p.unshift(0),n(m,p,g),-1==u&&(u=e(C,m,E,g=m.length))<1&&(f++,n(m,E<g?P:C,g)),g=m.length):0===u&&(f++,m=[0]),v[s++]=f,u&&m[0]?m[g++]=T[O]||0:(m=[T[O]],g=1)}while((O++<S||void 0!==m[0])&&x--)}return v[0]||v.shift(),y.e=l,k(y,a?i+j(y)+1:i)}}();function O(t,e){var n,r,i,a,c,l=0,f=0,h=t.constructor,d=h.precision;if(j(t)>16)throw Error(s+j(t));if(!t.s)return new h(o);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,n=r=i=new h(o),h.precision=c;;){if(r=k(r.times(t),c),n=n.times(++l),x((a=i.plus(w(r,n,c))).d).slice(0,c)===x(i.d).slice(0,c)){for(;f--;)i=k(i.times(i),c);return h.precision=d,null==e?(u=!0,k(i,d)):i}i=a}}function j(t){for(var e=7*t.e,n=t.d[0];n>=10;n/=10)e++;return e}function S(t,e,n){if(e>t.LN10.sd())throw u=!0,n&&(t.precision=n),Error(c+"LN10 precision limit exceeded");return k(new t(t.LN10),e)}function A(t){for(var e="";t--;)e+="0";return e}function E(t,e){var n,r,i,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,g=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(u=!1,p=g):p=e,y.eq(10))return null==e&&(u=!0),S(m,p);if(p+=10,m.precision=p,r=(n=x(v)).charAt(0),a=j(y),!(Math.abs(a)<15e14))return f=S(m,p+2,g).times(a+""),y=E(new m(r+"."+n.slice(1)),p-10).plus(f),m.precision=g,null==e?(u=!0,k(y,g)):y;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=x((y=y.times(t)).d)).charAt(0),d++;for(a=j(y),r>1?(y=new m("0."+n),a++):y=new m(r+"."+n.slice(1)),s=l=y=w(y.minus(o),y.plus(o),p),h=k(y.times(y),p),i=3;;){if(l=k(l.times(h),p),x((f=s.plus(w(l,new m(i),p))).d).slice(0,p)===x(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(S(m,p+2,g).times(a+""))),s=w(s,new m(d),p),m.precision=g,null==e?(u=!0,k(s,g)):s;s=f,i+=2}}function P(t,e){var n,r,o;for((n=e.indexOf("."))>-1&&(e=e.replace(".","")),(r=e.search(/e/i))>0?(n<0&&(n=r),n+=+e.slice(r+1),e=e.substring(0,r)):n<0&&(n=e.length),r=0;48===e.charCodeAt(r);)++r;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(r,o)){if(o-=r,n=n-r-1,t.e=f(n/7),t.d=[],r=(n+1)%7,n<0&&(r+=7),r<o){for(r&&t.d.push(+e.slice(0,r)),o-=7;r<o;)t.d.push(+e.slice(r,r+=7));r=7-(e=e.slice(r)).length}else r-=o;for(;r--;)e+="0";if(t.d.push(+e),u&&(t.e>v||t.e<-v))throw Error(s+n)}else t.s=0,t.e=0,t.d=[0];return t}function k(t,e,n){var r,o,i,a,c,l,h,y,m=t.d;for(a=1,i=m[0];i>=10;i/=10)a++;if((r=e-a)<0)r+=7,o=e,h=m[y=0];else{if((y=Math.ceil((r+1)/7))>=(i=m.length))return t;for(h=i=m[y],a=1;i>=10;i/=10)a++;o=(r%=7)-7+a}if(void 0!==n&&(c=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==m[y+1]||h%i,l=n<4?(c||l)&&(0==n||n==(t.s<0?3:2)):c>5||5==c&&(4==n||l||6==n&&(r>0?o>0?h/p(10,a-o):0:m[y-1])%10&1||n==(t.s<0?8:7))),e<1||!m[0])return l?(i=j(t),m.length=1,e=e-i-1,m[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(m.length=1,m[0]=t.e=t.s=0),t;if(0==r?(m.length=y,i=1,y--):(m.length=y+1,i=p(10,7-r),m[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){(m[0]+=i)==d&&(m[0]=1,++t.e);break}if(m[y]+=i,m[y]!=d)break;m[y--]=0,i=1}for(r=m.length;0===m[--r];)m.pop();if(u&&(t.e>v||t.e<-v))throw Error(s+j(t));return t}function M(t,e){var n,r,o,i,a,c,l,s,f,p,h=t.constructor,y=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?k(e,y):e;if(l=t.d,p=e.d,r=e.e,s=t.e,l=l.slice(),a=s-r){for((f=a<0)?(n=l,a=-a,c=p.length):(n=p,r=s,c=l.length),a>(o=Math.max(Math.ceil(y/7),c)+2)&&(a=o,n.length=1),n.reverse(),o=a;o--;)n.push(0);n.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(n=l,l=p,p=n,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=d-1;--l[i],l[o]+=d}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--r;return l[0]?(e.d=l,e.e=r,u?k(e,y):e):new h(0)}function _(t,e,n){var r,o=j(t),i=x(t.d),a=i.length;return e?(n&&(r=n-a)>0?i=i.charAt(0)+"."+i.slice(1)+A(r):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+A(-o-1)+i,n&&(r=n-a)>0&&(i+=A(r))):o>=a?(i+=A(o+1-a),n&&(r=n-o-1)>0&&(i=i+"."+A(r))):((r=o+1)<a&&(i=i.slice(0,r)+"."+i.slice(r)),n&&(r=n-a)>0&&(o+1===a&&(i+="."),i+=A(r))),t.s<0?"-"+i:i}function T(t,e){if(t.length>e)return t.length=e,!0}function C(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,n,r,o=["precision",1,i,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(r=t[n=o[e]])){if(!(f(r)===r&&r>=o[e+1]&&r<=o[e+2]))throw Error(l+n+": "+r);this[n]=r}if(void 0!==(r=t[n="LN10"])){if(r!=Math.LN10)throw Error(l+n+": "+r);this[n]=new this(r)}return this}a=function t(e){var n,r,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):P(e,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!h.test(t))throw Error(l+t);P(e,t)}if(i.prototype=m,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=C,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],n=0;n<o.length;)e.hasOwnProperty(r=o[n++])||(e[r]=this[r]);return i.config(e),i}(a),a.default=a.Decimal=a,o=new a(1),void 0===(r=function(){return a}.call(e,n,e,t))||(t.exports=r)}()},5171:function(t,e,n){var r=n(7183)["__core-js_shared__"];t.exports=r},5178:function(t,e,n){var r=n(3188)(n(7183),"DataView");t.exports=r},5194:function(t,e,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;t.exports=r},5218:function(t,e,n){var r=n(4183),o=n(9592);t.exports=function(t,e){var n=-1,i=o(t)?Array(t.length):[];return r(t,function(t,r,o){i[++n]=e(t,r,o)}),i}},5229:function(t,e,n){var r=n(6990),o=n(9637),i=n(6184),a=Function.prototype,u=Object.prototype,c=a.toString,l=u.hasOwnProperty,s=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=r(t))return!1;var e=o(t);if(null===e)return!0;var n=l.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&c.call(n)==s}},5237:function(t,e,n){var r=n(8869),o=n(7938),i=n(7773);t.exports=function(t,e,n,a,u,c){var l=1&n,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),h=c.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&n?new r:void 0;for(c.set(t,e),c.set(e,t);++d<s;){var m=t[d],g=e[d];if(a)var b=l?a(g,m,d,e,t,c):a(m,g,d,t,e,c);if(void 0!==b){if(b)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,n,a,c)))return v.push(e)})){y=!1;break}}else if(m!==g&&!u(m,g,n,a,c)){y=!1;break}}return c.delete(t),c.delete(e),y}},5351:function(t){t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},5355:function(t,e,n){var r=n(3300),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,i,a,u){var c=1&n,l=r(t),s=l.length;if(s!=r(e).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in e:o.call(e,p)))return!1}var h=u.get(t),d=u.get(e);if(h&&d)return h==e&&d==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<s;){var m=t[p=l[f]],g=e[p];if(i)var b=c?i(g,m,p,e,t,u):i(m,g,p,t,e,u);if(!(void 0===b?m===g||a(m,g,n,i,u):b)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w||(y=!1)}return u.delete(t),u.delete(e),y}},5369:function(t){var e=Date.now;t.exports=function(t){var n=0,r=0;return function(){var o=e(),i=16-(o-r);if(r=o,i>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(void 0,arguments)}}},5396:function(t,e,n){"use strict";n.d(e,{Z:function(){return k}});var r=n(1609),o=n.n(r),i=n(2381),a=n.n(i),u=n(6015),c=n.n(u),l=n(4360),s=n.n(l),f=n(9804),p=n.n(f),h=n(7724),d=n(6799),y=n(6075),v=n(9162);function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}var g=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function x(t){return function(t){if(Array.isArray(t))return w(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return w(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},O.apply(this,arguments)}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function S(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach(function(e){A(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function A(t,e,n){var r;return r=function(t,e){if("object"!=m(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=m(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function E(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var P=function(t){return Array.isArray(t.value)?p()(t.value):t.value};function k(t){var e=t.valueAccessor,n=void 0===e?P:e,r=E(t,g),i=r.data,u=r.dataKey,c=r.clockWise,l=r.id,s=r.textBreakAll,f=E(r,b);return i&&i.length?o().createElement(d.W,{className:"recharts-label-list"},i.map(function(t,e){var r=a()(u)?n(t,e):(0,v.kr)(t&&t.payload,u),i=a()(l)?{}:{id:"".concat(l,"-").concat(e)};return o().createElement(h.J,O({},(0,y.J9)(t,!0),f,i,{parentViewBox:t.parentViewBox,value:r,textBreakAll:s,viewBox:h.J.parseViewBox(a()(c)?t:S(S({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}k.displayName="LabelList",k.renderCallByParent=function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=(0,y.aS)(i,k).map(function(t,n){return(0,r.cloneElement)(t,{data:e,key:"labelList-".concat(n)})});return n?[function(t,e){return t?!0===t?o().createElement(k,{key:"labelList-implicit",data:e}):o().isValidElement(t)||s()(t)?o().createElement(k,{key:"labelList-implicit",data:e,content:t}):c()(t)?o().createElement(k,O({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,e)].concat(x(a)):a}},5410:function(t,e,n){var r=n(3572),o=1/0;t.exports=function(t){return t?(t=r(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}},5447:function(t,e,n){var r=n(3705),o=n(4207),i=n(9147),a=Math.max;t.exports=function(t,e,n){var u=null==t?0:t.length;if(!u)return-1;var c=null==n?0:i(n);return c<0&&(c=a(u+c,0)),r(t,o(e,3),c)}},5564:function(t){var e="\\ud800-\\udfff",n="["+e+"]",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",u="[\\ud800-\\udbff][\\udc00-\\udfff]",c="(?:"+r+"|"+o+")"+"?",l="[\\ufe0e\\ufe0f]?",s=l+c+("(?:\\u200d(?:"+[i,a,u].join("|")+")"+l+c+")*"),f="(?:"+[i+r+"?",r,a,u,n].join("|")+")",p=RegExp(o+"(?="+o+")|"+f+s,"g");t.exports=function(t){return t.match(p)||[]}},5580:function(t,e,n){var r=n(9090),o=n(3903),i=n(2139);t.exports=function(t,e){return i(o(t,e,r),t+"")}},5674:function(t,e,n){var r=n(3615),o=n(789),i=n(8561);t.exports=function(){this.size=0,this.__data__={hash:new r,map:new(i||o),string:new r}}},5706:function(t,e,n){"use strict";n.d(e,{f:function(){return y}});var r=n(4360),o=n.n(r),i=n(186),a=n(8742),u=n(9e3),c=n(2804);function l(t,e,n){if(e<1)return[];if(1===e&&void 0===n)return t;for(var r=[],o=0;o<t.length;o+=e){if(void 0!==n&&!0!==n(t[o]))return;r.push(t[o])}return r}function s(t,e,n,r,o){if(t*e<t*r||t*e>t*o)return!1;var i=n();return t*(e-t*i/2-r)>=0&&t*(e+t*i/2-o)<=0}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach(function(e){d(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function d(t,e,n){var r;return r=function(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==f(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function y(t,e,n){var r=t.tick,f=t.ticks,p=t.viewBox,d=t.minTickGap,y=t.orientation,v=t.interval,m=t.tickFormatter,g=t.unit,b=t.angle;if(!f||!f.length||!r)return[];if((0,i.Et)(v)||u.m.isSsr)return function(t,e){return l(t,e+1)}(f,"number"==typeof v&&(0,i.Et)(v)?v:0);var x=[],w="top"===y||"bottom"===y?"width":"height",O=g&&"width"===w?(0,a.Pu)(g,{fontSize:e,letterSpacing:n}):{width:0,height:0},j=function(t,r){var i=o()(m)?m(t.value,r):t.value;return"width"===w?function(t,e,n){var r={width:t.width+e.width,height:t.height+e.height};return(0,c.bx)(r,n)}((0,a.Pu)(i,{fontSize:e,letterSpacing:n}),O,b):(0,a.Pu)(i,{fontSize:e,letterSpacing:n})[w]},S=f.length>=2?(0,i.sA)(f[1].coordinate-f[0].coordinate):1,A=function(t,e,n){var r="width"===n,o=t.x,i=t.y,a=t.width,u=t.height;return 1===e?{start:r?o:i,end:r?o+a:i+u}:{start:r?o+a:i+u,end:r?o:i}}(p,S,w);return"equidistantPreserveStart"===v?function(t,e,n,r,o){for(var i,a=(r||[]).slice(),u=e.start,c=e.end,f=0,p=1,h=u,d=function(){var e=null==r?void 0:r[f];if(void 0===e)return{v:l(r,p)};var i,a=f,d=function(){return void 0===i&&(i=n(e,a)),i},y=e.coordinate,v=0===f||s(t,y,d,h,c);v||(f=0,h=u,p+=1),v&&(h=y+t*(d()/2+o),f+=p)};p<=a.length;)if(i=d())return i.v;return[]}(S,A,j,f,d):(x="preserveStart"===v||"preserveStartEnd"===v?function(t,e,n,r,o,i){var a=(r||[]).slice(),u=a.length,c=e.start,l=e.end;if(i){var f=r[u-1],p=n(f,u-1),d=t*(f.coordinate+t*p/2-l);a[u-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},c,l)&&(l=f.tickCoord-t*(p/2+o),a[u-1]=h(h({},f),{},{isShow:!0}))}for(var y=i?u-1:u,v=function(e){var r,i=a[e],u=function(){return void 0===r&&(r=n(i,e)),r};if(0===e){var f=t*(i.coordinate-t*u()/2-c);a[e]=i=h(h({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=h(h({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,u,c,l)&&(c=i.tickCoord+t*(u()/2+o),a[e]=h(h({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(S,A,j,f,d,"preserveStartEnd"===v):function(t,e,n,r,o){for(var i=(r||[]).slice(),a=i.length,u=e.start,c=e.end,l=function(e){var r,l=i[e],f=function(){return void 0===r&&(r=n(l,e)),r};if(e===a-1){var p=t*(l.coordinate+t*f()/2-c);i[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,u,c)&&(c=l.tickCoord-t*(f()/2+o),i[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(S,A,j,f,d),x.filter(function(t){return t.isShow}))}},5739:function(t,e,n){var r=n(8481);t.exports=function(t,e){return!!(null==t?0:t.length)&&r(t,e,0)>-1}},5753:function(t,e,n){"use strict";n.d(e,{A:function(){return r}});Array.prototype.slice;function r(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}},5789:function(t){t.exports=function(t){return t!=t}},5813:function(t){t.exports=function(t){return this.__data__.has(t)}},5862:function(t){t.exports=function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}},5891:function(t,e,n){var r=n(6775),o=n(7600),i=n(8629),a=r&&1/i(new r([,-0]))[1]==1/0?function(t){return new r(t)}:o;t.exports=a},5912:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},5962:function(t,e,n){var r=n(6252),o=n(6184),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=r(function(){return arguments}())?r:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")};t.exports=c},6006:function(t,e,n){"use strict";n.d(e,{IZ:function(){return l},Kg:function(){return u},yy:function(){return p}});n(2381),n(1609),n(4360);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function i(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach(function(e){a(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function a(t,e,n){var o;return o=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==r(o)?o:String(o))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var u=Math.PI/180,c=function(t){return 180*t/Math.PI},l=function(t,e,n,r){return{x:t+Math.cos(-u*r)*n,y:e+Math.sin(-u*r)*n}},s=function(t,e){var n,r,o,i,a,u,l=t.x,s=t.y,f=e.cx,p=e.cy,h=(r={x:f,y:p},o=(n={x:l,y:s}).x,i=n.y,a=r.x,u=r.y,Math.sqrt(Math.pow(o-a,2)+Math.pow(i-u,2)));if(h<=0)return{radius:h};var d=(l-f)/h,y=Math.acos(d);return s>p&&(y=2*Math.PI-y),{radius:h,angle:c(y),angleInRadian:y}},f=function(t,e){var n=e.startAngle,r=e.endAngle,o=Math.floor(n/360),i=Math.floor(r/360);return t+360*Math.min(o,i)},p=function(t,e){var n=t.x,r=t.y,o=s({x:n,y:r},e),a=o.radius,u=o.angle,c=e.innerRadius,l=e.outerRadius;if(a<c||a>l)return!1;if(0===a)return!0;var p,h=function(t){var e=t.startAngle,n=t.endAngle,r=Math.floor(e/360),o=Math.floor(n/360),i=Math.min(r,o);return{startAngle:e-360*i,endAngle:n-360*i}}(e),d=h.startAngle,y=h.endAngle,v=u;if(d<=y){for(;v>y;)v-=360;for(;v<d;)v+=360;p=v>=d&&v<=y}else{for(;v>d;)v-=360;for(;v<y;)v+=360;p=v>=y&&v<=d}return p?i(i({},e),{},{radius:a,angle:f(v,e)}):null}},6015:function(t){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},6075:function(t,e,n){"use strict";n.d(e,{AW:function(){return L},BU:function(){return k},J9:function(){return I},Me:function(){return M},Mn:function(){return j},ON:function(){return C},OV:function(){return D},X_:function(){return R},aS:function(){return P},ee:function(){return B}});var r=n(9650),o=n.n(r),i=n(2381),a=n.n(i),u=n(7561),c=n.n(u),l=n(4360),s=n.n(l),f=n(6015),p=n.n(f),h=n(1609),d=n(9085),y=n(186),v=n(9783),m=n(3038),g=["children"],b=["children"];function x(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}var O={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},j=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},S=null,A=null,E=function t(e){if(e===S&&Array.isArray(A))return A;var n=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?n=n.concat(t(e.props.children)):n.push(e))}),A=n,S=e,n};function P(t,e){var n=[],r=[];return r=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],E(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==r.indexOf(e)&&n.push(t)}),n}function k(t,e){var n=P(t,e);return n&&n[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,n=e.width,r=e.height;return!(!(0,y.Et)(n)||n<=0||!(0,y.Et)(r)||r<=0)},_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(t){return t&&t.type&&c()(t.type)&&_.indexOf(t.type)>=0},C=function(t){return t&&"object"===w(t)&&"cx"in t&&"cy"in t&&"r"in t},I=function(t,e,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,h.isValidElement)(t)&&(r=t.props),!p()(r))return null;var o={};return Object.keys(r).forEach(function(t){var i;(function(t,e,n,r){var o,i=null!==(o=null===m.VU||void 0===m.VU?void 0:m.VU[r])&&void 0!==o?o:[];return!s()(t)&&(r&&i.includes(e)||m.QQ.includes(e))||n&&m.j2.includes(e)})(null===(i=r)||void 0===i?void 0:i[t],t,e,n)&&(o[t]=r[t])}),o},D=function t(e,n){if(e===n)return!0;var r=h.Children.count(e);if(r!==h.Children.count(n))return!1;if(0===r)return!0;if(1===r)return N(Array.isArray(e)?e[0]:e,Array.isArray(n)?n[0]:n);for(var o=0;o<r;o++){var i=e[o],a=n[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var n=t.props||{},r=n.children,o=x(n,g),i=e.props||{},u=i.children,c=x(i,b);return r&&u?(0,v.b)(o,c)&&D(r,u):!r&&!u&&(0,v.b)(o,c)}return!1},B=function(t,e){var n=[],r={};return E(t).forEach(function(t,o){if(T(t))n.push(t);else if(t){var i=j(t.type),a=e[i]||{},u=a.handler,c=a.once;if(u&&(!c||!r[i])){var l=u(t,i,o);n.push(l),r[i]=!0}}}),n},R=function(t){var e=t&&t.type;return e&&O[e]?O[e]:null},L=function(t,e){return E(e).indexOf(t)}},6128:function(t,e,n){var r=n(1429)(Object.keys,Object);t.exports=r},6138:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=Array(r);++n<r;)o[n]=e(t[n],n,t);return o}},6143:function(t){t.exports=function(t){return function(e){return t(e)}}},6154:function(t,e,n){var r=n(6990),o=n(6184);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==r(t)}},6184:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},6218:function(t,e,n){var r=n(9830),o=n(9592),i=n(4683),a=n(6015);t.exports=function(t,e,n){if(!a(n))return!1;var u=typeof e;return!!("number"==u?o(n)&&i(e,n.length):"string"==u&&e in n)&&r(n[e],t)}},6252:function(t,e,n){var r=n(6990),o=n(6184);t.exports=function(t){return o(t)&&"[object Arguments]"==r(t)}},6323:function(t,e,n){var r=n(1192);t.exports=function(t,e,n){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=n.length;++o<u;){var l=r(i[o],a[o]);if(l)return o>=c?l:l*("desc"==n[o]?-1:1)}return t.index-e.index}},6352:function(t,e,n){"use strict";n.d(e,{u:function(){return h}});var r=n(1609),o=n.n(r),i=n(9685),a=n(6799),u=n(6075),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function p(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function h(t){var e=t.offset,n=t.layout,r=t.width,f=t.dataKey,h=t.data,d=t.dataPointFormatter,y=t.xAxis,v=t.yAxis,m=p(t,c),g=(0,u.J9)(m,!1);"x"===t.direction&&"number"!==y.type&&(0,i.A)(!1);var b=h.map(function(t){var i=d(t,f),u=i.x,c=i.y,p=i.value,h=i.errorVal;if(!h)return null;var m,b,x=[];if(Array.isArray(h)){var w=s(h,2);m=w[0],b=w[1]}else m=b=h;if("vertical"===n){var O=y.scale,j=c+e,S=j+r,A=j-r,E=O(p-m),P=O(p+b);x.push({x1:P,y1:S,x2:P,y2:A}),x.push({x1:E,y1:j,x2:P,y2:j}),x.push({x1:E,y1:S,x2:E,y2:A})}else if("horizontal"===n){var k=v.scale,M=u+e,_=M-r,T=M+r,C=k(p-m),I=k(p+b);x.push({x1:_,y1:I,x2:T,y2:I}),x.push({x1:M,y1:C,x2:M,y2:I}),x.push({x1:_,y1:C,x2:T,y2:C})}return o().createElement(a.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},g),x.map(function(t){return o().createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return o().createElement(a.W,{className:"recharts-errorBars"},b)}h.defaultProps={stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"},h.displayName="ErrorBar"},6434:function(t,e,n){"use strict";n.d(e,{A:function(){return s},h:function(){return l}});class r extends Map{constructor(t,e=u){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,n]of t)this.set(e,n)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(i(this,t),e)}delete(t){return super.delete(a(this,t))}}Set;function o({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):n}function i({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}function a({_intern:t,_key:e},n){const r=e(n);return t.has(r)&&(n=t.get(r),t.delete(r)),n}function u(t){return null!==t&&"object"==typeof t?t.valueOf():t}var c=n(2261);const l=Symbol("implicit");function s(){var t=new r,e=[],n=[],o=l;function i(r){let i=t.get(r);if(void 0===i){if(o!==l)return o;t.set(r,i=e.push(r)-1)}return n[i%n.length]}return i.domain=function(n){if(!arguments.length)return e.slice();e=[],t=new r;for(const r of n)t.has(r)||t.set(r,e.push(r)-1);return i},i.range=function(t){return arguments.length?(n=Array.from(t),i):n.slice()},i.unknown=function(t){return arguments.length?(o=t,i):o},i.copy=function(){return s(e,n).unknown(o)},c.C.apply(i,arguments),i}},6459:function(t){t.exports=function(t,e){return function(n){return null!=n&&(n[t]===e&&(void 0!==e||t in Object(n)))}}},6499:function(t){t.exports=function(){return[]}},6628:function(t,e,n){var r=n(4207),o=n(9592),i=n(8420);t.exports=function(t){return function(e,n,a){var u=Object(e);if(!o(e)){var c=r(n,3);e=i(e),n=function(t){return c(u[t],t,u)}}var l=t(e,n,a);return l>-1?u[c?e[l]:l]:void 0}}},6642:function(t,e,n){var r=n(7183);t.exports=function(){return r.Date.now()}},6678:function(t,e,n){"use strict";n.d(e,{g:function(){return s}});var r=n(9104),o=n(9162),i=n(6075);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach(function(e){l(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function l(t,e,n){var r;return r=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==a(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var s=function(t){var e,n=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,i.BU)(n,r.s);return s?(e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var n=e.item,r=e.props,o=r.sectors||r.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||n.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,n=e.props,r=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:r,type:s.props.iconType||a||"square",color:(0,o.Ps)(e),value:i||r,payload:e.props}}),c(c(c({},s.props),r.s.getWithHeight(s,u)),{},{payload:e,item:s})):null}},6775:function(t,e,n){var r=n(3188)(n(7183),"Set");t.exports=r},6799:function(t,e,n){"use strict";n.d(e,{W:function(){return s}});var r=n(1609),o=n.n(r),i=n(7064),a=n(6075),u=["children","className"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},c.apply(this,arguments)}function l(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var s=o().forwardRef(function(t,e){var n=t.children,r=t.className,s=l(t,u),f=(0,i.A)("recharts-layer",r);return o().createElement("g",c({className:f},(0,a.J9)(s,!0),{ref:e}),n)})},6810:function(t){t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},6846:function(t,e,n){var r=n(7183).Uint8Array;t.exports=r},6895:function(t,e,n){var r=n(1311),o=n(8420);t.exports=function(t,e){return t&&r(t,e,o)}},6917:function(t,e,n){"use strict";n.d(e,{d:function(){return T}});var r=n(1609),o=n.n(r),i=n(4360),a=n.n(i),u=n(928),c=n(186),l=n(6075),s=n(9162),f=n(5706),p=n(684),h=n(3499),d=["x1","y1","x2","y2","key"],y=["offset"];function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?m(Object(n),!0).forEach(function(e){b(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function b(t,e,n){var r;return r=function(t,e){if("object"!=v(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=v(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==v(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},x.apply(this,arguments)}function w(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var O=function(t){var e=t.fill;if(!e||"none"===e)return null;var n=t.fillOpacity,r=t.x,i=t.y,a=t.width,u=t.height;return o().createElement("rect",{x:r,y:i,width:a,height:u,stroke:"none",fill:e,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function j(t,e){var n;if(o().isValidElement(t))n=o().cloneElement(t,e);else if(a()(t))n=t(e);else{var r=e.x1,i=e.y1,u=e.x2,c=e.y2,s=e.key,f=w(e,d),p=(0,l.J9)(f,!1),h=(p.offset,w(p,y));n=o().createElement("line",x({},h,{x1:r,y1:i,x2:u,y2:c,fill:"none",key:s}))}return n}function S(t){var e=t.x,n=t.width,r=t.horizontal,i=void 0===r||r,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(r,o){var a=g(g({},t),{},{x1:e,y1:r,x2:e+n,y2:r,key:"line-".concat(o),index:o});return j(i,a)});return o().createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function A(t){var e=t.y,n=t.height,r=t.vertical,i=void 0===r||r,a=t.verticalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(r,o){var a=g(g({},t),{},{x1:r,y1:e,x2:r,y2:e+n,key:"line-".concat(o),index:o});return j(i,a)});return o().createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function E(t){var e=t.horizontalFill,n=t.fillOpacity,r=t.x,i=t.y,a=t.width,u=t.height,c=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=c.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,c){var l=!s[c+1]?i+u-t:s[c+1]-t;if(l<=0)return null;var f=c%e.length;return o().createElement("rect",{key:"react-".concat(c),y:t,x:r,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return o().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,n=void 0===e||e,r=t.verticalFill,i=t.fillOpacity,a=t.x,u=t.y,c=t.width,l=t.height,s=t.verticalPoints;if(!n||!r||!r.length)return null;var f=s.map(function(t){return Math.round(t+a-a)}).sort(function(t,e){return t-e});a!==f[0]&&f.unshift(0);var p=f.map(function(t,e){var n=!f[e+1]?a+c-t:f[e+1]-t;if(n<=0)return null;var s=e%r.length;return o().createElement("rect",{key:"react-".concat(e),x:t,y:u,width:n,height:l,stroke:"none",fill:r[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return o().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var k=function(t,e){var n=t.xAxis,r=t.width,o=t.height,i=t.offset;return(0,s.PW)((0,f.f)(g(g(g({},p.u.defaultProps),n),{},{ticks:(0,s.Rh)(n,!0),viewBox:{x:0,y:0,width:r,height:o}})),i.left,i.left+i.width,e)},M=function(t,e){var n=t.yAxis,r=t.width,o=t.height,i=t.offset;return(0,s.PW)((0,f.f)(g(g(g({},p.u.defaultProps),n),{},{ticks:(0,s.Rh)(n,!0),viewBox:{x:0,y:0,width:r,height:o}})),i.top,i.top+i.height,e)},_={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function T(t){var e,n,r,i,l,s,f=(0,h.yi)(),p=(0,h.rY)(),d=(0,h.hj)(),y=g(g({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:_.stroke,fill:null!==(n=t.fill)&&void 0!==n?n:_.fill,horizontal:null!==(r=t.horizontal)&&void 0!==r?r:_.horizontal,horizontalFill:null!==(i=t.horizontalFill)&&void 0!==i?i:_.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:_.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:_.verticalFill,x:(0,c.Et)(t.x)?t.x:d.left,y:(0,c.Et)(t.y)?t.y:d.top,width:(0,c.Et)(t.width)?t.width:d.width,height:(0,c.Et)(t.height)?t.height:d.height}),m=y.x,b=y.y,w=y.width,j=y.height,T=y.syncWithTicks,C=y.horizontalValues,I=y.verticalValues,D=(0,h.pj)(),N=(0,h.$G)();if(!(0,c.Et)(w)||w<=0||!(0,c.Et)(j)||j<=0||!(0,c.Et)(m)||m!==+m||!(0,c.Et)(b)||b!==+b)return null;var B=y.verticalCoordinatesGenerator||k,R=y.horizontalCoordinatesGenerator||M,L=y.horizontalPoints,z=y.verticalPoints;if((!L||!L.length)&&a()(R)){var U=C&&C.length,F=R({yAxis:N?g(g({},N),{},{ticks:U?C:N.ticks}):void 0,width:f,height:p,offset:d},!!U||T);(0,u.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(v(F),"]")),Array.isArray(F)&&(L=F)}if((!z||!z.length)&&a()(B)){var $=I&&I.length,W=B({xAxis:D?g(g({},D),{},{ticks:$?I:D.ticks}):void 0,width:f,height:p,offset:d},!!$||T);(0,u.R)(Array.isArray(W),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(v(W),"]")),Array.isArray(W)&&(z=W)}return o().createElement("g",{className:"recharts-cartesian-grid"},o().createElement(O,{fill:y.fill,fillOpacity:y.fillOpacity,x:y.x,y:y.y,width:y.width,height:y.height}),o().createElement(S,x({},y,{offset:d,horizontalPoints:L,xAxis:D,yAxis:N})),o().createElement(A,x({},y,{offset:d,verticalPoints:z,xAxis:D,yAxis:N})),o().createElement(E,x({},y,{horizontalPoints:L})),o().createElement(P,x({},y,{verticalPoints:z})))}T.displayName="CartesianGrid"},6937:function(t,e,n){var r=n(1308),o=n(8344),i=n(6966),a=n(2832);t.exports=function(t){return function(e){e=a(e);var n=o(e)?i(e):void 0,u=n?n[0]:e.charAt(0),c=n?r(n,1).join(""):e.slice(1);return u[t]()+c}}},6966:function(t,e,n){var r=n(8284),o=n(8344),i=n(5564);t.exports=function(t){return o(t)?i(t):r(t)}},6990:function(t,e,n){var r=n(7187),o=n(1029),i=n(8704),a=r?r.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},7064:function(t,e,n){"use strict";function r(t){var e,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(n=r(t[e]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}e.A=function(){for(var t,e,n=0,o="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=r(t))&&(o&&(o+=" "),o+=e);return o}},7066:function(t,e,n){var r=n(1158),o=/^\s+/;t.exports=function(t){return t?t.slice(0,r(t)+1).replace(o,""):t}},7098:function(t,e,n){var r=n(4183);t.exports=function(t,e){var n;return r(t,function(t,r,o){return!(n=e(t,r,o))}),!!n}},7114:function(t,e,n){var r=n(2823),o=n(5237),i=n(3728),a=n(5355),u=n(8355),c=n(4383),l=n(8098),s=n(3905),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,y,v,m){var g=c(t),b=c(e),x=g?p:u(t),w=b?p:u(e),O=(x=x==f?h:x)==h,j=(w=w==f?h:w)==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;g=!0,O=!1}if(S&&!O)return m||(m=new r),g||s(t)?o(t,e,n,y,v,m):i(t,e,x,n,y,v,m);if(!(1&n)){var A=O&&d.call(t,"__wrapped__"),E=j&&d.call(e,"__wrapped__");if(A||E){var P=A?t.value():t,k=E?e.value():e;return m||(m=new r),v(P,k,n,y,m)}}return!!S&&(m||(m=new r),a(t,e,n,y,v,m))}},7156:function(t,e,n){var r=n(4321),o=n(4873),i=n(9090);t.exports=function(t){return t&&t.length?r(t,i,o):void 0}},7183:function(t,e,n){var r=n(5194),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();t.exports=i},7187:function(t,e,n){var r=n(7183).Symbol;t.exports=r},7234:function(t,e,n){var r,o=n(5171),i=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";t.exports=function(t){return!!i&&i in t}},7316:function(t,e,n){var r=n(3368),o=n(3733),i=n(9090),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:i;t.exports=a},7493:function(t,e,n){var r=n(3234),o=n(2717),i=n(5580),a=n(6218),u=i(function(t,e){if(null==t)return[];var n=e.length;return n>1&&a(t,e[0],e[1])?e=[]:n>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,r(e,1),[])});t.exports=u},7561:function(t,e,n){var r=n(6990),o=n(4383),i=n(6184);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==r(t)}},7598:function(t,e,n){t.exports=n(856)()},7600:function(t){t.exports=function(){}},7724:function(t,e,n){"use strict";n.d(e,{J:function(){return _}});var r=n(1609),o=n.n(r),i=n(2381),a=n.n(i),u=n(4360),c=n.n(u),l=n(6015),s=n.n(l),f=n(7064),p=n(3704),h=n(6075),d=n(186),y=n(6006);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["offset"];function g(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return b(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function x(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach(function(e){j(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function j(t,e,n){var r;return r=function(t,e){if("object"!=v(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=v(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==v(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function S(){return S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},S.apply(this,arguments)}var A=function(t){var e=t.value,n=t.formatter,r=a()(t.children)?e:t.children;return c()(n)?n(r):r},E=function(t,e,n){var r,i,u=t.position,c=t.viewBox,l=t.offset,s=t.className,p=c,h=p.cx,v=p.cy,m=p.innerRadius,g=p.outerRadius,b=p.startAngle,x=p.endAngle,w=p.clockWise,O=(m+g)/2,j=function(t,e){return(0,d.sA)(e-t)*Math.min(Math.abs(e-t),360)}(b,x),A=j>=0?1:-1;"insideStart"===u?(r=b+A*l,i=w):"insideEnd"===u?(r=x-A*l,i=!w):"end"===u&&(r=x+A*l,i=w),i=j<=0?i:!i;var E=(0,y.IZ)(h,v,O,r),P=(0,y.IZ)(h,v,O,r+359*(i?1:-1)),k="M".concat(E.x,",").concat(E.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(i?0:1,",\n    ").concat(P.x,",").concat(P.y),M=a()(t.id)?(0,d.NF)("recharts-radial-line-"):t.id;return o().createElement("text",S({},n,{dominantBaseline:"central",className:(0,f.A)("recharts-radial-bar-label",s)}),o().createElement("defs",null,o().createElement("path",{id:M,d:k})),o().createElement("textPath",{xlinkHref:"#".concat(M)},e))},P=function(t){var e=t.viewBox,n=t.offset,r=t.position,o=e,i=o.cx,a=o.cy,u=o.innerRadius,c=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===r){var s=(0,y.IZ)(i,a,c+n,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(u+c)/2,h=(0,y.IZ)(i,a,p,l);return{x:h.x,y:h.y,textAnchor:"middle",verticalAnchor:"middle"}},k=function(t){var e=t.viewBox,n=t.parentViewBox,r=t.offset,o=t.position,i=e,a=i.x,u=i.y,c=i.width,l=i.height,f=l>=0?1:-1,p=f*r,h=f>0?"end":"start",y=f>0?"start":"end",v=c>=0?1:-1,m=v*r,g=v>0?"end":"start",b=v>0?"start":"end";if("top"===o)return O(O({},{x:a+c/2,y:u-f*r,textAnchor:"middle",verticalAnchor:h}),n?{height:Math.max(u-n.y,0),width:c}:{});if("bottom"===o)return O(O({},{x:a+c/2,y:u+l+p,textAnchor:"middle",verticalAnchor:y}),n?{height:Math.max(n.y+n.height-(u+l),0),width:c}:{});if("left"===o){var x={x:a-m,y:u+l/2,textAnchor:g,verticalAnchor:"middle"};return O(O({},x),n?{width:Math.max(x.x-n.x,0),height:l}:{})}if("right"===o){var w={x:a+c+m,y:u+l/2,textAnchor:b,verticalAnchor:"middle"};return O(O({},w),n?{width:Math.max(n.x+n.width-w.x,0),height:l}:{})}var j=n?{width:c,height:l}:{};return"insideLeft"===o?O({x:a+m,y:u+l/2,textAnchor:b,verticalAnchor:"middle"},j):"insideRight"===o?O({x:a+c-m,y:u+l/2,textAnchor:g,verticalAnchor:"middle"},j):"insideTop"===o?O({x:a+c/2,y:u+p,textAnchor:"middle",verticalAnchor:y},j):"insideBottom"===o?O({x:a+c/2,y:u+l-p,textAnchor:"middle",verticalAnchor:h},j):"insideTopLeft"===o?O({x:a+m,y:u+p,textAnchor:b,verticalAnchor:y},j):"insideTopRight"===o?O({x:a+c-m,y:u+p,textAnchor:g,verticalAnchor:y},j):"insideBottomLeft"===o?O({x:a+m,y:u+l-p,textAnchor:b,verticalAnchor:h},j):"insideBottomRight"===o?O({x:a+c-m,y:u+l-p,textAnchor:g,verticalAnchor:h},j):s()(o)&&((0,d.Et)(o.x)||(0,d._3)(o.x))&&((0,d.Et)(o.y)||(0,d._3)(o.y))?O({x:a+(0,d.F4)(o.x,c),y:u+(0,d.F4)(o.y,l),textAnchor:"end",verticalAnchor:"end"},j):O({x:a+c/2,y:u+l/2,textAnchor:"middle",verticalAnchor:"middle"},j)},M=function(t){return"cx"in t&&(0,d.Et)(t.cx)};function _(t){var e,n=t.offset,i=O({offset:void 0===n?5:n},x(t,m)),u=i.viewBox,l=i.position,s=i.value,d=i.children,y=i.content,v=i.className,g=void 0===v?"":v,b=i.textBreakAll;if(!u||a()(s)&&a()(d)&&!(0,r.isValidElement)(y)&&!c()(y))return null;if((0,r.isValidElement)(y))return(0,r.cloneElement)(y,i);if(c()(y)){if(e=(0,r.createElement)(y,i),(0,r.isValidElement)(e))return e}else e=A(i);var w=M(u),j=(0,h.J9)(i,!0);if(w&&("insideStart"===l||"insideEnd"===l||"end"===l))return E(i,e,j);var _=w?P(i):k(i);return o().createElement(p.E,S({className:(0,f.A)("recharts-label",g)},j,_,{breakAll:b}),e)}_.displayName="Label";var T=function(t){var e=t.cx,n=t.cy,r=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,v=t.height,m=t.clockWise,g=t.labelViewBox;if(g)return g;if((0,d.Et)(y)&&(0,d.Et)(v)){if((0,d.Et)(s)&&(0,d.Et)(f))return{x:s,y:f,width:y,height:v};if((0,d.Et)(p)&&(0,d.Et)(h))return{x:p,y:h,width:y,height:v}}return(0,d.Et)(s)&&(0,d.Et)(f)?{x:s,y:f,width:0,height:0}:(0,d.Et)(e)&&(0,d.Et)(n)?{cx:e,cy:n,startAngle:o||r||0,endAngle:i||r||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};_.parseViewBox=T,_.renderCallByParent=function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=T(t),u=(0,h.aS)(i,_).map(function(t,n){return(0,r.cloneElement)(t,{viewBox:e||a,key:"label-".concat(n)})});if(!n)return u;var l=function(t,e){return t?!0===t?o().createElement(_,{key:"label-implicit",viewBox:e}):(0,d.vh)(t)?o().createElement(_,{key:"label-implicit",viewBox:e,value:t}):(0,r.isValidElement)(t)?t.type===_?(0,r.cloneElement)(t,{key:"label-implicit",viewBox:e}):o().createElement(_,{key:"label-implicit",content:t,viewBox:e}):c()(t)?o().createElement(_,{key:"label-implicit",content:t,viewBox:e}):s()(t)?o().createElement(_,S({viewBox:e},t,{key:"label-implicit"})):null:null}(t.label,e||a);return[l].concat(g(u))}},7736:function(t,e,n){var r=n(4506)();t.exports=r},7747:function(t,e,n){var r=n(1960),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(r){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(e,t)?e[t]:void 0}},7752:function(t){t.exports=function(){this.__data__=[],this.size=0}},7773:function(t){t.exports=function(t,e){return t.has(e)}},7849:function(t,e,n){var r=n(435);t.exports=function(t){var e=this.__data__,n=r(e,t);return n<0?void 0:e[n][1]}},7938:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}},8037:function(t,e,n){var r=n(5912);t.exports=function(t,e){var n=t.__data__;return r(e)?n["string"==typeof e?"string":"hash"]:n.map}},8089:function(t){var e=Object.prototype;t.exports=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||e)}},8098:function(t,e,n){t=n.nmd(t);var r=n(7183),o=n(1329),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?r.Buffer:void 0,c=(u?u.isBuffer:void 0)||o;t.exports=c},8284:function(t){t.exports=function(t){return t.split("")}},8291:function(t,e,n){"use strict";n.d(e,{I:function(){return Y}});var r=n(1609),o=n.n(r);function i(){}function a(t,e,n){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+n)/6)}function u(t){this._context=t}function c(t){this._context=t}function l(t){this._context=t}u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(n,r):this._context.moveTo(n,r);break;case 3:this._point=4;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class s{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function f(t){this._context=t}function p(t){this._context=t}function h(t){return new p(t)}function d(t){return t<0?-1:1}function y(t,e,n){var r=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(r||o<0&&-0),a=(n-t._y1)/(o||r<0&&-0),u=(i*o+a*r)/(r+o);return(d(i)+d(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(u))||0}function v(t,e){var n=t._x1-t._x0;return n?(3*(t._y1-t._y0)/n-e)/2:e}function m(t,e,n){var r=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-r)/3;t._context.bezierCurveTo(r+u,o+u*e,i-u,a-u*n,i,a)}function g(t){this._context=t}function b(t){this._context=new x(t)}function x(t){this._context=t}function w(t){this._context=t}function O(t){var e,n,r=t.length-1,o=new Array(r),i=new Array(r),a=new Array(r);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<r-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[r-1]=2,i[r-1]=7,a[r-1]=8*t[r-1]+t[r],e=1;e<r;++e)n=o[e]/i[e-1],i[e]-=n,a[e]-=n*a[e-1];for(o[r-1]=a[r-1]/i[r-1],e=r-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[r-1]=(t[r]+o[r-1])/2,e=0;e<r-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function j(t,e){this._context=t,this._t=e}f.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},p.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:m(this,this._t0,v(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var n=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,m(this,v(this,n=y(this,t,e)),n);break;default:m(this,this._t0,n=y(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=n}}},(b.prototype=Object.create(g.prototype)).point=function(t,e){g.prototype.point.call(this,e,t)},x.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,n,r,o,i){this._context.bezierCurveTo(e,t,r,n,i,o)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,n=t.length;if(n)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===n)this._context.lineTo(t[1],e[1]);else for(var r=O(t),o=O(e),i=0,a=1;a<n;++i,++a)this._context.bezierCurveTo(r[0][i],o[0][i],r[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===n)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},j.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var n=this._x*(1-this._t)+t*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,e)}}this._x=t,this._y=e}};var S=n(5753),A=n(9886),E=n(5137);function P(t){return t[0]}function k(t){return t[1]}function M(t,e){var n=(0,A.A)(!0),r=null,o=h,i=null,a=(0,E.i)(u);function u(u){var c,l,s,f=(u=(0,S.A)(u)).length,p=!1;for(null==r&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&n(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?P:(0,A.A)(t),e="function"==typeof e?e:void 0===e?k:(0,A.A)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),u):e},u.defined=function(t){return arguments.length?(n="function"==typeof t?t:(0,A.A)(!!t),u):n},u.curve=function(t){return arguments.length?(o=t,null!=r&&(i=o(r)),u):o},u.context=function(t){return arguments.length?(null==t?r=i=null:i=o(r=t),u):r},u}function _(t,e,n){var r=null,o=(0,A.A)(!0),i=null,a=h,u=null,c=(0,E.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,S.A)(l)).length,v=!1,m=new Array(y),g=new Array(y);for(null==i&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],g[p]);u.lineEnd(),u.areaEnd()}v&&(m[s]=+t(h,s,l),g[s]=+e(h,s,l),u.point(r?+r(h,s,l):m[s],n?+n(h,s,l):g[s]))}if(d)return u=null,d+""||null}function s(){return M().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?P:(0,A.A)(+t),e="function"==typeof e?e:void 0===e?(0,A.A)(0):(0,A.A)(+e),n="function"==typeof n?n:void 0===n?k:(0,A.A)(+n),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),r=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),l):t},l.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,A.A)(+t),l):r},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),n=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),l):e},l.y1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,A.A)(+t),l):n},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(n)},l.lineX1=function(){return s().x(r).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,A.A)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var T=n(4254),C=n.n(T),I=n(4360),D=n.n(I),N=n(7064),B=n(3038),R=n(6075),L=n(186);function z(t){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},z(t)}function U(){return U=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},U.apply(this,arguments)}function F(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function $(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?F(Object(n),!0).forEach(function(e){W(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function W(t,e,n){var r;return r=function(t,e){if("object"!=z(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==z(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var q={curveBasisClosed:function(t){return new c(t)},curveBasisOpen:function(t){return new l(t)},curveBasis:function(t){return new u(t)},curveBumpX:function(t){return new s(t,!0)},curveBumpY:function(t){return new s(t,!1)},curveLinearClosed:function(t){return new f(t)},curveLinear:h,curveMonotoneX:function(t){return new g(t)},curveMonotoneY:function(t){return new b(t)},curveNatural:function(t){return new w(t)},curveStep:function(t){return new j(t,.5)},curveStepAfter:function(t){return new j(t,1)},curveStepBefore:function(t){return new j(t,0)}},X=function(t){return t.x===+t.x&&t.y===+t.y},H=function(t){return t.x},V=function(t){return t.y},G=function(t){var e,n=t.type,r=void 0===n?"linear":n,o=t.points,i=void 0===o?[]:o,a=t.baseLine,u=t.layout,c=t.connectNulls,l=void 0!==c&&c,s=function(t,e){if(D()(t))return t;var n="curve".concat(C()(t));return"curveMonotone"!==n&&"curveBump"!==n||!e?q[n]||h:q["".concat(n).concat("vertical"===e?"Y":"X")]}(r,u),f=l?i.filter(function(t){return X(t)}):i;if(Array.isArray(a)){var p=l?a.filter(function(t){return X(t)}):a,d=f.map(function(t,e){return $($({},t),{},{base:p[e]})});return(e="vertical"===u?_().y(V).x1(H).x0(function(t){return t.base.x}):_().x(H).y1(V).y0(function(t){return t.base.y})).defined(X).curve(s),e(d)}return(e="vertical"===u&&(0,L.Et)(a)?_().y(V).x1(H).x0(a):(0,L.Et)(a)?_().x(H).y1(V).y0(a):M().x(H).y(V)).defined(X).curve(s),e(f)},Y=function(t){var e=t.className,n=t.points,r=t.path,i=t.pathRef;if(!(n&&n.length||r))return null;var a=n&&n.length?G(t):r;return o().createElement("path",U({},(0,R.J9)(t,!1),(0,B._U)(t),{className:(0,N.A)("recharts-curve",e),d:a,ref:i}))}},8342:function(t,e,n){var r=n(1960);t.exports=function(){this.__data__=r?r(null):{},this.size=0}},8344:function(t){var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},8355:function(t,e,n){var r=n(5178),o=n(8561),i=n(1490),a=n(6775),u=n(1366),c=n(6990),l=n(275),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(r),v=l(o),m=l(i),g=l(a),b=l(u),x=c;(r&&x(new r(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=h)&&(x=function(t){var e=c(t),n="[object Object]"==e?t.constructor:void 0,r=n?l(n):"";if(r)switch(r){case y:return d;case v:return s;case m:return f;case g:return p;case b:return h}return e}),t.exports=x},8420:function(t,e,n){var r=n(661),o=n(9966),i=n(9592);t.exports=function(t){return i(t)?r(t):o(t)}},8423:function(t){t.exports=function(t){return this.__data__.has(t)}},8434:function(t,e,n){var r=n(3028);t.exports=function(t,e){return r(t,e)}},8481:function(t,e,n){var r=n(3705),o=n(5789),i=n(9705);t.exports=function(t,e,n){return e==e?i(t,e,n):r(t,o,n)}},8554:function(t,e,n){var r=n(4321),o=n(4214),i=n(9090);t.exports=function(t){return t&&t.length?r(t,i,o):void 0}},8561:function(t,e,n){var r=n(3188)(n(7183),"Map");t.exports=r},8629:function(t){t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}},8704:function(t){var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},8742:function(t,e,n){"use strict";n.d(e,{A3:function(){return p},Pu:function(){return f}});var r=n(9e3);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach(function(e){u(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function u(t,e,n){var r;return r=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==o(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span";var f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||r.m.isSsr)return{width:0,height:0};var n,o=(n=a({},e),Object.keys(n).forEach(function(t){n[t]||delete n[t]}),n),i=JSON.stringify({text:t,copyStyle:o});if(c.widthCache[i])return c.widthCache[i];try{var u=document.getElementById(s);u||((u=document.createElement("span")).setAttribute("id",s),u.setAttribute("aria-hidden","true"),document.body.appendChild(u));var f=a(a({},l),o);Object.assign(u.style,f),u.textContent="".concat(t);var p=u.getBoundingClientRect(),h={width:p.width,height:p.height};return c.widthCache[i]=h,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),h}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},8869:function(t,e,n){var r=n(1451),o=n(6810),i=n(5813);function a(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new r;++e<n;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},8887:function(t){t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach(function(t,r){n[++e]=[r,t]}),n}},8926:function(t,e,n){var r=n(789);t.exports=function(){this.__data__=new r,this.size=0}},8938:function(t,e,n){var r=n(6015);t.exports=function(t){return t==t&&!r(t)}},9e3:function(t,e,n){"use strict";n.d(e,{m:function(){return r}});var r={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return r[t]},set:function(t,e){if("string"==typeof t)r[t]=e;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(e){r[e]=t[e]})}}}},9085:function(t,e,n){"use strict";t.exports=n(2193)},9089:function(t,e,n){var r=n(3234),o=n(764);t.exports=function(t,e){return r(o(t,e),1)}},9090:function(t){t.exports=function(t){return t}},9098:function(t,e,n){var r=n(1451);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=e?e.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=t.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(o.Cache||r),n}o.Cache=r,t.exports=o},9104:function(t,e,n){"use strict";n.d(e,{s:function(){return U}});var r=n(1609),o=n.n(r),i=n(4360),a=n.n(i),u=n(7064),c=n(928),l=n(1989),s=n(4204),f=n(3038);function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},h.apply(this,arguments)}function d(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,w(r.key),r)}}function v(t,e,n){return e=g(e),function(t,e){if(e&&("object"===p(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,m()?Reflect.construct(e,n||[],g(t).constructor):e.apply(t,n))}function m(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(m=function(){return!!t})()}function g(t){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},g(t)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function x(t,e,n){return(e=w(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function w(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:String(e)}var O=32,j=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),v(this,e,arguments)}var n,r,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}(e,t),n=e,r=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,n=16,r=O/6,i=O/3,a=t.inactive?e:t.color;if("plainline"===t.type)return o().createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:n,x2:O,y2:n,className:"recharts-legend-icon"});if("line"===t.type)return o().createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(n,"h").concat(i,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*i,",").concat(n,"\n            H").concat(O,"M").concat(2*i,",").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(i,",").concat(n),className:"recharts-legend-icon"});if("rect"===t.type)return o().createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(O,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(o().isValidElement(t.legendIcon)){var u=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?d(Object(n),!0).forEach(function(e){x(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},t);return delete u.legendIcon,o().cloneElement(t.legendIcon,u)}return o().createElement(s.i,{fill:a,cx:n,cy:n,size:O,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,n=e.payload,r=e.iconSize,i=e.layout,s=e.formatter,p=e.inactiveColor,d={x:0,y:0,width:O,height:O},y={display:"horizontal"===i?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map(function(e,n){var i=e.formatter||s,m=(0,u.A)(x(x({"recharts-legend-item":!0},"legend-item-".concat(n),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=a()(e.value)?null:e.value;(0,c.R)(!a()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var b=e.inactive?p:e.color;return o().createElement("li",h({className:m,style:y,key:"legend-item-".concat(n)},(0,f.XC)(t.props,e,n)),o().createElement(l.u,{width:r,height:r,viewBox:d,style:v},t.renderIcon(e)),o().createElement("span",{className:"recharts-legend-item-text",style:{color:b}},i?i(g,e,n):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,n=t.layout,r=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===n?r:"left"};return o().createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],r&&y(n.prototype,r),i&&y(n,i),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.PureComponent);x(j,"displayName","Legend"),x(j,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var S=n(186),A=n(2157);function E(t){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(t)}var P=["ref"];function k(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?k(Object(n),!0).forEach(function(e){B(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,R(r.key),r)}}function T(t,e,n){return e=I(e),function(t,e){if(e&&("object"===E(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return D(t)}(t,C()?Reflect.construct(e,n||[],I(t).constructor):e.apply(t,n))}function C(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(C=function(){return!!t})()}function I(t){return I=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},I(t)}function D(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function N(t,e){return N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},N(t,e)}function B(t,e,n){return(e=R(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function R(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:String(e)}function L(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function z(t){return t.value}var U=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return B(D(t=T(this,e,[].concat(r))),"lastBoundingBox",{width:-1,height:-1}),t}var n,r,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&N(t,e)}(e,t),n=e,i=[{key:"getWithHeight",value:function(t,e){var n=t.props.layout;return"vertical"===n&&(0,S.Et)(t.props.height)?{height:t.props.height}:"horizontal"===n?{width:t.props.width||e}:null}}],(r=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?M({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,n,r=this.props,o=r.layout,i=r.align,a=r.verticalAlign,u=r.margin,c=r.chartWidth,l=r.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(n="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),M(M({},e),n)}},{key:"render",value:function(){var t=this,e=this.props,n=e.content,r=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=M(M({position:"absolute",width:r||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return o().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(o().isValidElement(t))return o().cloneElement(t,e);if("function"==typeof t)return o().createElement(t,e);e.ref;var n=L(e,P);return o().createElement(j,n)}(n,M(M({},this.props),{},{payload:(0,A.s)(c,u,z)})))}}])&&_(n.prototype,r),i&&_(n,i),Object.defineProperty(n,"prototype",{writable:!1}),e}(r.PureComponent);B(U,"displayName","Legend"),B(U,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},9121:function(t,e,n){var r=n(2823),o=n(3028);t.exports=function(t,e,n,i){var a=n.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=n[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=n[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new r;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},9142:function(t,e,n){var r=n(3733);t.exports=function(t,e,n){"__proto__"==e&&r?r(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}},9147:function(t,e,n){var r=n(5410);t.exports=function(t){var e=r(t),n=e%1;return e==e?n?e-n:e:0}},9162:function(t,e,n){"use strict";n.d(e,{s0:function(){return wi},gH:function(){return mi},YB:function(){return Ci},HQ:function(){return Mi},xi:function(){return Ii},Hj:function(){return Vi},BX:function(){return xi},tA:function(){return bi},DW:function(){return Fi},y2:function(){return Ui},nb:function(){return zi},PW:function(){return Ei},Ay:function(){return vi},vf:function(){return Si},Mk:function(){return Wi},Ps:function(){return gi},Mn:function(){return Ri},kA:function(){return $i},Rh:function(){return Pi},w7:function(){return Li},zb:function(){return Yi},kr:function(){return yi},_L:function(){return Ai},KC:function(){return Gi},A1:function(){return ji},W7:function(){return _i},AQ:function(){return Hi},_f:function(){return Di}});var r={};n.r(r),n.d(r,{scaleBand:function(){return o.A},scaleDiverging:function(){return Qr},scaleDivergingLog:function(){return to},scaleDivergingPow:function(){return no},scaleDivergingSqrt:function(){return ro},scaleDivergingSymlog:function(){return eo},scaleIdentity:function(){return Xt},scaleImplicit:function(){return ie.h},scaleLinear:function(){return qt},scaleLog:function(){return te},scaleOrdinal:function(){return ie.A},scalePoint:function(){return o.z},scalePow:function(){return se},scaleQuantile:function(){return Oe},scaleQuantize:function(){return je},scaleRadial:function(){return he},scaleSequential:function(){return Hr},scaleSequentialLog:function(){return Vr},scaleSequentialPow:function(){return Yr},scaleSequentialQuantile:function(){return Jr},scaleSequentialSqrt:function(){return Kr},scaleSequentialSymlog:function(){return Gr},scaleSqrt:function(){return fe},scaleSymlog:function(){return oe},scaleThreshold:function(){return Se},scaleTime:function(){return $r},scaleUtc:function(){return Wr},tickFormat:function(){return $t}});var o=n(5084);const i=Math.sqrt(50),a=Math.sqrt(10),u=Math.sqrt(2);function c(t,e,n){const r=(e-t)/Math.max(0,n),o=Math.floor(Math.log10(r)),l=r/Math.pow(10,o),s=l>=i?10:l>=a?5:l>=u?2:1;let f,p,h;return o<0?(h=Math.pow(10,-o)/s,f=Math.round(t*h),p=Math.round(e*h),f/h<t&&++f,p/h>e&&--p,h=-h):(h=Math.pow(10,o)*s,f=Math.round(t/h),p=Math.round(e/h),f*h<t&&++f,p*h>e&&--p),p<f&&.5<=n&&n<2?c(t,e,2*n):[f,p,h]}function l(t,e,n){if(!((n=+n)>0))return[];if((t=+t)===(e=+e))return[t];const r=e<t,[o,i,a]=r?c(e,t,n):c(t,e,n);if(!(i>=o))return[];const u=i-o+1,l=new Array(u);if(r)if(a<0)for(let t=0;t<u;++t)l[t]=(i-t)/-a;else for(let t=0;t<u;++t)l[t]=(i-t)*a;else if(a<0)for(let t=0;t<u;++t)l[t]=(o+t)/-a;else for(let t=0;t<u;++t)l[t]=(o+t)*a;return l}function s(t,e,n){return c(t=+t,e=+e,n=+n)[2]}function f(t,e,n){n=+n;const r=(e=+e)<(t=+t),o=r?s(e,t,n):s(t,e,n);return(r?-1:1)*(o<0?1/-o:o)}function p(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function h(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function d(t){let e,n,r;function o(t,r,o=0,i=t.length){if(o<i){if(0!==e(r,r))return i;do{const e=o+i>>>1;n(t[e],r)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=p,n=(e,n)=>p(t(e),n),r=(e,n)=>t(e)-n):(e=t===p||t===h?t:y,n=t,r=t),{left:o,center:function(t,e,n=0,i=t.length){const a=o(t,e,n,i-1);return a>n&&r(t[a-1],e)>-r(t[a],e)?a-1:a},right:function(t,r,o=0,i=t.length){if(o<i){if(0!==e(r,r))return i;do{const e=o+i>>>1;n(t[e],r)<=0?o=e+1:i=e}while(o<i)}return o}}}function y(){return 0}function v(t){return null===t?NaN:+t}const m=d(p),g=m.right;m.left,d(v).center;var b=g;function x(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function w(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function O(){}var j=.7,S=1/j,A="\\s*([+-]?\\d+)\\s*",E="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",P="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",k=/^#([0-9a-f]{3,8})$/,M=new RegExp(`^rgb\\(${A},${A},${A}\\)$`),_=new RegExp(`^rgb\\(${P},${P},${P}\\)$`),T=new RegExp(`^rgba\\(${A},${A},${A},${E}\\)$`),C=new RegExp(`^rgba\\(${P},${P},${P},${E}\\)$`),I=new RegExp(`^hsl\\(${E},${P},${P}\\)$`),D=new RegExp(`^hsla\\(${E},${P},${P},${E}\\)$`),N={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function B(){return this.rgb().formatHex()}function R(){return this.rgb().formatRgb()}function L(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=k.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?z(e):3===n?new $(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?U(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?U(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=M.exec(t))?new $(e[1],e[2],e[3],1):(e=_.exec(t))?new $(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=T.exec(t))?U(e[1],e[2],e[3],e[4]):(e=C.exec(t))?U(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=I.exec(t))?G(e[1],e[2]/100,e[3]/100,1):(e=D.exec(t))?G(e[1],e[2]/100,e[3]/100,e[4]):N.hasOwnProperty(t)?z(N[t]):"transparent"===t?new $(NaN,NaN,NaN,0):null}function z(t){return new $(t>>16&255,t>>8&255,255&t,1)}function U(t,e,n,r){return r<=0&&(t=e=n=NaN),new $(t,e,n,r)}function F(t,e,n,r){return 1===arguments.length?((o=t)instanceof O||(o=L(o)),o?new $((o=o.rgb()).r,o.g,o.b,o.opacity):new $):new $(t,e,n,null==r?1:r);var o}function $(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function W(){return`#${V(this.r)}${V(this.g)}${V(this.b)}`}function q(){const t=X(this.opacity);return`${1===t?"rgb(":"rgba("}${H(this.r)}, ${H(this.g)}, ${H(this.b)}${1===t?")":`, ${t})`}`}function X(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function H(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function V(t){return((t=H(t))<16?"0":"")+t.toString(16)}function G(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new K(t,e,n,r)}function Y(t){if(t instanceof K)return new K(t.h,t.s,t.l,t.opacity);if(t instanceof O||(t=L(t)),!t)return new K;if(t instanceof K)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,o=Math.min(e,n,r),i=Math.max(e,n,r),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(n-r)/u+6*(n<r):n===i?(r-e)/u+2:(e-n)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new K(a,u,c,t.opacity)}function K(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function J(t){return(t=(t||0)%360)<0?t+360:t}function Z(t){return Math.max(0,Math.min(1,t||0))}function Q(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}function tt(t,e,n,r,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*n+(1+3*t+3*i-3*a)*r+a*o)/6}x(O,L,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:B,formatHex:B,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Y(this).formatHsl()},formatRgb:R,toString:R}),x($,F,w(O,{brighter(t){return t=null==t?S:Math.pow(S,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new $(H(this.r),H(this.g),H(this.b),X(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:W,formatHex:W,formatHex8:function(){return`#${V(this.r)}${V(this.g)}${V(this.b)}${V(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:q,toString:q})),x(K,function(t,e,n,r){return 1===arguments.length?Y(t):new K(t,e,n,null==r?1:r)},w(O,{brighter(t){return t=null==t?S:Math.pow(S,t),new K(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new K(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,o=2*n-r;return new $(Q(t>=240?t-240:t+120,o,r),Q(t,o,r),Q(t<120?t+240:t-120,o,r),this.opacity)},clamp(){return new K(J(this.h),Z(this.s),Z(this.l),X(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=X(this.opacity);return`${1===t?"hsl(":"hsla("}${J(this.h)}, ${100*Z(this.s)}%, ${100*Z(this.l)}%${1===t?")":`, ${t})`}`}}));var et=t=>()=>t;function nt(t,e){return function(n){return t+n*e}}function rt(t){return 1===(t=+t)?ot:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):et(isNaN(e)?n:e)}}function ot(t,e){var n=e-t;return n?nt(t,n):et(isNaN(t)?e:t)}var it=function t(e){var n=rt(e);function r(t,e){var r=n((t=F(t)).r,(e=F(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=ot(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return r.gamma=t,r}(1);function at(t){return function(e){var n,r,o=e.length,i=new Array(o),a=new Array(o),u=new Array(o);for(n=0;n<o;++n)r=F(e[n]),i[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return i=t(i),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=i(t),r.g=a(t),r.b=u(t),r+""}}}at(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),o=t[r],i=t[r+1],a=r>0?t[r-1]:2*o-i,u=r<e-1?t[r+2]:2*i-o;return tt((n-r/e)*e,a,o,i,u)}}),at(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),o=t[(r+e-1)%e],i=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return tt((n-r/e)*e,o,i,a,u)}});function ut(t,e){var n,r=e?e.length:0,o=t?Math.min(r,t.length):0,i=new Array(o),a=new Array(r);for(n=0;n<o;++n)i[n]=yt(t[n],e[n]);for(;n<r;++n)a[n]=e[n];return function(t){for(n=0;n<o;++n)a[n]=i[n](t);return a}}function ct(t,e){var n=new Date;return t=+t,e=+e,function(r){return n.setTime(t*(1-r)+e*r),n}}function lt(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function st(t,e){var n,r={},o={};for(n in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)n in t?r[n]=yt(t[n],e[n]):o[n]=e[n];return function(t){for(n in r)o[n]=r[n](t);return o}}var ft=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,pt=new RegExp(ft.source,"g");function ht(t,e){var n,r,o,i=ft.lastIndex=pt.lastIndex=0,a=-1,u=[],c=[];for(t+="",e+="";(n=ft.exec(t))&&(r=pt.exec(e));)(o=r.index)>i&&(o=e.slice(i,o),u[a]?u[a]+=o:u[++a]=o),(n=n[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,c.push({i:a,x:lt(n,r)})),i=pt.lastIndex;return i<e.length&&(o=e.slice(i),u[a]?u[a]+=o:u[++a]=o),u.length<2?c[0]?function(t){return function(e){return t(e)+""}}(c[0].x):function(t){return function(){return t}}(e):(e=c.length,function(t){for(var n,r=0;r<e;++r)u[(n=c[r]).i]=n.x(t);return u.join("")})}function dt(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(n=0;n<r;++n)o[n]=t[n]*(1-i)+e[n]*i;return o}}function yt(t,e){var n,r,o=typeof e;return null==e||"boolean"===o?et(e):("number"===o?lt:"string"===o?(n=L(e))?(e=n,it):ht:e instanceof L?it:e instanceof Date?ct:(r=e,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(e)?ut:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?st:lt:dt))(t,e)}function vt(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}function mt(t){return+t}var gt=[0,1];function bt(t){return t}function xt(t,e){return(e-=t=+t)?function(n){return(n-t)/e}:(n=isNaN(e)?NaN:.5,function(){return n});var n}function wt(t,e,n){var r=t[0],o=t[1],i=e[0],a=e[1];return o<r?(r=xt(o,r),i=n(a,i)):(r=xt(r,o),i=n(i,a)),function(t){return i(r(t))}}function Ot(t,e,n){var r=Math.min(t.length,e.length)-1,o=new Array(r),i=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<r;)o[a]=xt(t[a],t[a+1]),i[a]=n(e[a],e[a+1]);return function(e){var n=b(t,e,1,r)-1;return i[n](o[n](e))}}function jt(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function St(){var t,e,n,r,o,i,a=gt,u=gt,c=yt,l=bt;function s(){var t,e,n,c=Math.min(a.length,u.length);return l!==bt&&(t=a[0],e=a[c-1],t>e&&(n=t,t=e,e=n),l=function(n){return Math.max(t,Math.min(e,n))}),r=c>2?Ot:wt,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?n:(o||(o=r(a.map(t),u,c)))(t(l(e)))}return f.invert=function(n){return l(e((i||(i=r(u,a.map(t),lt)))(n)))},f.domain=function(t){return arguments.length?(a=Array.from(t,mt),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=vt,s()},f.clamp=function(t){return arguments.length?(l=!!t||bt,s()):l!==bt},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(n=t,f):n},function(n,r){return t=n,e=r,s()}}function At(){return St()(bt,bt)}var Et,Pt=n(2261),kt=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Mt(t){if(!(e=kt.exec(t)))throw new Error("invalid format: "+t);var e;return new _t({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function _t(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Tt(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function Ct(t){return(t=Tt(Math.abs(t)))?t[1]:NaN}function It(t,e){var n=Tt(t,e);if(!n)return t+"";var r=n[0],o=n[1];return o<0?"0."+new Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+new Array(o-r.length+2).join("0")}Mt.prototype=_t.prototype,_t.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var Dt={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>It(100*t,e),r:It,s:function(t,e){var n=Tt(t,e);if(!n)return t+"";var r=n[0],o=n[1],i=o-(Et=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=r.length;return i===a?r:i>a?r+new Array(i-a+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+Tt(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Nt(t){return t}var Bt,Rt,Lt,zt=Array.prototype.map,Ut=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Ft(t){var e,n,r=void 0===t.grouping||void 0===t.thousands?Nt:(e=zt.call(t.grouping,Number),n=t.thousands+"",function(t,r){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>r&&(u=Math.max(1,r-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>r));)u=e[a=(a+1)%e.length];return i.reverse().join(n)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?Nt:function(t){return function(e){return e.replace(/[0-9]/g,function(e){return t[+e]})}}(zt.call(t.numerals,String)),c=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Mt(t)).fill,n=t.align,f=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,g=t.type;"n"===g?(y=!0,g="g"):Dt[g]||(void 0===v&&(v=12),m=!0,g="g"),(h||"0"===e&&"="===n)&&(h=!0,e="0",n="=");var b="$"===p?o:"#"===p&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===p?i:/[%p]/.test(g)?c:"",w=Dt[g],O=/[defgprs%]/.test(g);function j(t){var o,i,c,p=b,j=x;if("c"===g)j=w(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,n=t.length,r=1,o=-1;r<n;++r)switch(t[r]){case".":o=e=r;break;case"0":0===o&&(o=r),e=r;break;default:if(!+t[r])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0===+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===g?Ut[8+Et/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(c=t.charCodeAt(o))||c>57){j=(46===c?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}y&&!h&&(t=r(t,1/0));var A=p.length+t.length+j.length,E=A<d?new Array(d-A+1).join(e):"";switch(y&&h&&(t=r(E+t,E.length?d-j.length:1/0),E=""),n){case"<":t=p+t+j+E;break;case"=":t=p+E+t+j;break;case"^":t=E.slice(0,A=E.length>>1)+p+t+j+E.slice(A);break;default:t=E+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var n=f(((t=Mt(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3))),o=Math.pow(10,-r),i=Ut[8+r/3];return function(t){return n(o*t)+i}}}}function $t(t,e,n,r){var o,i=f(t,e,n);switch((r=Mt(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3)))-Ct(Math.abs(t)))}(i,a))||(r.precision=o),Lt(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Ct(e)-Ct(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(r.precision=o-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(o=function(t){return Math.max(0,-Ct(Math.abs(t)))}(i))||(r.precision=o-2*("%"===r.type))}return Rt(r)}function Wt(t){var e=t.domain;return t.ticks=function(t){var n=e();return l(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var r=e();return $t(r[0],r[r.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var r,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],f=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);f-- >0;){if((o=s(c,l,n))===r)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o}r=o}return t},t}function qt(){var t=At();return t.copy=function(){return jt(t,qt())},Pt.C.apply(t,arguments),Wt(t)}function Xt(t){var e;function n(t){return null==t||isNaN(t=+t)?e:t}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,mt),n):t.slice()},n.unknown=function(t){return arguments.length?(e=t,n):e},n.copy=function(){return Xt(t).unknown(e)},t=arguments.length?Array.from(t,mt):[0,1],Wt(n)}function Ht(t,e){var n,r=0,o=(t=t.slice()).length-1,i=t[r],a=t[o];return a<i&&(n=r,r=o,o=n,n=i,i=a,a=n),t[r]=e.floor(i),t[o]=e.ceil(a),t}function Vt(t){return Math.log(t)}function Gt(t){return Math.exp(t)}function Yt(t){return-Math.log(-t)}function Kt(t){return-Math.exp(-t)}function Jt(t){return isFinite(t)?+("1e"+t):t<0?0:t}function Zt(t){return(e,n)=>-t(-e,n)}function Qt(t){const e=t(Vt,Gt),n=e.domain;let r,o,i=10;function a(){return r=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?Jt:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),n()[0]<0?(r=Zt(r),o=Zt(o),t(Yt,Kt)):t(Vt,Gt),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(n(t),a()):n()},e.ticks=t=>{const e=n();let a=e[0],u=e[e.length-1];const c=u<a;c&&([a,u]=[u,a]);let s,f,p=r(a),h=r(u);const d=null==t?10:+t;let y=[];if(!(i%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),a>0){for(;p<=h;++p)for(s=1;s<i;++s)if(f=p<0?s/o(-p):s*o(p),!(f<a)){if(f>u)break;y.push(f)}}else for(;p<=h;++p)for(s=i-1;s>=1;--s)if(f=p>0?s/o(-p):s*o(p),!(f<a)){if(f>u)break;y.push(f)}2*y.length<d&&(y=l(a,u,d))}else y=l(p,h,Math.min(h-p,d)).map(o);return c?y.reverse():y},e.tickFormat=(t,n)=>{if(null==t&&(t=10),null==n&&(n=10===i?"s":","),"function"!=typeof n&&(i%1||null!=(n=Mt(n)).precision||(n.trim=!0),n=Rt(n)),t===1/0)return n;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(r(t)));return e*i<i-.5&&(e*=i),e<=a?n(t):""}},e.nice=()=>n(Ht(n(),{floor:t=>o(Math.floor(r(t))),ceil:t=>o(Math.ceil(r(t)))})),e}function te(){const t=Qt(St()).domain([1,10]);return t.copy=()=>jt(t,te()).base(t.base()),Pt.C.apply(t,arguments),t}function ee(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function ne(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function re(t){var e=1,n=t(ee(e),ne(e));return n.constant=function(n){return arguments.length?t(ee(e=+n),ne(e)):e},Wt(n)}function oe(){var t=re(St());return t.copy=function(){return jt(t,oe()).constant(t.constant())},Pt.C.apply(t,arguments)}Bt=Ft({thousands:",",grouping:[3],currency:["$",""]}),Rt=Bt.format,Lt=Bt.formatPrefix;var ie=n(6434);function ae(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ue(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ce(t){return t<0?-t*t:t*t}function le(t){var e=t(bt,bt),n=1;return e.exponent=function(e){return arguments.length?1===(n=+e)?t(bt,bt):.5===n?t(ue,ce):t(ae(n),ae(1/n)):n},Wt(e)}function se(){var t=le(St());return t.copy=function(){return jt(t,se()).exponent(t.exponent())},Pt.C.apply(t,arguments),t}function fe(){return se.apply(null,arguments).exponent(.5)}function pe(t){return Math.sign(t)*t*t}function he(){var t,e=At(),n=[0,1],r=!1;function o(n){var o=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(e(n));return isNaN(o)?t:r?Math.round(o):o}return o.invert=function(t){return e.invert(pe(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((n=Array.from(t,mt)).map(pe)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(r=!!t,o):r},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return he(e.domain(),n).round(r).clamp(e.clamp()).unknown(t)},Pt.C.apply(o,arguments),Wt(o)}function de(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let o of t)null!=(o=e(o,++r,t))&&(n<o||void 0===n&&o>=o)&&(n=o)}return n}function ye(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let o of t)null!=(o=e(o,++r,t))&&(n>o||void 0===n&&o>=o)&&(n=o)}return n}function ve(t=p){if(t===p)return me;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,n)=>{const r=t(e,n);return r||0===r?r:(0===t(n,n))-(0===t(e,e))}}function me(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function ge(t,e,n=0,r=1/0,o){if(e=Math.floor(e),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(t.length-1,r)),!(n<=e&&e<=r))return t;for(o=void 0===o?me:ve(o);r>n;){if(r-n>600){const i=r-n+1,a=e-n+1,u=Math.log(i),c=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*c*(i-c)/i)*(a-i/2<0?-1:1);ge(t,e,Math.max(n,Math.floor(e-a*c/i+l)),Math.min(r,Math.floor(e+(i-a)*c/i+l)),o)}const i=t[e];let a=n,u=r;for(be(t,n,e),o(t[r],i)>0&&be(t,n,r);a<u;){for(be(t,a,u),++a,--u;o(t[a],i)<0;)++a;for(;o(t[u],i)>0;)--u}0===o(t[n],i)?be(t,n,u):(++u,be(t,u,r)),u<=e&&(n=u+1),e<=u&&(r=u-1)}return t}function be(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function xe(t,e,n){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let n=-1;for(let r of t)null!=(r=e(r,++n,t))&&(r=+r)>=r&&(yield r)}}(t,n)),(r=t.length)&&!isNaN(e=+e)){if(e<=0||r<2)return ye(t);if(e>=1)return de(t);var r,o=(r-1)*e,i=Math.floor(o),a=de(ge(t,i).subarray(0,i+1));return a+(ye(t.subarray(i+1))-a)*(o-i)}}function we(t,e,n=v){if((r=t.length)&&!isNaN(e=+e)){if(e<=0||r<2)return+n(t[0],0,t);if(e>=1)return+n(t[r-1],r-1,t);var r,o=(r-1)*e,i=Math.floor(o),a=+n(t[i],i,t);return a+(+n(t[i+1],i+1,t)-a)*(o-i)}}function Oe(){var t,e=[],n=[],r=[];function o(){var t=0,o=Math.max(1,n.length);for(r=new Array(o-1);++t<o;)r[t-1]=we(e,t/o);return i}function i(e){return null==e||isNaN(e=+e)?t:n[b(r,e)]}return i.invertExtent=function(t){var o=n.indexOf(t);return o<0?[NaN,NaN]:[o>0?r[o-1]:e[0],o<r.length?r[o]:e[e.length-1]]},i.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let n of t)null==n||isNaN(n=+n)||e.push(n);return e.sort(p),o()},i.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.quantiles=function(){return r.slice()},i.copy=function(){return Oe().domain(e).range(n).unknown(t)},Pt.C.apply(i,arguments)}function je(){var t,e=0,n=1,r=1,o=[.5],i=[0,1];function a(e){return null!=e&&e<=e?i[b(o,e,0,r)]:t}function u(){var t=-1;for(o=new Array(r);++t<r;)o[t]=((t+1)*n-(t-r)*e)/(r+1);return a}return a.domain=function(t){return arguments.length?([e,n]=t,e=+e,n=+n,u()):[e,n]},a.range=function(t){return arguments.length?(r=(i=Array.from(t)).length-1,u()):i.slice()},a.invertExtent=function(t){var a=i.indexOf(t);return a<0?[NaN,NaN]:a<1?[e,o[0]]:a>=r?[o[r-1],n]:[o[a-1],o[a]]},a.unknown=function(e){return arguments.length?(t=e,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return je().domain([e,n]).range(i).unknown(t)},Pt.C.apply(Wt(a),arguments)}function Se(){var t,e=[.5],n=[0,1],r=1;function o(o){return null!=o&&o<=o?n[b(e,o,0,r)]:t}return o.domain=function(t){return arguments.length?(e=Array.from(t),r=Math.min(e.length,n.length-1),o):e.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),r=Math.min(e.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var r=n.indexOf(t);return[e[r-1],e[r]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return Se().domain(e).range(n).unknown(t)},Pt.C.apply(o,arguments)}const Ae=1e3,Ee=6e4,Pe=36e5,ke=864e5,Me=6048e5,_e=2592e6,Te=31536e6,Ce=new Date,Ie=new Date;function De(t,e,n,r){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=n=>(t(n=new Date(n-1)),e(n,1),t(n),n),o.round=t=>{const e=o(t),n=o.ceil(t);return t-e<n-t?e:n},o.offset=(t,n)=>(e(t=new Date(+t),null==n?1:Math.floor(n)),t),o.range=(n,r,i)=>{const a=[];if(n=o.ceil(n),i=null==i?1:Math.floor(i),!(n<r&&i>0))return a;let u;do{a.push(u=new Date(+n)),e(n,i),t(n)}while(u<n&&n<r);return a},o.filter=n=>De(e=>{if(e>=e)for(;t(e),!n(e);)e.setTime(e-1)},(t,r)=>{if(t>=t)if(r<0)for(;++r<=0;)for(;e(t,-1),!n(t););else for(;--r>=0;)for(;e(t,1),!n(t););}),n&&(o.count=(e,r)=>(Ce.setTime(+e),Ie.setTime(+r),t(Ce),t(Ie),Math.floor(n(Ce,Ie))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(r?e=>r(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const Ne=De(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Ne.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?De(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):Ne:null);Ne.range;const Be=De(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*Ae)},(t,e)=>(e-t)/Ae,t=>t.getUTCSeconds()),Re=(Be.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Ae)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getMinutes())),Le=(Re.range,De(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getUTCMinutes())),ze=(Le.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Ae-t.getMinutes()*Ee)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getHours())),Ue=(ze.range,De(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getUTCHours())),Fe=(Ue.range,De(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Ee)/ke,t=>t.getDate()-1)),$e=(Fe.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>t.getUTCDate()-1)),We=($e.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>Math.floor(t/ke)));We.range;function qe(t){return De(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Ee)/Me)}const Xe=qe(0),He=qe(1),Ve=qe(2),Ge=qe(3),Ye=qe(4),Ke=qe(5),Je=qe(6);Xe.range,He.range,Ve.range,Ge.range,Ye.range,Ke.range,Je.range;function Ze(t){return De(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/Me)}const Qe=Ze(0),tn=Ze(1),en=Ze(2),nn=Ze(3),rn=Ze(4),on=Ze(5),an=Ze(6),un=(Qe.range,tn.range,en.range,nn.range,rn.range,on.range,an.range,De(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear()),t=>t.getMonth())),cn=(un.range,De(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth())),ln=(cn.range,De(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear()));ln.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)}):null;ln.range;const sn=De(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());sn.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)}):null;sn.range;function fn(t,e,n,r,o,i){const a=[[Be,1,Ae],[Be,5,5e3],[Be,15,15e3],[Be,30,3e4],[i,1,Ee],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Pe],[o,3,108e5],[o,6,216e5],[o,12,432e5],[r,1,ke],[r,2,1728e5],[n,1,Me],[e,1,_e],[e,3,7776e6],[t,1,Te]];function u(e,n,r){const o=Math.abs(n-e)/r,i=d(([,,t])=>t).right(a,o);if(i===a.length)return t.every(f(e/Te,n/Te,r));if(0===i)return Ne.every(Math.max(f(e,n,r),1));const[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,n){const r=e<t;r&&([t,e]=[e,t]);const o=n&&"function"==typeof n.range?n:u(t,e,n),i=o?o.range(t,+e+1):[];return r?i.reverse():i},u]}const[pn,hn]=fn(sn,cn,Qe,We,Ue,Le),[dn,yn]=fn(ln,un,Xe,Fe,ze,Re);function vn(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function mn(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function gn(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}var bn,xn,wn,On={"-":"",_:" ",0:"0"},jn=/^\s*\d+/,Sn=/^%/,An=/[\\^$*+?|[\]().{}]/g;function En(t,e,n){var r=t<0?"-":"",o=(r?-t:t)+"",i=o.length;return r+(i<n?new Array(n-i+1).join(e)+o:o)}function Pn(t){return t.replace(An,"\\$&")}function kn(t){return new RegExp("^(?:"+t.map(Pn).join("|")+")","i")}function Mn(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function _n(t,e,n){var r=jn.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Tn(t,e,n){var r=jn.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Cn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function In(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function Dn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Nn(t,e,n){var r=jn.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function Bn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Rn(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Ln(t,e,n){var r=jn.exec(e.slice(n,n+1));return r?(t.q=3*r[0]-3,n+r[0].length):-1}function zn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Un(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Fn(t,e,n){var r=jn.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function $n(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Wn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function qn(t,e,n){var r=jn.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function Xn(t,e,n){var r=jn.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function Hn(t,e,n){var r=jn.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Vn(t,e,n){var r=Sn.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function Gn(t,e,n){var r=jn.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function Yn(t,e,n){var r=jn.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Kn(t,e){return En(t.getDate(),e,2)}function Jn(t,e){return En(t.getHours(),e,2)}function Zn(t,e){return En(t.getHours()%12||12,e,2)}function Qn(t,e){return En(1+Fe.count(ln(t),t),e,3)}function tr(t,e){return En(t.getMilliseconds(),e,3)}function er(t,e){return tr(t,e)+"000"}function nr(t,e){return En(t.getMonth()+1,e,2)}function rr(t,e){return En(t.getMinutes(),e,2)}function or(t,e){return En(t.getSeconds(),e,2)}function ir(t){var e=t.getDay();return 0===e?7:e}function ar(t,e){return En(Xe.count(ln(t)-1,t),e,2)}function ur(t){var e=t.getDay();return e>=4||0===e?Ye(t):Ye.ceil(t)}function cr(t,e){return t=ur(t),En(Ye.count(ln(t),t)+(4===ln(t).getDay()),e,2)}function lr(t){return t.getDay()}function sr(t,e){return En(He.count(ln(t)-1,t),e,2)}function fr(t,e){return En(t.getFullYear()%100,e,2)}function pr(t,e){return En((t=ur(t)).getFullYear()%100,e,2)}function hr(t,e){return En(t.getFullYear()%1e4,e,4)}function dr(t,e){var n=t.getDay();return En((t=n>=4||0===n?Ye(t):Ye.ceil(t)).getFullYear()%1e4,e,4)}function yr(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+En(e/60|0,"0",2)+En(e%60,"0",2)}function vr(t,e){return En(t.getUTCDate(),e,2)}function mr(t,e){return En(t.getUTCHours(),e,2)}function gr(t,e){return En(t.getUTCHours()%12||12,e,2)}function br(t,e){return En(1+$e.count(sn(t),t),e,3)}function xr(t,e){return En(t.getUTCMilliseconds(),e,3)}function wr(t,e){return xr(t,e)+"000"}function Or(t,e){return En(t.getUTCMonth()+1,e,2)}function jr(t,e){return En(t.getUTCMinutes(),e,2)}function Sr(t,e){return En(t.getUTCSeconds(),e,2)}function Ar(t){var e=t.getUTCDay();return 0===e?7:e}function Er(t,e){return En(Qe.count(sn(t)-1,t),e,2)}function Pr(t){var e=t.getUTCDay();return e>=4||0===e?rn(t):rn.ceil(t)}function kr(t,e){return t=Pr(t),En(rn.count(sn(t),t)+(4===sn(t).getUTCDay()),e,2)}function Mr(t){return t.getUTCDay()}function _r(t,e){return En(tn.count(sn(t)-1,t),e,2)}function Tr(t,e){return En(t.getUTCFullYear()%100,e,2)}function Cr(t,e){return En((t=Pr(t)).getUTCFullYear()%100,e,2)}function Ir(t,e){return En(t.getUTCFullYear()%1e4,e,4)}function Dr(t,e){var n=t.getUTCDay();return En((t=n>=4||0===n?rn(t):rn.ceil(t)).getUTCFullYear()%1e4,e,4)}function Nr(){return"+0000"}function Br(){return"%"}function Rr(t){return+t}function Lr(t){return Math.floor(+t/1e3)}function zr(t){return new Date(t)}function Ur(t){return t instanceof Date?+t:+new Date(+t)}function Fr(t,e,n,r,o,i,a,u,c,l){var s=At(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),g=l("%b %d"),b=l("%B"),x=l("%Y");function w(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:r(t)<t?o(t)<t?m:g:n(t)<t?b:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,Ur)):p().map(zr)},s.ticks=function(e){var n=p();return t(n[0],n[n.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var n=p();return t&&"function"==typeof t.range||(t=e(n[0],n[n.length-1],null==t?10:t)),t?p(Ht(n,t)):s},s.copy=function(){return jt(s,Fr(t,e,n,r,o,i,a,u,c,l))},s}function $r(){return Pt.C.apply(Fr(dn,yn,ln,un,Xe,Fe,ze,Re,Be,xn).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Wr(){return Pt.C.apply(Fr(pn,hn,sn,cn,Qe,$e,Ue,Le,Be,wn).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function qr(){var t,e,n,r,o,i=0,a=1,u=bt,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===n?.5:(e=(r(e)-t)*n,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var n,r;return arguments.length?([n,r]=e,u=t(n,r),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=r(i=+i),e=r(a=+a),n=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(yt),l.rangeRound=s(vt),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return r=o,t=o(i),e=o(a),n=t===e?0:1/(e-t),l}}function Xr(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Hr(){var t=Wt(qr()(bt));return t.copy=function(){return Xr(t,Hr())},Pt.K.apply(t,arguments)}function Vr(){var t=Qt(qr()).domain([1,10]);return t.copy=function(){return Xr(t,Vr()).base(t.base())},Pt.K.apply(t,arguments)}function Gr(){var t=re(qr());return t.copy=function(){return Xr(t,Gr()).constant(t.constant())},Pt.K.apply(t,arguments)}function Yr(){var t=le(qr());return t.copy=function(){return Xr(t,Yr()).exponent(t.exponent())},Pt.K.apply(t,arguments)}function Kr(){return Yr.apply(null,arguments).exponent(.5)}function Jr(){var t=[],e=bt;function n(n){if(null!=n&&!isNaN(n=+n))return e((b(t,n,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let n of e)null==n||isNaN(n=+n)||t.push(n);return t.sort(p),n},n.interpolator=function(t){return arguments.length?(e=t,n):e},n.range=function(){return t.map((n,r)=>e(r/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(n,r)=>xe(t,r/e))},n.copy=function(){return Jr(e).domain(t)},Pt.K.apply(n,arguments)}function Zr(){var t,e,n,r,o,i,a,u=0,c=.5,l=1,s=1,f=bt,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?r:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var n,r,o;return arguments.length?([n,r,o]=e,f=function(t,e){void 0===e&&(e=t,t=yt);for(var n=0,r=e.length-1,o=e[0],i=new Array(r<0?0:r);n<r;)i[n]=t(o,o=e[++n]);return function(t){var e=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return i[e](t-e)}}(t,[n,r,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),n=i(l=+l),r=t===e?0:.5/(e-t),o=e===n?0:.5/(n-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(yt),h.rangeRound=d(vt),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),n=a(l),r=t===e?0:.5/(e-t),o=e===n?0:.5/(n-e),s=e<t?-1:1,h}}function Qr(){var t=Wt(Zr()(bt));return t.copy=function(){return Xr(t,Qr())},Pt.K.apply(t,arguments)}function to(){var t=Qt(Zr()).domain([.1,1,10]);return t.copy=function(){return Xr(t,to()).base(t.base())},Pt.K.apply(t,arguments)}function eo(){var t=re(Zr());return t.copy=function(){return Xr(t,eo()).constant(t.constant())},Pt.K.apply(t,arguments)}function no(){var t=le(Zr());return t.copy=function(){return Xr(t,no()).exponent(t.exponent())},Pt.K.apply(t,arguments)}function ro(){return no.apply(null,arguments).exponent(.5)}function oo(t,e){if((o=t.length)>1)for(var n,r,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(r=a,a=t[e[i]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(r[n][1])?r[n][0]:r[n][1]}!function(t){bn=function(t){var e=t.dateTime,n=t.date,r=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=kn(o),s=Mn(o),f=kn(i),p=Mn(i),h=kn(a),d=Mn(a),y=kn(u),v=Mn(u),m=kn(c),g=Mn(c),b={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:Kn,e:Kn,f:er,g:pr,G:dr,H:Jn,I:Zn,j:Qn,L:tr,m:nr,M:rr,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Rr,s:Lr,S:or,u:ir,U:ar,V:cr,w:lr,W:sr,x:null,X:null,y:fr,Y:hr,Z:yr,"%":Br},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:vr,e:vr,f:wr,g:Cr,G:Dr,H:mr,I:gr,j:br,L:xr,m:Or,M:jr,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Rr,s:Lr,S:Sr,u:Ar,U:Er,V:kr,w:Mr,W:_r,x:null,X:null,y:Tr,Y:Ir,Z:Nr,"%":Br},w={a:function(t,e,n){var r=h.exec(e.slice(n));return r?(t.w=d.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(t,e,n){var r=f.exec(e.slice(n));return r?(t.w=p.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(t,e,n){var r=m.exec(e.slice(n));return r?(t.m=g.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(t,e,n){var r=y.exec(e.slice(n));return r?(t.m=v.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(t,n,r){return S(t,e,n,r)},d:Un,e:Un,f:Hn,g:Bn,G:Nn,H:$n,I:$n,j:Fn,L:Xn,m:zn,M:Wn,p:function(t,e,n){var r=l.exec(e.slice(n));return r?(t.p=s.get(r[0].toLowerCase()),n+r[0].length):-1},q:Ln,Q:Gn,s:Yn,S:qn,u:Tn,U:Cn,V:In,w:_n,W:Dn,x:function(t,e,r){return S(t,n,e,r)},X:function(t,e,n){return S(t,r,e,n)},y:Bn,Y:Nn,Z:Rn,"%":Vn};function O(t,e){return function(n){var r,o,i,a=[],u=-1,c=0,l=t.length;for(n instanceof Date||(n=new Date(+n));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=On[r=t.charAt(++u)])?r=t.charAt(++u):o="e"===r?" ":"0",(i=e[r])&&(r=i(n,o)),a.push(r),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(n){var r,o,i=gn(1900,void 0,1);if(S(i,t,n+="",0)!=n.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(r=mn(gn(i.y,0,1))).getUTCDay(),r=o>4||0===o?tn.ceil(r):tn(r),r=$e.offset(r,7*(i.V-1)),i.y=r.getUTCFullYear(),i.m=r.getUTCMonth(),i.d=r.getUTCDate()+(i.w+6)%7):(o=(r=vn(gn(i.y,0,1))).getDay(),r=o>4||0===o?He.ceil(r):He(r),r=Fe.offset(r,7*(i.V-1)),i.y=r.getFullYear(),i.m=r.getMonth(),i.d=r.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?mn(gn(i.y,0,1)).getUTCDay():vn(gn(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,mn(i)):vn(i)}}function S(t,e,n,r){for(var o,i,a=0,u=e.length,c=n.length;a<u;){if(r>=c)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=w[o in On?e.charAt(a++):o])||(r=i(t,n,r))<0)return-1}else if(o!=n.charCodeAt(r++))return-1}return r}return b.x=O(n,b),b.X=O(r,b),b.c=O(e,b),x.x=O(n,x),x.X=O(r,x),x.c=O(e,x),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),xn=bn.format,bn.parse,wn=bn.utcFormat,bn.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var io=n(5753),ao=n(9886);function uo(t){for(var e=t.length,n=new Array(e);--e>=0;)n[e]=e;return n}function co(t,e){return t[e]}function lo(t){const e=[];return e.key=t,e}var so=n(7156),fo=n.n(so),po=n(8554),ho=n.n(po),yo=n(2381),vo=n.n(yo),mo=n(4360),go=n.n(mo),bo=n(7561),xo=n.n(bo),wo=n(9650),Oo=n.n(wo),jo=n(9089),So=n.n(jo),Ao=n(3859),Eo=n.n(Ao),Po=n(4254),ko=n.n(Po),Mo=n(8434),_o=n.n(Mo),To=n(7493),Co=n.n(To),Io=n(5169),Do=n.n(Io);function No(t){return function(t){if(Array.isArray(t))return Bo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Bo(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Bo(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bo(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Ro=function(t){return t},Lo={"@@functional/placeholder":!0},zo=function(t){return t===Lo},Uo=function(t){return function e(){return 0===arguments.length||1===arguments.length&&zo(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Fo=function t(e,n){return 1===e?n:Uo(function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==Lo}).length;return a>=e?n.apply(void 0,o):t(e-a,Uo(function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var i=o.map(function(t){return zo(t)?e.shift():t});return n.apply(void 0,No(i).concat(e))}))})},$o=function(t){return Fo(t.length,t)},Wo=function(t,e){for(var n=[],r=t;r<e;++r)n[r-t]=r;return n},qo=$o(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),Xo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(!e.length)return Ro;var r=e.reverse(),o=r[0],i=r.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},Ho=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Vo=function(t){var e=null,n=null;return function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e&&o.every(function(t,n){return t===e[n]})?n:(e=o,n=t.apply(void 0,o))}};var Go={rangeStep:function(t,e,n){for(var r=new(Do())(t),o=0,i=[];r.lt(e)&&o<1e5;)i.push(r.toNumber()),r=r.add(n),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(Do())(t).abs().log(10).toNumber())+1},interpolateNumber:$o(function(t,e,n){var r=+t;return r+n*(+e-r)}),uninterpolateNumber:$o(function(t,e,n){var r=e-+t;return(n-t)/(r=r||1/0)}),uninterpolateTruncation:$o(function(t,e,n){var r=e-+t;return r=r||1/0,Math.max(0,Math.min(1,(n-t)/r))})};function Yo(t){return function(t){if(Array.isArray(t))return Zo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Jo(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ko(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(r=(a=u.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(t){o=!0,i=t}finally{try{r||null==u.return||u.return()}finally{if(o)throw i}}return n}(t,e)||Jo(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jo(t,e){if(t){if("string"==typeof t)return Zo(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Zo(t,e):void 0}}function Zo(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Qo(t){var e=Ko(t,2),n=e[0],r=e[1],o=n,i=r;return n>r&&(o=r,i=n),[o,i]}function ti(t,e,n){if(t.lte(0))return new(Do())(0);var r=Go.getDigitCount(t.toNumber()),o=new(Do())(10).pow(r),i=t.div(o),a=1!==r?.05:.1,u=new(Do())(Math.ceil(i.div(a).toNumber())).add(n).mul(a).mul(o);return e?u:new(Do())(Math.ceil(u))}function ei(t,e,n){var r=1,o=new(Do())(t);if(!o.isint()&&n){var i=Math.abs(t);i<1?(r=new(Do())(10).pow(Go.getDigitCount(t)-1),o=new(Do())(Math.floor(o.div(r).toNumber())).mul(r)):i>1&&(o=new(Do())(Math.floor(t)))}else 0===t?o=new(Do())(Math.floor((e-1)/2)):n||(o=new(Do())(Math.floor(t)));var a=Math.floor((e-1)/2);return Xo(qo(function(t){return o.add(new(Do())(t-a).mul(r)).toNumber()}),Wo)(0,e)}function ni(t,e,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(n-1)))return{step:new(Do())(0),tickMin:new(Do())(0),tickMax:new(Do())(0)};var i,a=ti(new(Do())(e).sub(t).div(n-1),r,o);i=t<=0&&e>=0?new(Do())(0):(i=new(Do())(t).add(e).div(2)).sub(new(Do())(i).mod(a));var u=Math.ceil(i.sub(t).div(a).toNumber()),c=Math.ceil(new(Do())(e).sub(i).div(a).toNumber()),l=u+c+1;return l>n?ni(t,e,n,r,o+1):(l<n&&(c=e>0?c+(n-l):c,u=e>0?u:u+(n-l)),{step:a,tickMin:i.sub(new(Do())(u).mul(a)),tickMax:i.add(new(Do())(c).mul(a))})}var ri=Vo(function(t){var e=Ko(t,2),n=e[0],r=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),u=Ko(Qo([n,r]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(Yo(Wo(0,o-1).map(function(){return 1/0}))):[].concat(Yo(Wo(0,o-1).map(function(){return-1/0})),[l]);return n>r?Ho(s):s}if(c===l)return ei(c,o,i);var f=ni(c,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=Go.rangeStep(h,d.add(new(Do())(.1).mul(p)),p);return n>r?Ho(y):y}),oi=(Vo(function(t){var e=Ko(t,2),n=e[0],r=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),u=Ko(Qo([n,r]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[n,r];if(c===l)return ei(c,o,i);var s=ti(new(Do())(l).sub(c).div(a-1),i,0),f=Xo(qo(function(t){return new(Do())(c).add(new(Do())(t).mul(s)).toNumber()}),Wo)(0,a).filter(function(t){return t>=c&&t<=l});return n>r?Ho(f):f}),Vo(function(t,e){var n=Ko(t,2),r=n[0],o=n[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Ko(Qo([r,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[r,o];if(u===c)return[u];var l=Math.max(e,2),s=ti(new(Do())(c).sub(u).div(l-1),i,0),f=[].concat(Yo(Go.rangeStep(new(Do())(u),new(Do())(c).sub(new(Do())(.99).mul(s)),s)),[c]);return r>o?Ho(f):f})),ii=n(6352),ai=n(186),ui=n(6075),ci=n(6678);function li(t){return li="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(t)}function si(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function fi(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?si(Object(n),!0).forEach(function(e){pi(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):si(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function pi(t,e,n){var r;return r=function(t,e){if("object"!=li(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=li(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==li(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function hi(t){return function(t){if(Array.isArray(t))return di(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return di(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return di(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function di(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function yi(t,e,n){return vo()(t)||vo()(e)?n:(0,ai.vh)(e)?Oo()(t,e,n):go()(e)?e(t):n}function vi(t,e,n,r){var o=So()(t,function(t){return yi(t,e)});if("number"===n){var i=o.filter(function(t){return(0,ai.Et)(t)||parseFloat(t)});return i.length?[ho()(i),fo()(i)]:[1/0,-1/0]}return(r?o.filter(function(t){return!vo()(t)}):o).map(function(t){return(0,ai.vh)(t)||t instanceof Date?t:""})}var mi=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==n?void 0:n.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var u=o.range,c=0;c<a;c++){var l=c>0?r[c-1].coordinate:r[a-1].coordinate,s=r[c].coordinate,f=c>=a-1?r[0].coordinate:r[c+1].coordinate,p=void 0;if((0,ai.sA)(s-l)!==(0,ai.sA)(f-s)){var h=[];if((0,ai.sA)(f-s)===(0,ai.sA)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=r[c].index;break}}else{var m=Math.min(l,f),g=Math.max(l,f);if(t>(m+s)/2&&t<=(g+s)/2){i=r[c].index;break}}}else for(var b=0;b<a;b++)if(0===b&&t<=(n[b].coordinate+n[b+1].coordinate)/2||b>0&&b<a-1&&t>(n[b].coordinate+n[b-1].coordinate)/2&&t<=(n[b].coordinate+n[b+1].coordinate)/2||b===a-1&&t>(n[b].coordinate+n[b-1].coordinate)/2){i=n[b].index;break}return i},gi=function(t){var e,n=t.type.displayName,r=t.props,o=r.stroke,i=r.fill;switch(n){case"Line":e=o;break;case"Area":case"Radar":e=o&&"none"!==o?o:i;break;default:e=i}return e},bi=function(t){var e=t.barSize,n=t.totalSize,r=t.stackGroups,o=void 0===r?{}:r;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,ui.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].props.barSize,g=v[0].props[y];i[g]||(i[g]=[]);var b=vo()(m)?e:m;i[g].push({item:v[0],stackList:v.slice(1),barSize:vo()(b)?void 0:(0,ai.F4)(b,n,0)})}}return i},xi=function(t){var e=t.barGap,n=t.barCategoryGap,r=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,u=i.length;if(u<1)return null;var c,l=(0,ai.F4)(e,r,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=r/u,h=i.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=r&&(h-=(u-1)*l,l=0),h>=r&&p>0&&(f=!0,h=u*(p*=.9));var d={offset:((r-h)/2|0)-l,size:0};c=i.reduce(function(t,e){var n={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},r=[].concat(hi(t),[n]);return d=r[r.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){r.push({item:t,position:d})}),r},s)}else{var y=(0,ai.F4)(n,r,0,!0);r-2*y-(u-1)*l<=0&&(l=0);var v=(r-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;c=i.reduce(function(t,e,n){var r=[].concat(hi(t),[{item:e.item,position:{offset:y+(v+l)*n+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){r.push({item:t,position:r[r.length-1].position})}),r},s)}return c},wi=function(t,e,n,r){var o=n.children,i=n.width,a=n.margin,u=i-(a.left||0)-(a.right||0),c=(0,ci.g)({children:o,legendWidth:u});if(c){var l=r||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,ai.Et)(t[p]))return fi(fi({},t),{},pi({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,ai.Et)(t[h]))return fi(fi({},t),{},pi({},h,t[h]+(f||0)))}return t},Oi=function(t,e,n,r,o){var i=e.props.children,a=(0,ui.aS)(i,ii.u).filter(function(t){return function(t,e,n){return!!vo()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===n?"xAxis"===e:"y"!==n||"yAxis"===e)}(r,o,t.props.direction)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var r=yi(e,n);if(vo()(r))return t;var o=Array.isArray(r)?[ho()(r),fo()(r)]:[r,r],i=u.reduce(function(t,n){var r=yi(e,n,0),i=o[0]-Math.abs(Array.isArray(r)?r[0]:r),a=o[1]+Math.abs(Array.isArray(r)?r[1]:r);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},ji=function(t,e,n,r,o){var i=e.map(function(e){return Oi(t,e,n,o,r)}).filter(function(t){return!vo()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},Si=function(t,e,n,r,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===n&&i&&Oi(t,e,i,r)||vi(t,i,n,o)});if("number"===n)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var n=0,r=e.length;n<r;n++)a[e[n]]||(a[e[n]]=!0,t.push(e[n]));return t},[])},Ai=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Ei=function(t,e,n,r){if(r)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===n&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(n),a},Pi=function(t,e,n){if(!t)return null;var r=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?r.bandwidth()/2:2,c=(e||n)&&"category"===i&&r.bandwidth?r.bandwidth()/u:0;return c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,ai.sA)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map(function(t){var e=o?o.indexOf(t):t;return{coordinate:r(e)+c,value:t,offset:c}}).filter(function(t){return!Eo()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:r(t)+c,value:t,index:e,offset:c}}):r.ticks&&!n?r.ticks(t.tickCount).map(function(t){return{coordinate:r(t)+c,value:t,offset:c}}):r.domain().map(function(t,e){return{coordinate:r(t)+c,value:o?o[t]:t,index:e,offset:c}})},ki=new WeakMap,Mi=function(t,e){if("function"!=typeof e)return t;ki.has(t)||ki.set(t,new WeakMap);var n=ki.get(t);if(n.has(e))return n.get(e);var r=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return n.set(e,r),r},_i=function(t,e,n){var i=t.scale,a=t.type,u=t.layout,c=t.axisType;if("auto"===i)return"radial"===u&&"radiusAxis"===c?{scale:o.A(),realScaleType:"band"}:"radial"===u&&"angleAxis"===c?{scale:qt(),realScaleType:"linear"}:"category"===a&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!n)?{scale:o.z(),realScaleType:"point"}:"category"===a?{scale:o.A(),realScaleType:"band"}:{scale:qt(),realScaleType:"linear"};if(xo()(i)){var l="scale".concat(ko()(i));return{scale:(r[l]||o.z)(),realScaleType:r[l]?l:"point"}}return go()(i)?{scale:i}:{scale:o.z(),realScaleType:"point"}},Ti=1e-4,Ci=function(t){var e=t.domain();if(e&&!(e.length<=2)){var n=e.length,r=t.range(),o=Math.min(r[0],r[1])-Ti,i=Math.max(r[0],r[1])+Ti,a=t(e[0]),u=t(e[n-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[n-1]])}},Ii=function(t,e){if(!t)return null;for(var n=0,r=t.length;n<r;n++)if(t[n].item===e)return t[n].position;return null},Di=function(t,e){if(!e||2!==e.length||!(0,ai.Et)(e[0])||!(0,ai.Et)(e[1]))return t;var n=Math.min(e[0],e[1]),r=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,ai.Et)(t[0])||t[0]<n)&&(o[0]=n),(!(0,ai.Et)(t[1])||t[1]>r)&&(o[1]=r),o[0]>r&&(o[0]=r),o[1]<n&&(o[1]=n),o},Ni={sign:function(t){var e=t.length;if(!(e<=0))for(var n=0,r=t[0].length;n<r;++n)for(var o=0,i=0,a=0;a<e;++a){var u=Eo()(t[a][n][1])?t[a][n][0]:t[a][n][1];u>=0?(t[a][n][0]=o,t[a][n][1]=o+u,o=t[a][n][1]):(t[a][n][0]=i,t[a][n][1]=i+u,i=t[a][n][1])}},expand:function(t,e){if((r=t.length)>0){for(var n,r,o,i=0,a=t[0].length;i<a;++i){for(o=n=0;n<r;++n)o+=t[n][i][1]||0;if(o)for(n=0;n<r;++n)t[n][i][1]/=o}oo(t,e)}},none:oo,silhouette:function(t,e){if((n=t.length)>0){for(var n,r=0,o=t[e[0]],i=o.length;r<i;++r){for(var a=0,u=0;a<n;++a)u+=t[a][r][1]||0;o[r][1]+=o[r][0]=-u/2}oo(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(r=(n=t[e[0]]).length)>0){for(var n,r,o,i=0,a=1;a<r;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}n[a-1][1]+=n[a-1][0]=i,c&&(i-=l/c)}n[a-1][1]+=n[a-1][0]=i,oo(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var n=0,r=t[0].length;n<r;++n)for(var o=0,i=0;i<e;++i){var a=Eo()(t[i][n][1])?t[i][n][0]:t[i][n][1];a>=0?(t[i][n][0]=o,t[i][n][1]=o+a,o=t[i][n][1]):(t[i][n][0]=0,t[i][n][1]=0)}}},Bi=function(t,e,n){var r=e.map(function(t){return t.props.dataKey}),o=Ni[n],i=function(){var t=(0,ao.A)([]),e=uo,n=oo,r=co;function o(o){var i,a,u=Array.from(t.apply(this,arguments),lo),c=u.length,l=-1;for(const t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+r(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,io.A)(e(u));i<c;++i)u[a[i]].index=i;return n(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,ao.A)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(r="function"==typeof t?t:(0,ao.A)(+t),o):r},o.order=function(t){return arguments.length?(e=null==t?uo:"function"==typeof t?t:(0,ao.A)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(n=null==t?oo:t,o):n},o}().keys(r).value(function(t,e){return+yi(t,e,0)}).order(uo).offset(o);return i(t)},Ri=function(t,e,n,r,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o=e.props,i=o.stackId;if(o.hide)return t;var a=e.props[n],u=t[a]||{hasStack:!1,stackGroups:{}};if((0,ai.vh)(i)){var c=u.stackGroups[i]||{numericAxisId:n,cateAxisId:r,items:[]};c.items.push(e),u.hasStack=!0,u.stackGroups[i]=c}else u.stackGroups[(0,ai.NF)("_stackId_")]={numericAxisId:n,cateAxisId:r,items:[e]};return fi(fi({},t),{},pi({},a,u))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];if(u.hasStack){u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return fi(fi({},e),{},pi({},i,{numericAxisId:n,cateAxisId:r,items:a.items,stackedData:Bi(t,a.items,o)}))},{})}return fi(fi({},e),{},pi({},i,u))},{})},Li=function(t,e){var n=e.realScaleType,r=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=n||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===r&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=ri(c,o,a);return t.domain([ho()(l),fo()(l)]),{niceTicks:l}}if(o&&"number"===r){var s=t.domain();return{niceTicks:oi(s,o,a)}}return null};function zi(t){var e=t.axis,n=t.ticks,r=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!vo()(o[e.dataKey])){var u=(0,ai.eP)(n,"value",o[e.dataKey]);if(u)return u.coordinate+r/2}return n[i]?n[i].coordinate+r/2:null}var c=yi(o,vo()(a)?e.dataKey:a);return vo()(c)?null:e.scale(c)}var Ui=function(t){var e=t.axis,n=t.ticks,r=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return n[a]?n[a].coordinate+r:null;var u=yi(i,e.dataKey,e.domain[a]);return vo()(u)?null:e.scale(u)-o/2+r},Fi=function(t){var e=t.numericAxis,n=e.scale.domain();if("number"===e.type){var r=Math.min(n[0],n[1]),o=Math.max(n[0],n[1]);return r<=0&&o>=0?0:o<0?o:r}return n[0]},$i=function(t,e){var n=t.props.stackId;if((0,ai.vh)(n)){var r=e[n];if(r){var o=r.items.indexOf(t);return o>=0?r.stackedData[o]:null}}return null},Wi=function(t,e,n){return Object.keys(t).reduce(function(r,o){var i=t[o].stackedData.reduce(function(t,r){var o=r.slice(e,n+1).reduce(function(t,e){return[ho()(e.concat([t[0]]).filter(ai.Et)),fo()(e.concat([t[1]]).filter(ai.Et))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],r[0]),Math.max(i[1],r[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},qi=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Xi=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Hi=function(t,e,n){if(go()(t))return t(e,n);if(!Array.isArray(t))return e;var r=[];if((0,ai.Et)(t[0]))r[0]=n?t[0]:Math.min(t[0],e[0]);else if(qi.test(t[0])){var o=+qi.exec(t[0])[1];r[0]=e[0]-o}else go()(t[0])?r[0]=t[0](e[0]):r[0]=e[0];if((0,ai.Et)(t[1]))r[1]=n?t[1]:Math.max(t[1],e[1]);else if(Xi.test(t[1])){var i=+Xi.exec(t[1])[1];r[1]=e[1]+i}else go()(t[1])?r[1]=t[1](e[1]):r[1]=e[1];return r},Vi=function(t,e,n){if(t&&t.scale&&t.scale.bandwidth){var r=t.scale.bandwidth();if(!n||r>0)return r}if(t&&e&&e.length>=2){for(var o=Co()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return n?void 0:0},Gi=function(t,e,n){return t&&t.length?_o()(t,Oo()(n,"type.defaultProps.domain"))?e:t:e},Yi=function(t,e){var n=t.props,r=n.dataKey,o=n.name,i=n.unit,a=n.formatter,u=n.tooltipType,c=n.chartType,l=n.hide;return fi(fi({},(0,ui.J9)(t,!1)),{},{dataKey:r,unit:i,formatter:a,name:o||r,color:gi(t),value:yi(e,r),type:u,payload:e,chartType:c,hide:l})}},9169:function(t,e,n){var r=n(5862),o=n(4383);t.exports=function(t,e,n){var i=e(t);return o(t)?i:r(i,n(t))}},9184:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},9406:function(t,e,n){var r=n(8938),o=n(8420);t.exports=function(t){for(var e=o(t),n=e.length;n--;){var i=e[n],a=t[i];e[n]=[i,a,r(a)]}return e}},9475:function(t,e,n){var r=n(6628)(n(5447));t.exports=r},9542:function(t,e,n){"use strict";n.d(e,{W:function(){return s}});var r=n(1609),o=n.n(r),i=n(7064),a=n(3499),u=n(684),c=n(9162);function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},l.apply(this,arguments)}var s=function(t){var e=t.xAxisId,n=(0,a.yi)(),r=(0,a.rY)(),s=(0,a.AF)(e);return null==s?null:o().createElement(u.u,l({},s,{className:(0,i.A)("recharts-".concat(s.axisType," ").concat(s.axisType),s.className),viewBox:{x:0,y:0,width:n,height:r},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))};s.displayName="XAxis",s.defaultProps={allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0}},9564:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},9592:function(t,e,n){var r=n(4360),o=n(1784);t.exports=function(t){return null!=t&&o(t.length)&&!r(t)}},9637:function(t,e,n){var r=n(1429)(Object.getPrototypeOf,Object);t.exports=r},9650:function(t,e,n){var r=n(2748);t.exports=function(t,e,n){var o=null==t?void 0:r(t,e);return void 0===o?n:o}},9653:function(t,e,n){"use strict";n.d(e,{yp:function(){return L},GG:function(){return X},NE:function(){return z},nZ:function(){return U},xQ:function(){return F}});var r=n(1609),o=n.n(r),i=n(4360),a=n.n(i),u=n(5229),c=n.n(u),l=n(6154),s=n.n(l),f=n(8434),p=n.n(f),h=n(2729),d=n(7064),y=n(4227),v=n(6075);function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},g.apply(this,arguments)}function b(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return x(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return x(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach(function(e){j(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function j(t,e,n){var r;return r=function(t,e){if("object"!=m(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=m(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var S=function(t,e,n,r,o){var i,a=n-r;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+n,",").concat(e),i+="L ".concat(t+n-a/2,",").concat(e+o),i+="L ".concat(t+n-a/2-r,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},A={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},E=function(t){var e=O(O({},A),t),n=(0,r.useRef)(),i=b((0,r.useState)(-1),2),a=i[0],u=i[1];(0,r.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=e.x,l=e.y,s=e.upperWidth,f=e.lowerWidth,p=e.height,h=e.className,m=e.animationEasing,x=e.animationDuration,w=e.animationBegin,j=e.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var E=(0,d.A)("recharts-trapezoid",h);return j?o().createElement(y.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:x,animationEasing:m,isActive:j},function(t){var r=t.upperWidth,i=t.lowerWidth,u=t.height,c=t.x,l=t.y;return o().createElement(y.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:m},o().createElement("path",g({},(0,v.J9)(e,!0),{className:E,d:S(c,l,r,i,u),ref:n})))}):o().createElement("g",null,o().createElement("path",g({},(0,v.J9)(e,!0),{className:E,d:S(c,l,s,f,p)})))},P=n(1596),k=n(6799),M=n(4204),_=["option","shapeType","propTransformer","activeClassName","isActive"];function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function C(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function I(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function D(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?I(Object(n),!0).forEach(function(e){N(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function N(t,e,n){var r;return r=function(t,e){if("object"!=T(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==T(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t,e){return D(D({},e),t)}function R(t){var e=t.shapeType,n=t.elementProps;switch(e){case"rectangle":return o().createElement(h.M,n);case"trapezoid":return o().createElement(E,n);case"sector":return o().createElement(P.h,n);case"symbols":if(function(t){return"symbols"===t}(e))return o().createElement(M.i,n);break;default:return null}}function L(t){var e,n=t.option,i=t.shapeType,u=t.propTransformer,l=void 0===u?B:u,f=t.activeClassName,p=void 0===f?"recharts-active-shape":f,h=t.isActive,d=C(t,_);if((0,r.isValidElement)(n))e=(0,r.cloneElement)(n,D(D({},d),function(t){return(0,r.isValidElement)(t)?t.props:t}(n)));else if(a()(n))e=n(d);else if(c()(n)&&!s()(n)){var y=l(n,d);e=o().createElement(R,{shapeType:i,elementProps:y})}else{var v=d;e=o().createElement(R,{shapeType:i,elementProps:v})}return h?o().createElement(k.W,{className:p},e):e}function z(t,e){return null!=e&&"trapezoids"in t.props}function U(t,e){return null!=e&&"sectors"in t.props}function F(t,e){return null!=e&&"points"in t.props}function $(t,e){var n,r,o=t.x===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.x)||t.x===e.x,i=t.y===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.y)||t.y===e.y;return o&&i}function W(t,e){var n=t.endAngle===e.endAngle,r=t.startAngle===e.startAngle;return n&&r}function q(t,e){var n=t.x===e.x,r=t.y===e.y,o=t.z===e.z;return n&&r&&o}function X(t){var e=t.activeTooltipItem,n=t.graphicalItem,r=t.itemData,o=function(t,e){var n;return z(t,e)?n="trapezoids":U(t,e)?n="sectors":F(t,e)&&(n="points"),n}(n,e),i=function(t,e){var n,r;return z(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:U(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:F(t,e)?e.payload:{}}(n,e),a=r.filter(function(t,r){var a=p()(i,t),u=n.props[o].filter(function(t){var r=function(t,e){var n;return z(t,e)?n=$:U(t,e)?n=W:F(t,e)&&(n=q),n}(n,e);return r(t,e)}),c=n.props[o].indexOf(u[u.length-1]);return a&&r===c});return r.indexOf(a[a.length-1])}},9668:function(t,e,n){var r=n(4383),o=n(3536),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(r(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!o(t))||(a.test(t)||!i.test(t)||null!=e&&t in Object(e))}},9685:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=!0,o="Invariant failed";function i(t,e){if(!t){if(r)throw new Error(o);var n="function"==typeof e?e():e,i=n?"".concat(o,": ").concat(n):o;throw new Error(i)}}},9705:function(t){t.exports=function(t,e,n){for(var r=n-1,o=t.length;++r<o;)if(t[r]===e)return r;return-1}},9783:function(t,e,n){"use strict";function r(t,e){for(var n in t)if({}.hasOwnProperty.call(t,n)&&(!{}.hasOwnProperty.call(e,n)||t[n]!==e[n]))return!1;for(var r in e)if({}.hasOwnProperty.call(e,r)&&!{}.hasOwnProperty.call(t,r))return!1;return!0}n.d(e,{b:function(){return r}})},9804:function(t){t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},9830:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},9835:function(t,e,n){var r=n(9592);t.exports=function(t,e){return function(n,o){if(null==n)return n;if(!r(n))return t(n,o);for(var i=n.length,a=e?i:-1,u=Object(n);(e?a--:++a<i)&&!1!==o(u[a],a,u););return n}}},9886:function(t,e,n){"use strict";function r(t){return function(){return t}}n.d(e,{A:function(){return r}})},9946:function(t,e,n){var r=n(836),o=n(6499),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(t){return null==t?[]:(t=Object(t),r(a(t),function(e){return i.call(t,e)}))}:o;t.exports=u},9966:function(t,e,n){var r=n(8089),o=n(6128),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return o(t);var e=[];for(var n in Object(t))i.call(t,n)&&"constructor"!=n&&e.push(n);return e}}}]);
"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[631],{4060:function(e,n,t){t.r(n),t.d(n,{default:function(){return v}});var s,o=t(5798),i=t(7195),r=t.n(i),c=t(9662),a=t.n(c),u=t(1533),d=t.n(u),l=(null===d()||void 0===d()||null===(s=d().components)||void 0===s?void 0:s.user_select)||{},f=function(e){var n=(0,o.getClosest)(e,".gform-settings-field"),t=l.endpoints,s=l.data;a().instances.postSelects.push(new(r())({container:n.id,selector:"gform-settings-field-user-select",render:!1,renderListData:!0,searchType:"async",onItemSelect:function(e){return function(e,n){var t=(0,o.getNodes)("gf-user-select-input",!0,n,!1)[0];t.value=e;var s=new Event("change");t.dispatchEvent(s)}(e,n)},baseUrl:t.get,endpoints:t,endpointKey:"get",endpointRequestOptions:{method:"GET"},endpointUseRest:!0,listData:s}))},p=function(e){!function(e){a().instances.postSelects=[],e.forEach(function(e){f(e)})}(e)},v=function(e){p(e),(0,o.consoleInfo)("Gravity Forms Admin: Initialized user select dropdown component.")}}}]);
"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[832],{1026:function(t,e,n){n.r(e),n.d(e,{default:function(){return b}});var o=n(5798),r=n(2279),i=n(1533),c=n.n(i),a=window.wp||{},l=(null===c()||void 0===c()?void 0:c().block_editor["gravityforms/form"])||{},s=l.i18n,u={ready:!1},d={insertButton:null},f=function(t){return a.data.select("core/blocks").getBlockTypes().filter(function(e){return!t||"gravityforms/form"!==e.name}).map(function(t){return t.name})},p=function(){a.data.dispatch("core/edit-post").showBlockTypes(f(!1))},m=function(){var t;window.wp.data.dispatch("core/edit-post").setIsInserterOpened(!0),t=f(!0),a.data.dispatch("core/edit-post").hideBlockTypes(t),a.data.dispatch("core/edit-post").showBlockTypes(["gravityforms/form"]);var e=setInterval(function(){var t,n,r;d.insertButton=document.querySelector(".editor-block-list-item-gravityforms-form"),d.insertButton&&(u.ready=!0,n=document.createElement("div"),r=d.insertButton.getBoundingClientRect(),n.innerHTML='\n\t<div class="gform-block__tooltip-inner">\n\t\t<span class="gform-block__tooltip-title">'.concat(s.insert_gform_block_title,"</span>\n\t\t").concat((0,o.sprintf)(s.insert_gform_block_content,'<a class="gform-link" href="'.concat(l.data.block_docs_url,'" rel="noopener" target="_blank">'),'<span class="screen-reader-text">'.concat(s.external_link_opens_in_new_tab,'</span>&nbsp;<span class="gform-icon gform-icon--external-link"></span></a>')),"\n\t</div>\n\t"),n.classList.add("gform-block__tooltip"),(0,o.isRtl)()?n.style="left: ".concat(r.right-(275+d.insertButton.clientWidth),"px; top: ").concat(r.top+d.insertButton.clientHeight/2,"px;"):n.style="left: ".concat(r.right+20,"px; top: ").concat(r.top+d.insertButton.clientHeight/2,"px;"),d.insertButton.addEventListener("mouseenter",function(){n&&(n.style.opacity="0",setTimeout(function(){n.style.zIndex="-1"},200))}),d.insertButton.parentNode.appendChild(n),t=a.data.subscribe(function(){a.data.select("core/block-editor").getBlocks().filter(function(t){return"gravityforms/form"===t.name&&void 0===t.originalContent}).length&&(t(),p())})),u.ready&&clearInterval(e)},500)},g=function(){var t=(0,o.queryToJson)();null!=t&&t.gfAddBlock&&(m(),window.addEventListener("beforeunload",p),(0,o.consoleInfo)("Gravity Forms Admin: Initialized block editor insert form scripts."))},b=function(){if(g(),void 0!==n(4309)){var t=n(1714);(0,r.registerPlugin)("gravityforms",{render:t.default}),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all block editor scripts.")}}},1714:function(t,e,n){n.r(e);var o=n(8140),r=n(527),i=n(4309),c=n(7143),a=n(6087),l=n(3963),s=n.n(l),u=n(1533);function d(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,o)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?d(Object(n),!0).forEach(function(e){(0,r.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var p=n.n(u)().block_editor["gravityforms/form"].i18n,m=function(){if(_()){var t=(0,c.select)("core/block-editor").getSelectedBlock();navigator.clipboard.writeText(JSON.stringify(b(t.attributes))).then()}},g=function(){_()&&k()&&navigator.clipboard.readText().then(function(t){var e=f({},(0,c.select)("core/block-editor").getSelectedBlock());try{var n=b(JSON.parse(t));e.attributes=f(f({},e.attributes),n),(0,c.dispatch)("core/block-editor").updateBlock(e.clientId,e)}catch(t){v(p.invalid_form_styles,p.please_ensure_correct_format)}})},b=function(t){var e=Object.entries(t).filter(function(t){var e=(0,o.A)(t,2),n=e[0],r=e[1];return y().includes(n)&&"string"==typeof r});return Object.fromEntries(e)},y=function(){return["theme","inputSize","inputBorderRadius","inputBorderColor","inputBackgroundColor","inputPrimaryColor","inputColor","inputImageChoiceAppearance","inputImageChoiceStyle","inputImageChoiceSize","labelFontSize","labelColor","descriptionFontSize","descriptionColor","buttonPrimaryBackgroundColor","buttonPrimaryColor"]},v=function(t,e){new(s())({alertButtonText:p.ok,content:e,closeButtonTitle:p.close,id:"copy-paste-error-alert",maskBlur:!1,maskTheme:"none",mode:"alert",title:t,titleIcon:"circle-delete",titleIconColor:"#DD301D",zIndex:100055}).showDialog()},_=function(){return!!window.isSecureContext||(v(p.copy_and_paste_not_available,p.copy_and_paste_requires_secure_connection),!1)},k=function(){return!(navigator.userAgent.indexOf("Firefox")>-1)||"function"==typeof navigator.clipboard.readText||(v(p.paste_not_available,p.your_browser_no_permission_to_paste),!1)};e.default=function(){return React.createElement(a.Fragment,null,React.createElement(i.PluginBlockSettingsMenuItem,{allowedBlocks:["gravityforms/form"],icon:"",label:p.copy_form_styles,onClick:function(){return m()}}),React.createElement(i.PluginBlockSettingsMenuItem,{allowedBlocks:["gravityforms/form"],icon:"",label:p.paste_form_styles,onClick:function(){return g()}}))}}}]);
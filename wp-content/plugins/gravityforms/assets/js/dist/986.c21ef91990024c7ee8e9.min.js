"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[986],{419:function(t,e,n){n.d(e,{A:function(){return A}});var r=n(8171),a=n(3798);function i(t,e,n){if(void 0===e&&(e={}),n=new r.Ay(n),e.v2){if(!t.countryCallingCode)throw new Error("Invalid phone number object passed");n.selectNumberingPlan(t.countryCallingCode)}else{if(!t.phone)return!1;if(t.country){if(!n.hasCountry(t.country))throw new Error("Unknown country: ".concat(t.country));n.country(t.country)}else{if(!t.countryCallingCode)throw new Error("Invalid phone number object passed");n.selectNumberingPlan(t.countryCallingCode)}}if(n.possibleLengths())return function(t,e){if("IS_POSSIBLE"===(0,a.A)(t,e))return!0;return!1}(t.phone||t.nationalNumber,n);if(t.countryCallingCode&&n.isNonGeographicCallingCode(t.countryCallingCode))return!0;throw new Error('Missing "possibleLengths" in metadata. Perhaps the metadata has been generated before v1.0.18.')}var o=n(2700),u=n(9386);function l(t,e,n){var a=new r.Ay(n).getCountryCodesForCallingCode(t);return a?a.filter(function(t){return function(t,e,n){var a=new r.Ay(n);if(a.selectNumberingPlan(e),a.numberingPlan.possibleLengths().indexOf(t.length)>=0)return!0;return!1}(e,t,n)}):[]}var s=n(8988),c=/^[\d]+(?:[~\u2053\u223C\uFF5E][\d]+)?$/;function f(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return h(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach(function(e){d(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var y={formatExtension:function(t,e,n){return"".concat(t).concat(n.ext()).concat(e)}};function v(t,e,n,a){if(n=n?m(m({},y),n):y,a=new r.Ay(a),t.country&&"001"!==t.country){if(!a.hasCountry(t.country))throw new Error("Unknown country: ".concat(t.country));a.country(t.country)}else{if(!t.countryCallingCode)return t.phone||"";a.selectNumberingPlan(t.countryCallingCode)}var i,o=a.countryCallingCode(),u=n.v2?t.nationalNumber:t.phone;switch(e){case"NATIONAL":return u?b(i=p(u,t.carrierCode,"NATIONAL",a,n),t.ext,a,n.formatExtension):"";case"INTERNATIONAL":return u?(i=p(u,null,"INTERNATIONAL",a,n),b(i="+".concat(o," ").concat(i),t.ext,a,n.formatExtension)):"+".concat(o);case"E.164":return"+".concat(o).concat(u);case"RFC3966":return function(t){var e=t.number,n=t.ext;if(!e)return"";if("+"!==e[0])throw new Error('"formatRFC3966()" expects "number" to be in E.164 format.');return"tel:".concat(e).concat(n?";ext="+n:"")}({number:"+".concat(o).concat(u),ext:t.ext});case"IDD":if(!n.fromCountry)return;var l=function(t,e,n,a,i){var o=(0,r.Ko)(a,i.metadata);if(o===n){var u=p(t,e,"NATIONAL",i);return"1"===n?n+" "+u:u}var l=function(t,e,n){var a=new r.Ay(n);return a.selectNumberingPlan(t,e),a.defaultIDDPrefix()?a.defaultIDDPrefix():c.test(a.IDDPrefix())?a.IDDPrefix():void 0}(a,void 0,i.metadata);if(l)return"".concat(l," ").concat(n," ").concat(p(t,null,"INTERNATIONAL",i))}(u,t.carrierCode,o,n.fromCountry,a);return b(l,t.ext,a,n.formatExtension);default:throw new Error('Unknown "format" argument passed to "formatNumber()": "'.concat(e,'"'))}}function p(t,e,n,r,a){var i=function(t,e){for(var n,r=f(t);!(n=r()).done;){var a=n.value;if(a.leadingDigitsPatterns().length>0){var i=a.leadingDigitsPatterns()[a.leadingDigitsPatterns().length-1];if(0!==e.search(i))continue}if((0,o.A)(e,a.pattern()))return a}}(r.formats(),t);return i?(0,s.A)(t,i,{useInternationalFormat:"INTERNATIONAL"===n,withNationalPrefix:!i.nationalPrefixIsOptionalWhenFormattingInNationalFormat()||!a||!1!==a.nationalPrefix,carrierCode:e,metadata:r}):t}function b(t,e,n,r){return e?r(t,e,n):t}function C(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function N(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?C(Object(n),!0).forEach(function(e){x(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function x(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function P(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var A=function(){function t(e,n,a){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e)throw new TypeError("`country` or `countryCallingCode` not passed");if(!n)throw new TypeError("`nationalNumber` not passed");if(!a)throw new TypeError("`metadata` not passed");var i=function(t,e){var n,a,i=new r.Ay(e);S(t)?(n=t,i.selectNumberingPlan(n),a=i.countryCallingCode()):a=t;return{country:n,countryCallingCode:a}}(e,a),o=i.country,u=i.countryCallingCode;this.country=o,this.countryCallingCode=u,this.nationalNumber=n,this.number="+"+this.countryCallingCode+this.nationalNumber,this.getMetadata=function(){return a}}var e,n,a;return e=t,(n=[{key:"setExt",value:function(t){this.ext=t}},{key:"getPossibleCountries",value:function(){return this.country?[this.country]:l(this.countryCallingCode,this.nationalNumber,this.getMetadata())}},{key:"isPossible",value:function(){return i(this,{v2:!0},this.getMetadata())}},{key:"isValid",value:function(){return function(t,e,n){if(e=e||{},(n=new r.Ay(n)).selectNumberingPlan(t.country,t.countryCallingCode),n.hasTypes())return void 0!==(0,u.A)(t,e,n.metadata);var a=e.v2?t.nationalNumber:t.phone;return(0,o.A)(a,n.nationalNumberPattern())}(this,{v2:!0},this.getMetadata())}},{key:"isNonGeographic",value:function(){return new r.Ay(this.getMetadata()).isNonGeographicCallingCode(this.countryCallingCode)}},{key:"isEqual",value:function(t){return this.number===t.number&&this.ext===t.ext}},{key:"getType",value:function(){return(0,u.A)(this,{v2:!0},this.getMetadata())}},{key:"format",value:function(t,e){return v(this,t,e?N(N({},e),{},{v2:!0}):{v2:!0},this.getMetadata())}},{key:"formatNational",value:function(t){return this.format("NATIONAL",t)}},{key:"formatInternational",value:function(t){return this.format("INTERNATIONAL",t)}},{key:"getURI",value:function(t){return this.format("RFC3966",t)}}])&&P(e.prototype,n),a&&P(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}(),S=function(t){return/^[A-Z]{2}$/.test(t)}},1677:function(t,e,n){function r(t,e,n){for(var r={},a="",i=0,o=0;o<t.length;){var u=n(t[o],a,r);void 0!==u&&(a+=u,void 0!==e&&(e===o?i=a.length-1:e>o&&(i=a.length))),o++}return void 0===e&&(i=a.length),{value:a,caret:i}}n.d(e,{A:function(){return r}})},1844:function(t,e,n){n.d(e,{A:function(){return a}});var r={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"};function a(t){return r[t]}},1969:function(t,e){e.A={AC:"40123",AD:"312345",AE:"501234567",AF:"701234567",AG:"2684641234",AI:"2642351234",AL:"672123456",AM:"77123456",AO:"923123456",AR:"91123456789",AS:"6847331234",AT:"664123456",AU:"412345678",AW:"5601234",AX:"412345678",AZ:"401234567",BA:"61123456",BB:"2462501234",BD:"1812345678",BE:"470123456",BF:"70123456",BG:"43012345",BH:"36001234",BI:"79561234",BJ:"0195123456",BL:"690001234",BM:"4413701234",BN:"7123456",BO:"71234567",BQ:"3181234",BR:"11961234567",BS:"2423591234",BT:"17123456",BW:"71123456",BY:"294911911",BZ:"6221234",CA:"5062345678",CC:"412345678",CD:"991234567",CF:"70012345",CG:"061234567",CH:"781234567",CI:"0123456789",CK:"71234",CL:"221234567",CM:"671234567",CN:"13123456789",CO:"3211234567",CR:"83123456",CU:"51234567",CV:"9911234",CW:"95181234",CX:"412345678",CY:"96123456",CZ:"601123456",DE:"15123456789",DJ:"77831001",DK:"34412345",DM:"7672251234",DO:"8092345678",DZ:"551234567",EC:"991234567",EE:"51234567",EG:"1001234567",EH:"650123456",ER:"7123456",ES:"612345678",ET:"911234567",FI:"412345678",FJ:"7012345",FK:"51234",FM:"3501234",FO:"211234",FR:"612345678",GA:"06031234",GB:"7400123456",GD:"4734031234",GE:"555123456",GF:"694201234",GG:"7781123456",GH:"231234567",GI:"57123456",GL:"221234",GM:"3012345",GN:"601123456",GP:"690001234",GQ:"222123456",GR:"6912345678",GT:"51234567",GU:"6713001234",GW:"955012345",GY:"6091234",HK:"51234567",HN:"91234567",HR:"921234567",HT:"34101234",HU:"201234567",ID:"812345678",IE:"850123456",IL:"502345678",IM:"7924123456",IN:"8123456789",IO:"3801234",IQ:"7912345678",IR:"9123456789",IS:"6111234",IT:"3123456789",JE:"7797712345",JM:"8762101234",JO:"790123456",JP:"9012345678",KE:"712123456",KG:"700123456",KH:"91234567",KI:"72001234",KM:"3212345",KN:"8697652917",KP:"1921234567",KR:"1020000000",KW:"50012345",KY:"3453231234",KZ:"7710009998",LA:"2023123456",LB:"71123456",LC:"7582845678",LI:"660234567",LK:"712345678",LR:"770123456",LS:"50123456",LT:"61234567",LU:"628123456",LV:"21234567",LY:"912345678",MA:"650123456",MC:"612345678",MD:"62112345",ME:"67622901",MF:"690001234",MG:"321234567",MH:"2351234",MK:"72345678",ML:"65012345",MM:"92123456",MN:"88123456",MO:"66123456",MP:"6702345678",MQ:"696201234",MR:"22123456",MS:"6644923456",MT:"96961234",MU:"52512345",MV:"7712345",MW:"991234567",MX:"2221234567",MY:"123456789",MZ:"821234567",NA:"811234567",NC:"751234",NE:"93123456",NF:"381234",NG:"8021234567",NI:"81234567",NL:"612345678",NO:"40612345",NP:"9841234567",NR:"5551234",NU:"8884012",NZ:"211234567",OM:"92123456",PA:"61234567",PE:"912345678",PF:"87123456",PG:"70123456",PH:"9051234567",PK:"3012345678",PL:"512345678",PM:"551234",PR:"7872345678",PS:"599123456",PT:"912345678",PW:"6201234",PY:"961456789",QA:"33123456",RE:"692123456",RO:"712034567",RS:"601234567",RU:"9123456789",RW:"720123456",SA:"512345678",SB:"7421234",SC:"2510123",SD:"911231234",SE:"701234567",SG:"81234567",SH:"51234",SI:"31234567",SJ:"41234567",SK:"912123456",SL:"25123456",SM:"66661212",SN:"701234567",SO:"71123456",SR:"7412345",SS:"977123456",ST:"9812345",SV:"70123456",SX:"7215205678",SY:"944567890",SZ:"76123456",TA:"8999",TC:"6492311234",TD:"63012345",TG:"90112345",TH:"812345678",TJ:"917123456",TK:"7290",TL:"77212345",TM:"66123456",TN:"20123456",TO:"7715123",TR:"5012345678",TT:"8682911234",TV:"901234",TW:"912345678",TZ:"621234567",UA:"501234567",UG:"712345678",US:"2015550123",UY:"94231234",UZ:"912345678",VA:"3123456789",VC:"7844301234",VE:"4121234567",VG:"2843001234",VI:"3406421234",VN:"912345678",VU:"5912345",WF:"821234",WS:"7212345",XK:"43201234",YE:"712345678",YT:"639012345",ZA:"711234567",ZM:"955123456",ZW:"712345678"}},2503:function(t,e,n){n.d(e,{a:function(){return o}});var r=n(4657),a=n(419);function i(t,e,n){if(e[t])return new a.A(t,e[t],n)}function o(){return(0,r.A)(i,arguments)}},2700:function(t,e,n){function r(t,e){return t=t||"",new RegExp("^(?:"+e+")$").test(t)}n.d(e,{A:function(){return r}})},3068:function(t,e,n){n.d(e,{Jq:function(){return r},OA:function(){return a},tz:function(){return o},uD:function(){return i}});var r=3,a="0-9０-９٠-٩۰-۹",i="".concat("-‐-―−ー－").concat("／/").concat("．.").concat("  ­​⁠　").concat("()（）［］\\[\\]").concat("~⁓∼～"),o="+＋"},3315:function(t,e,n){n.d(e,{A:function(){return a}});var r=n(3068);function a(t){return t.replace(new RegExp("[".concat(r.uD,"]+"),"g")," ").trim()}},3798:function(t,e,n){function r(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function i(t,e){return o(t,void 0,e)}function o(t,e,n){var a=n.type(e),i=a&&a.possibleLengths()||n.possibleLengths();if(!i)return"IS_POSSIBLE";if("FIXED_LINE_OR_MOBILE"===e){if(!n.type("FIXED_LINE"))return o(t,"MOBILE",n);var u=n.type("MOBILE");u&&(i=function(t,e){for(var n,a=t.slice(),i=r(e);!(n=i()).done;){var o=n.value;t.indexOf(o)<0&&a.push(o)}return a.sort(function(t,e){return t-e})}(i,u.possibleLengths()))}else if(e&&!a)return"INVALID_LENGTH";var l=t.length,s=i[0];return s===l?"IS_POSSIBLE":s>l?"TOO_SHORT":i[i.length-1]<l?"TOO_LONG":i.indexOf(l,1)>=0?"IS_POSSIBLE":"INVALID_LENGTH"}n.d(e,{A:function(){return i}})},4965:function(t,e,n){n.d(e,{A:function(){return a}});var r=n(5295);function a(t,e,n){"function"==typeof e&&(n=e,e="x");var a=(0,r.v)(e,t);return function(t,e){if(e.length<a)return n(t,e)}}},5295:function(t,e,n){function r(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function i(t,e){for(var n,a=0,i=r(e.split(""));!(n=i()).done;){n.value===t&&a++}return a}n.d(e,{v:function(){return i}})},6511:function(t,e,n){n.d(e,{A:function(){return a}});var r=n(7611);function a(t,e,n){"string"==typeof n&&(n=(0,r.A)(n));var a=n(t)||{},i=a.text,o=a.template;if(void 0===i&&(i=t),o)if(void 0===e)e=i.length;else{for(var u=0,l=!1,s=-1;u<i.length&&u<o.length;){if(i[u]!==o[u]){if(0===e){l=!0,e=u;break}s=u,e--}u++}l||(e=s+1)}return{text:i,caret:e}}},7611:function(t,e,n){n.d(e,{A:function(){return o}});var r=n(5295);function a(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return i(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"x",n=arguments.length>2?arguments[2]:void 0;if(!t)return function(t){return{text:t}};var i=(0,r.v)(e,t);return function(o){if(!o)return{text:"",template:t};for(var u,l=0,s="",c=a(t.split(""));!(u=c()).done;){var f=u.value;if(f===e){if(s+=o[l],++l===o.length&&o.length<i)break}else s+=f}return n&&(s=function(t,e){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"x",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:" ",i=t.length,o=(0,r.v)("(",t)-(0,r.v)(")",t);o>0&&i<e.length;)t+=e[i].replace(n,a),")"===e[i]&&o--,i++;return t}(s,t)),{text:s,template:t}}}},8456:function(t,e,n){n.d(e,{Q:function(){return dt}});var r=n(8533),a=n(8171),i=n(419);function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var u=function(){function t(e){var n=e.onCountryChange,r=e.onCallingCodeChange;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.onCountryChange=n,this.onCallingCodeChange=r}var e,n,r;return e=t,(n=[{key:"reset",value:function(t){var e=t.country,n=t.callingCode;this.international=!1,this.missingPlus=!1,this.IDDPrefix=void 0,this.callingCode=void 0,this.digits="",this.resetNationalSignificantNumber(),this.initCountryAndCallingCode(e,n)}},{key:"resetNationalSignificantNumber",value:function(){this.nationalSignificantNumber=this.getNationalDigits(),this.nationalSignificantNumberMatchesInput=!0,this.nationalPrefix=void 0,this.carrierCode=void 0,this.complexPrefixBeforeNationalSignificantNumber=void 0}},{key:"update",value:function(t){for(var e=0,n=Object.keys(t);e<n.length;e++){var r=n[e];this[r]=t[r]}}},{key:"initCountryAndCallingCode",value:function(t,e){this.setCountry(t),this.setCallingCode(e)}},{key:"setCountry",value:function(t){this.country=t,this.onCountryChange(t)}},{key:"setCallingCode",value:function(t){this.callingCode=t,this.onCallingCodeChange(t,this.country)}},{key:"startInternationalNumber",value:function(t,e){this.international=!0,this.initCountryAndCallingCode(t,e)}},{key:"appendDigits",value:function(t){this.digits+=t}},{key:"appendNationalSignificantNumberDigits",value:function(t){this.nationalSignificantNumber+=t}},{key:"getNationalDigits",value:function(){return this.international?this.digits.slice((this.IDDPrefix?this.IDDPrefix.length:0)+(this.callingCode?this.callingCode.length:0)):this.digits}},{key:"getDigitsWithoutInternationalPrefix",value:function(){return this.international&&this.IDDPrefix?this.digits.slice(this.IDDPrefix.length):this.digits}}])&&o(e.prototype,n),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function l(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var c="x",f=new RegExp(c);function h(t,e){if(e<1)return"";for(var n="";e>1;)1&e&&(n+=t),e>>=1,t+=t;return n+t}function g(t,e){return")"===t[e]&&e++,function(t){var e=[],n=0;for(;n<t.length;)"("===t[n]?e.push(n):")"===t[n]&&e.pop(),n++;var r=0,a="";e.push(t.length);for(var i=0,o=e;i<o.length;i++){var u=o[i];a+=t.slice(r,u),r=u+1}return a}(t.slice(0,e))}var m=n(3798);function d(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var v={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"};function p(t){return v[t]}function b(t){for(var e,n="",r=d(t.split(""));!(e=r()).done;){var a=p(e.value);a&&(n+=a)}return n}var C=n(8988);function N(t,e,n){var r=n.metadata,a=n.shouldTryNationalPrefixFormattingRule,i=n.getSeparatorAfterNationalPrefix;if(new RegExp("^(?:".concat(e.pattern(),")$")).test(t.nationalSignificantNumber))return function(t,e,n){var r=n.metadata,a=n.shouldTryNationalPrefixFormattingRule,i=n.getSeparatorAfterNationalPrefix;t.nationalSignificantNumber,t.international,t.nationalPrefix,t.carrierCode;if(a(e)){var o=x(t,e,{useNationalPrefixFormattingRule:!0,getSeparatorAfterNationalPrefix:i,metadata:r});if(o)return o}return x(t,e,{useNationalPrefixFormattingRule:!1,getSeparatorAfterNationalPrefix:i,metadata:r})}(t,e,{metadata:r,shouldTryNationalPrefixFormattingRule:a,getSeparatorAfterNationalPrefix:i})}function x(t,e,n){var r=n.metadata,a=n.useNationalPrefixFormattingRule,i=n.getSeparatorAfterNationalPrefix,o=(0,C.A)(t.nationalSignificantNumber,e,{carrierCode:t.carrierCode,useInternationalFormat:t.international,withNationalPrefix:a,metadata:r});if(a||(t.nationalPrefix?o=t.nationalPrefix+i(e)+o:t.complexPrefixBeforeNationalSignificantNumber&&(o=t.complexPrefixBeforeNationalSignificantNumber+" "+o)),function(t,e){return b(t)===e.getNationalDigits()}(o,t))return o}function P(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var A=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,n,r;return e=t,(n=[{key:"parse",value:function(t){if(this.context=[{or:!0,instructions:[]}],this.parsePattern(t),1!==this.context.length)throw new Error("Non-finalized contexts left when pattern parse ended");var e=this.context[0],n=e.branches,r=e.instructions;if(n)return{op:"|",args:n.concat([O(r)])};if(0===r.length)throw new Error("Pattern is required");return 1===r.length?r[0]:r}},{key:"startContext",value:function(t){this.context.push(t)}},{key:"endContext",value:function(){this.context.pop()}},{key:"getContext",value:function(){return this.context[this.context.length-1]}},{key:"parsePattern",value:function(t){if(!t)throw new Error("Pattern is required");var e=t.match(I);if(e){var n=e[1],r=t.slice(0,e.index),a=t.slice(e.index+n.length);switch(n){case"(?:":r&&this.parsePattern(r),this.startContext({or:!0,instructions:[],branches:[]});break;case")":if(!this.getContext().or)throw new Error('")" operator must be preceded by "(?:" operator');if(r&&this.parsePattern(r),0===this.getContext().instructions.length)throw new Error('No instructions found after "|" operator in an "or" group');var i=this.getContext().branches;i.push(O(this.getContext().instructions)),this.endContext(),this.getContext().instructions.push({op:"|",args:i});break;case"|":if(!this.getContext().or)throw new Error('"|" operator can only be used inside "or" groups');if(r&&this.parsePattern(r),!this.getContext().branches){if(1!==this.context.length)throw new Error('"branches" not found in an "or" group context');this.getContext().branches=[]}this.getContext().branches.push(O(this.getContext().instructions)),this.getContext().instructions=[];break;case"[":r&&this.parsePattern(r),this.startContext({oneOfSet:!0});break;case"]":if(!this.getContext().oneOfSet)throw new Error('"]" operator must be preceded by "[" operator');this.endContext(),this.getContext().instructions.push({op:"[]",args:S(r)});break;default:throw new Error("Unknown operator: ".concat(n))}a&&this.parsePattern(a)}else{if(w.test(t))throw new Error("Illegal characters found in a pattern: ".concat(t));this.getContext().instructions=this.getContext().instructions.concat(t.split(""))}}}])&&P(e.prototype,n),r&&P(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function S(t){for(var e=[],n=0;n<t.length;){if("-"===t[n]){if(0===n||n===t.length-1)throw new Error("Couldn't parse a one-of set pattern: ".concat(t));for(var r=t[n-1].charCodeAt(0)+1,a=t[n+1].charCodeAt(0)-1,i=r;i<=a;)e.push(String.fromCharCode(i)),i++}else e.push(t[n]);n++}return e}var w=/[\(\)\[\]\?\:\|]/,I=new RegExp("(\\||\\(\\?\\:|\\)|\\[|\\])");function O(t){return 1===t.length?t[0]:t}function E(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return T(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function F(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var k=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.matchTree=(new A).parse(e)}var e,n,r;return e=t,n=[{key:"match",value:function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).allowOverflow;if(!t)throw new Error("String is required");var n=D(t.split(""),this.matchTree,!0);if(n&&n.match&&delete n.matchedChars,!n||!n.overflow||e)return n}}],n&&F(e.prototype,n),r&&F(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function D(t,e,n){if("string"==typeof e){var r=t.join("");return 0===e.indexOf(r)?t.length===e.length?{match:!0,matchedChars:t}:{partialMatch:!0}:0===r.indexOf(e)?n&&t.length>e.length?{overflow:!0}:{match:!0,matchedChars:t.slice(0,e.length)}:void 0}if(Array.isArray(e)){for(var a=t.slice(),i=0;i<e.length;){var o=D(a,e[i],n&&i===e.length-1);if(!o)return;if(o.overflow)return o;if(!o.match){if(o.partialMatch)return{partialMatch:!0};throw new Error("Unsupported match result:\n".concat(JSON.stringify(o,null,2)))}if(0===(a=a.slice(o.matchedChars.length)).length)return i===e.length-1?{match:!0,matchedChars:t}:{partialMatch:!0};i++}return n?{overflow:!0}:{match:!0,matchedChars:t.slice(0,t.length-a.length)}}switch(e.op){case"|":for(var u,l,s=E(e.args);!(l=s()).done;){var c=D(t,l.value,n);if(c){if(c.overflow)return c;if(c.match)return{match:!0,matchedChars:c.matchedChars};if(!c.partialMatch)throw new Error("Unsupported match result:\n".concat(JSON.stringify(c,null,2)));u=!0}}return u?{partialMatch:!0}:void 0;case"[]":for(var f,h=E(e.args);!(f=h()).done;){var g=f.value;if(t[0]===g)return 1===t.length?{match:!0,matchedChars:t}:n?{overflow:!0}:{match:!0,matchedChars:[g]}}return;default:throw new Error("Unsupported instruction tree: ".concat(e))}}var M=n(3068),R=n(3315);function j(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return L(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function B(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var _=h("9",15),U=/[- ]/,G=function(){return/\[([^\[\]])*\]/g},W=function(){return/\d(?=[^,}][^,}])/g},$=new RegExp("["+M.uD+"]*\\$1["+M.uD+"]*(\\$\\d["+M.uD+"]*)*$"),H=function(){function t(e){e.state;var n=e.metadata;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.metadata=n,this.resetFormat()}var e,n,r;return e=t,(n=[{key:"resetFormat",value:function(){this.chosenFormat=void 0,this.template=void 0,this.nationalNumberTemplate=void 0,this.populatedNationalNumberTemplate=void 0,this.populatedNationalNumberTemplatePosition=-1}},{key:"reset",value:function(t,e){this.resetFormat(),t?(this.isNANP="1"===t.callingCode(),this.matchingFormats=t.formats(),e.nationalSignificantNumber&&this.narrowDownMatchingFormats(e)):(this.isNANP=void 0,this.matchingFormats=[])}},{key:"format",value:function(t,e){var n,r,a=this;if(n=e.nationalSignificantNumber,r=this.metadata,"IS_POSSIBLE"===(0,m.A)(n,r))for(var i,o=j(this.matchingFormats);!(i=o()).done;){var u=i.value,l=N(e,u,{metadata:this.metadata,shouldTryNationalPrefixFormattingRule:function(t){return a.shouldTryNationalPrefixFormattingRule(t,{international:e.international,nationalPrefix:e.nationalPrefix})},getSeparatorAfterNationalPrefix:function(t){return a.getSeparatorAfterNationalPrefix(t)}});if(l)return this.resetFormat(),this.chosenFormat=u,this.setNationalNumberTemplate(l.replace(/\d/g,c),e),this.populatedNationalNumberTemplate=l,this.populatedNationalNumberTemplatePosition=this.template.lastIndexOf(c),l}return this.formatNationalNumberWithNextDigits(t,e)}},{key:"formatNationalNumberWithNextDigits",value:function(t,e){var n=this.chosenFormat,r=this.chooseFormat(e);if(r)return r===n?this.formatNextNationalNumberDigits(t):this.formatNextNationalNumberDigits(e.getNationalDigits())}},{key:"narrowDownMatchingFormats",value:function(t){var e=this,n=t.nationalSignificantNumber,r=t.nationalPrefix,a=t.international,i=n,o=i.length-3;o<0&&(o=0),this.matchingFormats=this.matchingFormats.filter(function(t){return e.formatSuits(t,a,r)&&e.formatMatches(t,i,o)}),this.chosenFormat&&-1===this.matchingFormats.indexOf(this.chosenFormat)&&this.resetFormat()}},{key:"formatSuits",value:function(t,e,n){return!(n&&!t.usesNationalPrefix()&&!t.nationalPrefixIsOptionalWhenFormattingInNationalFormat()||!e&&!n&&t.nationalPrefixIsMandatoryWhenFormattingInNationalFormat())}},{key:"formatMatches",value:function(t,e,n){var r=t.leadingDigitsPatterns().length;if(0===r)return!0;n=Math.min(n,r-1);var a=t.leadingDigitsPatterns()[n];if(e.length<3)try{return void 0!==new k(a).match(e,{allowOverflow:!0})}catch(t){return!0}return new RegExp("^(".concat(a,")")).test(e)}},{key:"getFormatFormat",value:function(t,e){return e?t.internationalFormat():t.format()}},{key:"chooseFormat",value:function(t){for(var e,n=this,r=function(){var r=e.value;return n.chosenFormat===r?"break":$.test(n.getFormatFormat(r,t.international))?n.createTemplateForFormat(r,t)?(n.chosenFormat=r,"break"):(n.matchingFormats=n.matchingFormats.filter(function(t){return t!==r}),"continue"):"continue"},a=j(this.matchingFormats.slice());!(e=a()).done;){var i=r();if("break"===i)break}return this.chosenFormat||this.resetFormat(),this.chosenFormat}},{key:"createTemplateForFormat",value:function(t,e){if(!(t.pattern().indexOf("|")>=0)){var n=this.getTemplateForFormat(t,e);return n?(this.setNationalNumberTemplate(n,e),!0):void 0}}},{key:"getSeparatorAfterNationalPrefix",value:function(t){return this.isNANP||t&&t.nationalPrefixFormattingRule()&&U.test(t.nationalPrefixFormattingRule())?" ":""}},{key:"getInternationalPrefixBeforeCountryCallingCode",value:function(t,e){var n=t.IDDPrefix,r=t.missingPlus;return n?e&&!1===e.spacing?n:n+" ":r?"":"+"}},{key:"getTemplate",value:function(t){if(this.template){for(var e=-1,n=0,r=t.international?this.getInternationalPrefixBeforeCountryCallingCode(t,{spacing:!1}):"";n<r.length+t.getDigitsWithoutInternationalPrefix().length;)e=this.template.indexOf(c,e+1),n++;return g(this.template,e+1)}}},{key:"setNationalNumberTemplate",value:function(t,e){this.nationalNumberTemplate=t,this.populatedNationalNumberTemplate=t,this.populatedNationalNumberTemplatePosition=-1,e.international?this.template=this.getInternationalPrefixBeforeCountryCallingCode(e).replace(/[\d\+]/g,c)+h(c,e.callingCode.length)+" "+t:this.template=t}},{key:"getTemplateForFormat",value:function(t,e){var n=e.nationalSignificantNumber,r=e.international,a=e.nationalPrefix,i=e.complexPrefixBeforeNationalSignificantNumber,o=t.pattern();o=o.replace(G(),"\\d").replace(W(),"\\d");var u=_.match(o)[0];if(!(n.length>u.length)){var l=new RegExp("^"+o+"$"),s=n.replace(/\d/g,"9");l.test(s)&&(u=s);var f,g=this.getFormatFormat(t,r);if(this.shouldTryNationalPrefixFormattingRule(t,{international:r,nationalPrefix:a})){var m=g.replace(C._,t.nationalPrefixFormattingRule());if(b(t.nationalPrefixFormattingRule())===(a||"")+b("$1")&&(g=m,f=!0,a))for(var d=a.length;d>0;)g=g.replace(/\d/,c),d--}var y=u.replace(new RegExp(o),g).replace(new RegExp("9","g"),c);return f||(i?y=h(c,i.length)+" "+y:a&&(y=h(c,a.length)+this.getSeparatorAfterNationalPrefix(t)+y)),r&&(y=(0,R.A)(y)),y}}},{key:"formatNextNationalNumberDigits",value:function(t){var e=function(t,e,n){for(var r,a=l(n.split(""));!(r=a()).done;){var i=r.value;if(t.slice(e+1).search(f)<0)return;e=t.search(f),t=t.replace(f,i)}return[t,e]}(this.populatedNationalNumberTemplate,this.populatedNationalNumberTemplatePosition,t);if(e)return this.populatedNationalNumberTemplate=e[0],this.populatedNationalNumberTemplatePosition=e[1],g(this.populatedNationalNumberTemplate,this.populatedNationalNumberTemplatePosition+1);this.resetFormat()}},{key:"shouldTryNationalPrefixFormattingRule",value:function(t,e){var n=e.international,r=e.nationalPrefix;if(t.nationalPrefixFormattingRule()){var a=t.usesNationalPrefix();if(a&&r||!a&&!n)return!0}}}])&&B(e.prototype,n),r&&B(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}(),K=new RegExp("(["+M.OA+"])");function V(t,e,n,r){if(e){var i=new a.Ay(r);i.selectNumberingPlan(e,n);var o=new RegExp(i.IDDPrefix());if(0===t.search(o)){var u=(t=t.slice(t.match(o)[0].length)).match(K);if(!(u&&null!=u[1]&&u[1].length>0&&"0"===u[1]))return t}}}var Z=n(2700);function J(t,e){if(t&&e.numberingPlan.nationalPrefixForParsing()){var n=new RegExp("^(?:"+e.numberingPlan.nationalPrefixForParsing()+")"),r=n.exec(t);if(r){var a,i,o,u=r.length-1,l=u>0&&r[u];if(e.nationalPrefixTransformRule()&&l)a=t.replace(n,e.nationalPrefixTransformRule()),u>1&&(i=r[1]);else{var s=r[0];a=t.slice(s.length),l&&(i=r[1])}if(l){var c=t.indexOf(r[1]);t.slice(0,c)===e.numberingPlan.nationalPrefix()&&(o=e.numberingPlan.nationalPrefix())}else o=r[0];return{nationalNumber:a,nationalPrefix:o,carrierCode:i}}}return{nationalNumber:t}}function X(t,e){var n=J(t,e),r=n.carrierCode,a=n.nationalNumber;if(a!==t){if(!function(t,e,n){if((0,Z.A)(t,n.nationalNumberPattern())&&!(0,Z.A)(e,n.nationalNumberPattern()))return!1;return!0}(t,a,e))return{nationalNumber:t};if(e.possibleLengths()&&!function(t,e){switch((0,m.A)(t,e)){case"TOO_SHORT":case"INVALID_LENGTH":return!1;default:return!0}}(a,e))return{nationalNumber:t}}return{nationalNumber:a,carrierCode:r}}function Y(t,e,n,r){var i=e?(0,a.Ko)(e,r):n;if(0===t.indexOf(i)){(r=new a.Ay(r)).selectNumberingPlan(e,n);var o=t.slice(i.length),u=X(o,r).nationalNumber,l=X(t,r).nationalNumber;if(!(0,Z.A)(l,r.nationalNumberPattern())&&(0,Z.A)(u,r.nationalNumberPattern())||"TOO_LONG"===(0,m.A)(l,r))return{countryCallingCode:i,number:o}}return{number:t}}function q(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==n)return;var r,a,i=[],o=!0,u=!1;try{for(n=n.call(t);!(o=(r=n.next()).done)&&(i.push(r.value),!e||i.length!==e);o=!0);}catch(t){u=!0,a=t}finally{try{o||null==n.return||n.return()}finally{if(u)throw a}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Q(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function z(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var tt="["+M.uD+M.OA+"]+",et=new RegExp("^"+tt+"$","i"),nt="(?:["+M.tz+"]["+M.uD+M.OA+"]*|["+M.uD+M.OA+"]+)",rt=new RegExp("[^"+M.uD+M.OA+"]+.*$"),at=/[^\d\[\]]/,it=function(){function t(e){var n=e.defaultCountry,r=e.defaultCallingCode,a=e.metadata,i=e.onNationalSignificantNumberChange;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.defaultCountry=n,this.defaultCallingCode=r,this.metadata=a,this.onNationalSignificantNumberChange=i}var e,n,r;return e=t,(n=[{key:"input",value:function(t,e){var n,r=function(t){var e=function(t){var e=function(t){var e,n=t.search(nt);if(!(n<0))return"+"===(t=t.slice(n))[0]&&(e=!0,t=t.slice(1)),t=t.replace(rt,""),e&&(t="+"+t),t}(t)||"";return"+"===e[0]?[e.slice(1),!0]:[e]}(t),n=q(e,2),r=n[0],a=n[1];return et.test(r)||(r=""),[r,a]}(t),a=q(r,2),i=a[0],o=a[1],u=b(i);return o&&(e.digits||(e.startInternationalNumber(),u||(n=!0))),u&&this.inputDigits(u,e),{digits:u,justLeadingPlus:n}}},{key:"inputDigits",value:function(t,e){var n=e.digits,r=n.length<3&&n.length+t.length>=3;if(e.appendDigits(t),r&&this.extractIddPrefix(e),this.isWaitingForCountryCallingCode(e)){if(!this.extractCountryCallingCode(e))return}else e.appendNationalSignificantNumberDigits(t);e.international||this.hasExtractedNationalSignificantNumber||this.extractNationalSignificantNumber(e.getNationalDigits(),function(t){return e.update(t)})}},{key:"isWaitingForCountryCallingCode",value:function(t){var e=t.international,n=t.callingCode;return e&&!n}},{key:"extractCountryCallingCode",value:function(t){var e=function(t,e,n,r){if(!t)return{};var i;if("+"!==t[0]){var o=V(t,e,n,r);if(!o||o===t){if(e||n){var u=Y(t,e,n,r),l=u.countryCallingCode,s=u.number;if(l)return{countryCallingCodeSource:"FROM_NUMBER_WITHOUT_PLUS_SIGN",countryCallingCode:l,number:s}}return{number:t}}i=!0,t="+"+o}if("0"===t[1])return{};r=new a.Ay(r);for(var c=2;c-1<=M.Jq&&c<=t.length;){var f=t.slice(1,c);if(r.hasCallingCode(f))return r.selectNumberingPlan(f),{countryCallingCodeSource:i?"FROM_NUMBER_WITH_IDD":"FROM_NUMBER_WITH_PLUS_SIGN",countryCallingCode:f,number:t.slice(c)};c++}return{}}("+"+t.getDigitsWithoutInternationalPrefix(),this.defaultCountry,this.defaultCallingCode,this.metadata.metadata),n=e.countryCallingCode,r=e.number;if(n)return t.setCallingCode(n),t.update({nationalSignificantNumber:r}),!0}},{key:"reset",value:function(t){if(t){this.hasSelectedNumberingPlan=!0;var e=t._nationalPrefixForParsing();this.couldPossiblyExtractAnotherNationalSignificantNumber=e&&at.test(e)}else this.hasSelectedNumberingPlan=void 0,this.couldPossiblyExtractAnotherNationalSignificantNumber=void 0}},{key:"extractNationalSignificantNumber",value:function(t,e){if(this.hasSelectedNumberingPlan){var n=J(t,this.metadata),r=n.nationalPrefix,a=n.nationalNumber,i=n.carrierCode;if(a!==t)return this.onExtractedNationalNumber(r,i,a,t,e),!0}}},{key:"extractAnotherNationalSignificantNumber",value:function(t,e,n){if(!this.hasExtractedNationalSignificantNumber)return this.extractNationalSignificantNumber(t,n);if(this.couldPossiblyExtractAnotherNationalSignificantNumber){var r=J(t,this.metadata),a=r.nationalPrefix,i=r.nationalNumber,o=r.carrierCode;if(i!==e)return this.onExtractedNationalNumber(a,o,i,t,n),!0}}},{key:"onExtractedNationalNumber",value:function(t,e,n,r,a){var i,o,u=r.lastIndexOf(n);if(u>=0&&u===r.length-n.length){o=!0;var l=r.slice(0,u);l!==t&&(i=l)}a({nationalPrefix:t,carrierCode:e,nationalSignificantNumber:n,nationalSignificantNumberMatchesInput:o,complexPrefixBeforeNationalSignificantNumber:i}),this.hasExtractedNationalSignificantNumber=!0,this.onNationalSignificantNumberChange()}},{key:"reExtractNationalSignificantNumber",value:function(t){return!!this.extractAnotherNationalSignificantNumber(t.getNationalDigits(),t.nationalSignificantNumber,function(e){return t.update(e)})||(this.extractIddPrefix(t)||this.fixMissingPlus(t)?(this.extractCallingCodeAndNationalSignificantNumber(t),!0):void 0)}},{key:"extractIddPrefix",value:function(t){var e=t.international,n=t.IDDPrefix,r=t.digits;if(t.nationalSignificantNumber,!e&&!n){var a=V(r,this.defaultCountry,this.defaultCallingCode,this.metadata.metadata);return void 0!==a&&a!==r?(t.update({IDDPrefix:r.slice(0,r.length-a.length)}),this.startInternationalNumber(t,{country:void 0,callingCode:void 0}),!0):void 0}}},{key:"fixMissingPlus",value:function(t){if(!t.international){var e=Y(t.digits,this.defaultCountry,this.defaultCallingCode,this.metadata.metadata),n=e.countryCallingCode;if(e.number,n)return t.update({missingPlus:!0}),this.startInternationalNumber(t,{country:t.country,callingCode:n}),!0}}},{key:"startInternationalNumber",value:function(t,e){var n=e.country,r=e.callingCode;t.startInternationalNumber(n,r),t.nationalSignificantNumber&&(t.resetNationalSignificantNumber(),this.onNationalSignificantNumberChange(),this.hasExtractedNationalSignificantNumber=void 0)}},{key:"extractCallingCodeAndNationalSignificantNumber",value:function(t){this.extractCountryCallingCode(t)&&this.extractNationalSignificantNumber(t.getNationalDigits(),function(e){return t.update(e)})}}])&&z(e.prototype,n),r&&z(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();var ot=n(9386);function ut(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return lt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return lt(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function lt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function st(t,e){var n=e.countries,r=(e.defaultCountry,e.metadata);r=new a.Ay(r);for(var i,o=ut(n);!(i=o()).done;){var u=i.value;if(r.country(u),r.leadingDigits()){if(t&&0===t.search(r.leadingDigits()))return u}else if((0,ot.A)({phone:t,country:u},void 0,r.metadata))return u}}var ct=n(8048);function ft(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==n)return;var r,a,i=[],o=!0,u=!1;try{for(n=n.call(t);!(o=(r=n.next()).done)&&(i.push(r.value),!e||i.length!==e);o=!0);}catch(t){u=!0,a=t}finally{try{o||null==n.return||n.return()}finally{if(u)throw a}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ht(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ht(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function gt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var mt=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.metadata=new a.Ay(n);var r=ft(this.getCountryAndCallingCode(e),2),i=r[0],o=r[1];this.defaultCountry=i,this.defaultCallingCode=o,this.reset()}var e,n,r;return e=t,(n=[{key:"getCountryAndCallingCode",value:function(t){var e,n;return t&&((0,ct.A)(t)?(e=t.defaultCountry,n=t.defaultCallingCode):e=t),e&&!this.metadata.hasCountry(e)&&(e=void 0),[e,n]}},{key:"input",value:function(t){var e=this.parser.input(t,this.state),n=e.digits;if(e.justLeadingPlus)this.formattedOutput="+";else if(n){var r;if(this.determineTheCountryIfNeeded(),this.state.nationalSignificantNumber&&this.formatter.narrowDownMatchingFormats(this.state),this.metadata.hasSelectedNumberingPlan()&&(r=this.formatter.format(n,this.state)),void 0===r&&this.parser.reExtractNationalSignificantNumber(this.state)){this.determineTheCountryIfNeeded();var a=this.state.getNationalDigits();a&&(r=this.formatter.format(a,this.state))}this.formattedOutput=r?this.getFullNumber(r):this.getNonFormattedNumber()}return this.formattedOutput}},{key:"reset",value:function(){var t=this;return this.state=new u({onCountryChange:function(e){t.country=e},onCallingCodeChange:function(e,n){t.metadata.selectNumberingPlan(n,e),t.formatter.reset(t.metadata.numberingPlan,t.state),t.parser.reset(t.metadata.numberingPlan)}}),this.formatter=new H({state:this.state,metadata:this.metadata}),this.parser=new it({defaultCountry:this.defaultCountry,defaultCallingCode:this.defaultCallingCode,metadata:this.metadata,state:this.state,onNationalSignificantNumberChange:function(){t.determineTheCountryIfNeeded(),t.formatter.reset(t.metadata.numberingPlan,t.state)}}),this.state.reset({country:this.defaultCountry,callingCode:this.defaultCallingCode}),this.formattedOutput="",this}},{key:"isInternational",value:function(){return this.state.international}},{key:"getCallingCode",value:function(){if(this.isInternational())return this.state.callingCode}},{key:"getCountryCallingCode",value:function(){return this.getCallingCode()}},{key:"getCountry",value:function(){if(this.state.digits)return this._getCountry()}},{key:"_getCountry",value:function(){var t=this.state.country;return t}},{key:"determineTheCountryIfNeeded",value:function(){this.state.country&&!this.isCountryCallingCodeAmbiguous()||this.determineTheCountry()}},{key:"getFullNumber",value:function(t){var e=this;if(this.isInternational()){var n=function(t){return e.formatter.getInternationalPrefixBeforeCountryCallingCode(e.state,{spacing:!!t})+t},r=this.state.callingCode;return n(r?t?"".concat(r," ").concat(t):r:"".concat(this.state.getDigitsWithoutInternationalPrefix()))}return t}},{key:"getNonFormattedNationalNumberWithPrefix",value:function(){var t=this.state,e=t.nationalSignificantNumber,n=t.complexPrefixBeforeNationalSignificantNumber,r=t.nationalPrefix,a=e,i=n||r;return i&&(a=i+a),a}},{key:"getNonFormattedNumber",value:function(){var t=this.state.nationalSignificantNumberMatchesInput;return this.getFullNumber(t?this.getNonFormattedNationalNumberWithPrefix():this.state.getNationalDigits())}},{key:"getNonFormattedTemplate",value:function(){var t=this.getNonFormattedNumber();if(t)return t.replace(/[\+\d]/g,c)}},{key:"isCountryCallingCodeAmbiguous",value:function(){var t=this.state.callingCode,e=this.metadata.getCountryCodesForCallingCode(t);return e&&e.length>1}},{key:"determineTheCountry",value:function(){this.state.setCountry(function(t,e){var n=e.nationalNumber,r=e.defaultCountry,a=e.metadata,i=a.getCountryCodesForCallingCode(t);if(i)return 1===i.length?i[0]:st(n,{countries:i,defaultCountry:r,metadata:a.metadata})}(this.isInternational()?this.state.callingCode:this.defaultCallingCode,{nationalNumber:this.state.nationalSignificantNumber,defaultCountry:this.defaultCountry,metadata:this.metadata}))}},{key:"getNumberValue",value:function(){var t=this.state,e=t.digits,n=t.callingCode,r=t.country,a=t.nationalSignificantNumber;if(e)return this.isInternational()?n?"+"+n+a:"+"+e:r||n?"+"+(r?this.metadata.countryCallingCode():n)+a:void 0}},{key:"getNumber",value:function(){var t=this.state,e=t.nationalSignificantNumber,n=t.carrierCode,r=t.callingCode,o=this._getCountry();if(e&&(o||r)){if(o&&o===this.defaultCountry){var u=new a.Ay(this.metadata.metadata);u.selectNumberingPlan(o);var l=u.numberingPlan.callingCode(),s=this.metadata.getCountryCodesForCallingCode(l);if(s.length>1){var c=st(e,{countries:s,defaultCountry:this.defaultCountry,metadata:this.metadata.metadata});c&&(o=c)}}var f=new i.A(o||r,e,this.metadata.metadata);return n&&(f.carrierCode=n),f}}},{key:"isPossible",value:function(){var t=this.getNumber();return!!t&&t.isPossible()}},{key:"isValid",value:function(){var t=this.getNumber();return!!t&&t.isValid()}},{key:"getNationalNumber",value:function(){return this.state.nationalSignificantNumber}},{key:"getChars",value:function(){return(this.state.international?"+":"")+this.state.digits}},{key:"getTemplate",value:function(){return this.formatter.getTemplate(this.state)||this.getNonFormattedTemplate()||""}}])&&gt(e.prototype,n),r&&gt(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function dt(t){return mt.call(this,t,r.A)}dt.prototype=Object.create(mt.prototype,{}),dt.prototype.constructor=dt},8988:function(t,e,n){n.d(e,{A:function(){return i},_:function(){return a}});var r=n(3315),a=/(\$\d)/;function i(t,e,n){var i=n.useInternationalFormat,o=n.withNationalPrefix,u=(n.carrierCode,n.metadata,t.replace(new RegExp(e.pattern()),i?e.internationalFormat():o&&e.nationalPrefixFormattingRule()?e.format().replace(a,e.nationalPrefixFormattingRule()):e.format()));return i?(0,r.A)(u):u}},9386:function(t,e,n){n.d(e,{A:function(){return l}});var r=n(8171),a=n(2700);function i(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var u=["MOBILE","PREMIUM_RATE","TOLL_FREE","SHARED_COST","VOIP","PERSONAL_NUMBER","PAGER","UAN","VOICEMAIL"];function l(t,e,n){if(e=e||{},t.country||t.countryCallingCode){(n=new r.Ay(n)).selectNumberingPlan(t.country,t.countryCallingCode);var o=e.v2?t.nationalNumber:t.phone;if((0,a.A)(o,n.nationalNumberPattern())){if(s(o,"FIXED_LINE",n))return n.type("MOBILE")&&""===n.type("MOBILE").pattern()?"FIXED_LINE_OR_MOBILE":n.type("MOBILE")?s(o,"MOBILE",n)?"FIXED_LINE_OR_MOBILE":"FIXED_LINE":"FIXED_LINE_OR_MOBILE";for(var l,c=i(u);!(l=c()).done;){var f=l.value;if(s(o,f,n))return f}}}}function s(t,e,n){return!(!(e=n.type(e))||!e.pattern())&&(!(e.possibleLengths()&&e.possibleLengths().indexOf(t.length)<0)&&(0,a.A)(t,e.pattern()))}}}]);
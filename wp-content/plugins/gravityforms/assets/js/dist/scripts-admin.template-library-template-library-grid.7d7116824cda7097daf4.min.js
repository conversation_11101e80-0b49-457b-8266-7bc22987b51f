"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[441],{1159:function(e,t,a){a.r(t);var r=a(8134),i=a(7616),n=a(9652),l=a.n(n),c=a(6243),s=a.n(c),m=a(8045),o=a.n(m),u=a(5078);t.default=function(e){var t,a,n,c=e.blankOnClick,m=void 0===c?function(){}:c,g=e.licenseType,p=void 0===g?"":g,d=e.strings,b=void 0===d?{}:d,f=e.templateOnClick,k=void 0===f?function(){}:f,v=e.templates,y=void 0===v?[]:v,h=e.thumbnailUrl,_=void 0===h?"":h;return i.React.createElement(i.SimpleBar,null,i.React.createElement("div",{className:"gform-template-library__card-grid-container"},i.React.createElement(l(),{container:!0,wrap:!0,rowSpacing:6,columnSpacing:6,customClasses:["gform-template-library__card-grid"],justifyContent:"flex-start"},(t={headingAttributes:{content:b.blankForm,weight:"medium",size:"text-sm",tagName:"h2"},textAttributes:{content:b.createForm,size:"text-sm"},blankButtonAttributes:{onClick:m},imageAttributes:{asBg:!0,url:"https://i.imgur.com/KsZxvrs.png",altText:b.blankForm},style:"form-template-blank"},a=i.React.createElement(l(),{key:0,customClasses:["gform-template-library__card-grid-item"],item:!0},i.React.createElement(o(),t)),n=y.map(function(e,t){var a=p&&p.slice(2).toLowerCase()||"single",r=(0,u.requiresUpgrade)({accessLevels:e.template_access_level,licenseLevel:a})?b.upgradeTag:"",n=b.useTemplateWithTitle.split("%s"),c=b.previewWithTitle.split("%s"),m={customClasses:["gform-card__form-template-secondary-button-icon"],icon:"external-link",iconPrefix:"gform-icon"},g={bgColor:e.template_background,headingAttributes:{content:e.title,weight:"medium",size:"text-sm",tagName:"h2"},primaryCtaAttrs:{ctaType:"button",children:i.React.createElement(i.React.Fragment,null,n[0],i.React.createElement("span",{className:"gform-visually-hidden"},'"'.concat(e.title,'"')),n[1]),onClick:k(e)},secondaryCtaAttrs:{ctaType:"link",children:i.React.createElement(i.React.Fragment,null,i.React.createElement(s(),m),c[0],i.React.createElement("span",{className:"gform-visually-hidden"},'"'.concat(e.title,'"')),c[1]),href:e.template_preview_url,target:"_blank"},imageAttributes:{asBg:!0,url:"".concat(_).concat(e.template_thumbnail),imagePosition:"top center",imageAttributes:{style:{backgroundSize:"100%"}},altText:e.title},tagAttributes:{content:r,size:"text-xxs"},style:"form-template"};return i.React.createElement(l(),{key:t+1,customClasses:["gform-template-library__card-grid-item"],item:!0},i.React.createElement(o(),g))}),[a].concat((0,r.A)(n))))))}}}]);
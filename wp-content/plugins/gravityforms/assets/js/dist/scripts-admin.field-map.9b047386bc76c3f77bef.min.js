"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[392],{6279:function(e,t,n){n.r(t),n.d(t,{FieldMap:function(){return N}});var a=n(1873),r=n(7113),i=n(0),c=n(1118),o=n(7821),l=n(527);function s(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],function(){}))}catch(e){}return(s=function(){return!!e})()}var u=wp.element,p=u.Component,g=(u.Fragment,function(e){function t(){return(0,a.A)(this,t),e=this,n=t,r=arguments,n=(0,c.A)(n),(0,i.A)(e,s()?Reflect.construct(n,r||[],(0,c.A)(e).constructor):n.apply(e,r));var e,n,r}return(0,o.A)(t,e),(0,r.A)(t,[{key:"render",value:function(){return this.props.tooltip?React.createElement("button",{type:"button",className:"gf_tooltip tooltip","aria-label":this.props.tooltip},React.createElement("i",{className:"gform-icon gform-icon--question-mark","aria-hidden":"true"})):null}}])}(p)),m=n(6940);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach(function(t){(0,l.A)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(f=function(){return!!e})()}var v=wp.element.Component,_=window.gform_admin_config.field_map.i18n,y=function(e){function t(e){var n,r,o,l;return(0,a.A)(this,t),r=this,o=t,l=[e],o=(0,c.A)(o),(n=(0,i.A)(r,f()?Reflect.construct(o,l||[],(0,c.A)(r).constructor):o.apply(r,l))).resizeObserver=null,n.state={hasScrollbar:!1},n}return(0,o.A)(t,e),(0,r.A)(t,[{key:"componentDidMount",value:function(){var e=this;"undefined"!=typeof form&&(0,m.A)();var t=this.input||this.textarea;if(jQuery(t).on("propertychange",function(t){e.props.updateMapping(h(h({},e.props.mapping),{},{custom_value:t.target.value}),e.props.index)}),this.textarea){var n=this.textarea.scrollHeight>400?400:this.textarea.scrollHeight+2;this.textarea.style.height="".concat(n,"px"),this.resizeObserver=new ResizeObserver(function(){e.setState({hasScrollbar:e.textarea.scrollHeight>e.textarea.clientHeight})}),this.resizeObserver.observe(this.textarea)}}},{key:"componentWillUnmount",value:function(){this.resizeObserver&&this.textarea&&this.resizeObserver.unobserve(this.textarea)}},{key:"renderInput",value:function(){var e=this,t=this.props,n=t.fieldId,a=t.mapping,r=t.valueField,i=t.updateMapping,c=t.index,o=this.props.mergeTagSupport?"merge-tag-support mt-position-right":"";return"textarea"===r.custom_value_type?React.createElement("textarea",{ref:function(t){e.textarea=t},id:n,className:o,value:a.custom_value||"",placeholder:r.placeholder,onChange:function(t){i(h(h({},a),{},{custom_value:t.target.value}),c),e.setState({hasScrollbar:t.target.scrollHeight>t.target.clientHeight})}}):React.createElement("input",{type:"text",ref:function(t){return e.input=t},id:n,className:o,value:a.custom_value||"",placeholder:r.placeholder,onChange:function(e){return i(h(h({},a),{},{custom_value:e.target.value}),c)}})}},{key:"render",value:function(){var e=this,t=this.props.mergeTagSupport?"gform-settings-generic-map__custom gform-settings-input__container--with-merge-tag":"gform-settings-generic-map__custom";return React.createElement("span",{className:t},this.renderInput(),React.createElement("button",{className:"gform-settings-generic-map__reset ".concat(this.state.hasScrollbar?"gform-settings-generic-map__reset--with-scrollbar":""),onClick:function(t){t.preventDefault(),e.props.updateMapping(h(h({},e.props.mapping),{},{value:"",custom_value:""}),e.props.index)}},React.createElement("span",{className:"screen-reader-text"},_.remove_custom_value)))}}])}(v);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach(function(t){(0,l.A)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function R(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(R=function(){return!!e})()}var O=wp.element,E=O.Component,M=O.Fragment,w=window.gform_admin_config.field_map.i18n,x=function(e){function t(){return(0,a.A)(this,t),e=this,n=t,r=arguments,n=(0,c.A)(n),(0,i.A)(e,R()?Reflect.construct(n,r||[],(0,c.A)(e).constructor):n.apply(e,r));var e,n,r}return(0,o.A)(t,e),(0,r.A)(t,[{key:"renderRequiredSpan",value:function(){var e=this.props.choice,t=this.getKeyInputId();return e.required?React.createElement("span",{className:"required",id:t},"*"):null}},{key:"render",value:function(){var e=this.props,t=e.isInvalid,n=e.index;return React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--key"},this.getKeyInput(n)),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--value"},this.getValueInput()),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--error"},t&&React.createElement("svg",{width:"22",height:"22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M11 22C4.9249 22 0 17.0751 0 11S4.9249 0 11 0s11 4.9249 11 11-4.9249 11-11 11z",fill:"#E54C3B"}),React.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.9317 5.0769a.1911.1911 0 00-.1909.2006l.3708 7.4158a.8895.8895 0 001.7768 0l.3708-7.4158a.1911.1911 0 00-.1909-.2006H9.9317zm2.3375 10.5769c0 .701-.5682 1.2693-1.2692 1.2693-.701 0-1.2692-.5683-1.2692-1.2693 0-.7009.5682-1.2692 1.2692-1.2692.701 0 1.2692.5683 1.2692 1.2692z",fill:"#fff"}))),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--buttons"},this.getAddButton(),this.getDeleteButton()))}},{key:"getValueInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,a=e.index,r=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_value_").concat(a);default:return"".concat(t,"_").concat(r.key)}}},{key:"getKeyInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,a=e.index,r=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_key_").concat(a);default:return"".concat(t,"_").concat(r.key,"_key")}}},{key:"getKeyInput",value:function(e){var t=this.props,n=t.choice,a=t.keyField,r=t.index,i=t.mapping,c=t.updateMapping,o=a.choices,l=a.display_all,s=a.placeholder,u=this.getKeyInputId();return n.required||l?React.createElement(M,null,React.createElement("label",null,n.label," ",this.renderRequiredSpan()," "),React.createElement(g,{tooltip:n.tooltip})):"gf_custom"===i.key?React.createElement("span",{className:"gform-settings-generic-map__custom"},React.createElement("input",{id:u,type:"text",value:i.custom_key,placeholder:s,onChange:function(e){return c(b(b({},i),{},{custom_key:e.target.value}),r)}}),o.length>0&&React.createElement("button",{className:"gform-settings-generic-map__reset",onClick:function(e){e.preventDefault(),c(b(b({},i),{},{key:"",custom_key:""}),r)}},React.createElement("span",{className:"screen-reader-text"},w.add_custom_key))):React.createElement("select",{id:u,value:i.key,onChange:function(e){return c(b(b({},i),{},{key:e.target.value}),r)}},this.getKeyOptions(e))}},{key:"getKeyOptions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],r=this.props,i=r.keyField,c=r.mappedChoices,o=r.mapping,l=i.allow_custom,s=i.allow_duplicates;t||(t=i.choices);var u=t.map(function(e){return e.name||e.value}),p="select-".concat(e,"-optiongroup-"),g=[];!u.includes("")&&n&&g.push(React.createElement("option",{key:"".concat(p,"-default"),value:"",disabled:!1},w.select_a_field));for(var m=0;m<t.length;m++){var d=t[m],h=d.name||d.value;if(!d.required){var f=c.includes(h)&&h!==o.key&&!s;d.choices&&d.choices.length>0?g.push(React.createElement("optgroup",{label:d.label,key:"".concat(p,"-").concat(m)},this.getKeyOptions("".concat(e,".").concat(m),d.choices,!1,!1))):g.push(React.createElement("option",{key:"".concat(p,"-").concat(m),value:d.value,disabled:f},d.label))}}return l&&!u.includes("gf_custom")&&a&&g.push(React.createElement("option",{key:"".concat(p,"-custom"),value:"gf_custom",disabled:!1},w.add_custom_key)),g}},{key:"getValueInput",value:function(){var e=this.props,t=e.choice,n=e.index,a=e.isInvalid,r=e.mapping,i=e.updateMapping,c=e.valueField,o=e.mergeTagSupport,l=t.required,s=this.getValueInputId();return"gf_custom"===r.value?React.createElement(y,{choice:t,index:n,isInvalid:a,mapping:r,updateMapping:i,valueField:c,mergeTagSupport:o,fieldId:s}," "):React.createElement("select",{id:s,disabled:""===r.key||!r.key,value:r.value,onChange:function(e){return i(b(b({},r),{},{value:e.target.value}),n)},className:a?"gform-settings-generic-map__value--invalid":"",required:l},this.getValueOptions().map(function(e){return e.choices&&e.choices.length>0?React.createElement("optgroup",{key:e.label,label:e.label},e.choices.map(function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)})):React.createElement("option",{key:e.value,value:e.value},e.label)}))}},{key:"getValueOptions",value:function(){var e=this.props,t=e.choice,n=e.valueField,a=n.allow_custom,r=t.name&&n.choice_keys&&n.choice_keys[t.name]?n.choice_keys[t.name]:"default",i=t.choices||n.choices[r];i||(i=[]);var c=i.map(function(e){return e.value});return a&&!c.includes("gf_custom")&&i.push({label:w.add_custom_value,value:"gf_custom",disabled:!1}),gform.applyFilters("gform_generic_map_field_choices",i,this.props.choice,{mapping:this.props.mapping,mappedChoices:this.props.mappedChoices,isInvalid:this.props.isInvalid,keyField:this.props.keyField,valueField:this.props.valueField,canAdd:this.props.canAdd,canDelete:this.props.canDelete,index:this.props.index,inputId:this.props.inputId,inputType:this.props.inputType,mergeTagSupport:this.props.mergeTagSupport})}},{key:"getAddButton",value:function(){var e=this.props,t=e.canAdd,n=e.addMapping,a=e.index;return t?React.createElement("button",{className:"add_field_choice gform-st-icon gform-st-icon--circle-plus gform-settings-generic-map__button gform-settings-generic-map__button--add",onClick:function(e){e.preventDefault(),n(a)}},React.createElement("span",{className:"screen-reader-text"},w.add)):null}},{key:"getDeleteButton",value:function(){var e=this.props,t=e.canDelete,n=e.deleteMapping,a=e.index;return t?React.createElement("button",{className:"delete_field_choice gform-st-icon gform-st-icon--circle-minus gform-settings-generic-map__button gform-settings-generic-map__button--delete",onClick:function(e){e.preventDefault(),n(a)}},React.createElement("span",{className:"screen-reader-text"},w.delete)):null}}])}(E);function A(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(A=function(){return!!e})()}var C=wp.element,I=C.Component,N=(C.Fragment,window.gform_admin_config.field_map.i18n,function(e){function t(){var e,n,r,o;return(0,a.A)(this,t),n=this,r=t,o=arguments,r=(0,c.A)(r),(e=(0,i.A)(n,A()?Reflect.construct(r,o||[],(0,c.A)(n).constructor):r.apply(n,o))).state={mapping:JSON.parse(document.querySelector('[name="'.concat(e.props.input,'"]')).value)},e.addMapping=e.addMapping.bind(e),e.deleteMapping=e.deleteMapping.bind(e),e.getMapping=e.getMapping.bind(e),e.updateMapping=e.updateMapping.bind(e),e}return(0,o.A)(t,e),(0,r.A)(t,[{key:"componentDidMount",value:function(){this.populateRequiredMappings(),0===this.getRequiredChoices().length&&this.getMapping().length<1&&this.addMapping(0)}},{key:"addMapping",value:function(e){var t=this.props.keyField,n=t.allow_custom,a=t.choices,r=this.getMapping(),i=0===a.length&&n?"gf_custom":"";r.splice(e+1,0,{key:i,custom_key:"",value:"",custom_value:""}),this.setMapping(r)}},{key:"deleteMapping",value:function(e){var t=this.getMapping();t.splice(e,1),this.setMapping(t)}},{key:"getMapping",value:function(){return this.state.mapping}},{key:"setMapping",value:function(e){var t=this.props.input;this.setState({mapping:e}),document.querySelector('[name="'.concat(t,'"]')).value=JSON.stringify(e)}},{key:"updateMapping",value:function(e,t){var n=this.getMapping();e.key||(e.value=""),n[t]=e,this.setMapping(n)}},{key:"getChoice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||(t=this.props.keyField.choices);for(var n=0;n<t.length;n++){var a=t[n];if((a.name||a.value)===e)return t[n];if(a.choices){var r=this.getChoice(e,a.choices);if(r)return r}}return!1}},{key:"getMappedChoices",value:function(){return this.getMapping().filter(function(e){return e.key&&"gf_custom"!==e.key}).map(function(e){return e.key})}},{key:"getRequiredChoices",value:function(){for(var e=this.props.keyField,t=e.choices,n=e.display_all,a=[],r=0;r<t.length;r++){var i=t[r];if((i.required||n)&&a.push(i.name||i.value),i.choices)for(var c=0;c<i.choices.length;c++){var o=i.choices[c];(o.required||n)&&a.push(o.name||o.value)}}return a}},{key:"populateRequiredMappings",value:function(){for(var e=this.getMapping(),t=this.getRequiredChoices(),n=e.map(function(e){return e.key}),a=0;a<t.length;a++)n.includes(t[a])||e.push({key:t[a],custom_key:"",value:"",custom_value:""});for(var r=0;r<e.length;r++)if(""===e[r].value){var i=this.getChoice(e[r].key);i&&"default_value"in i&&(e[r].value=i.default_value)}this.setMapping(e)}},{key:"countKeyFieldChoices",value:function(){for(var e=this.props.keyField.choices,t=0,n=0;n<e.length;n++)e[n].choices?t+=e[n].choices.length:t++;return t}},{key:"render",value:function(){var e=this,t=this.props,n=t.keyField,a=t.invalidChoices,r=t.limit,i=t.valueField,c=t.input,o=t.inputType,l=t.mergeTagSupport,s=this.getMapping(),u=this.countKeyFieldChoices();return React.createElement("table",{className:"gform-settings-generic-map__table",cellSpacing:"0",cellPadding:"0"},React.createElement("tbody",null,React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--key"},n.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--value"},i.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--error"}),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--buttons"})),s.map(function(t,p){var g=e.getChoice(t.key);return React.createElement(x,{key:p,mapping:t,choice:g,mappedChoices:e.getMappedChoices(),isInvalid:t.key&&a.includes(t.key),keyField:n,valueField:i,canAdd:n.allow_custom&&(0===r||s.length<=r)||!n.allow_custom&&s.length<u,canDelete:s.length>1&&!g.required&&!n.display_all,addMapping:e.addMapping,deleteMapping:e.deleteMapping,updateMapping:e.updateMapping,index:p,inputId:c,inputType:o,mergeTagSupport:l})})))}}])}(I))}}]);
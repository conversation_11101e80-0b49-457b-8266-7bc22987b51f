(()=>{var r=CryptoJS,c=r.lib.WordArray;r.enc.Base64={stringify:function(r){var a=r.words,t=r.sigBytes,n=this._map;r.clamp(),r=[];for(var e=0;e<t;e+=3)for(var i=(a[e>>>2]>>>24-e%4*8&255)<<16|(a[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|a[e+2>>>2]>>>24-(e+2)%4*8&255,f=0;f<4&&e+.75*f<t;f++)r.push(n.charAt(i>>>6*(3-f)&63));if(a=n.charAt(64))for(;r.length%4;)r.push(a);return r.join("")},parse:function(r){var a=r.length,t=this._map;(i=t.charAt(64))&&-1!=(i=r.indexOf(i))&&(a=i);for(var n,e,i=[],f=0,h=0;h<a;h++)h%4&&(n=t.indexOf(r.charAt(h-1))<<h%4*2,e=t.indexOf(r.charAt(h))>>>6-h%4*2,i[f>>>2]|=(n|e)<<24-f%4*8,f++);return c.create(i,f)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}})();
/* ------------------ Field Map ------------------ */

.gforms_form_settings.form-table .settings-field-map-table thead th {
	font-weight: bold;
	text-align: left;
	padding-left:0px;
}

table.settings-field-map-table tbody td {
	padding: 0px 0px 8px 0px;
}

.settings-field-map-table td:first-child {
	width: 220px;
}

.settings-field-map-table td:last-child {
	white-space: nowrap;
}

.settings-field-map-table .repeater th, .settings-field-map-table .repeater td:nth-child(2) {
	padding-left: 0px;
	padding-top: 0px;
	width: 220px;
}

.settings-field-map-table select {
	font-family: inherit;
	height: 25px;
	width: 210px;
}

.settings-field-map-table .chosen-container,
.settings-field-map-table .select2-container {
	width: 210px !important;
}

.settings-field-map-table .custom-key-container,
.settings-field-map-table .custom-value-container {
	position: relative;
	width: 210px;
}

.settings-field-map-table .custom-key-container input,
.settings-field-map-table .custom-value-container input {
	width: 210px;
}

.settings-field-map-table .custom-key-container input:not(:only-child),
.settings-field-map-table .custom-value-container input:not(:only-child) {
	padding-right: 30px;
}

.settings-field-map-table .custom-key-container.supports-merge-tags input:not(:only-child),
.settings-field-map-table .custom-value-container.supports-merge-tags input:not(:only-child) {
	padding-right: 50px;
}

.settings-field-map-table .custom-value-container.supports-merge-tags .all-merge-tags {
	height: 25px;
	position: absolute;
	right: 36px;
	top: 0;
}

.settings-field-map-table .custom-value-container.supports-merge-tags .all-merge-tags .tooltip-merge-tag {
	background-position: center;
	height: 25px;
	margin: 0;
	width: 25px;
}

.settings-field-map-table .custom-key-reset,
.settings-field-map-table .custom-value-reset {
	background: url( '../images/field-map-reset.png' ) no-repeat center #ddd;
	background-size: 10px 10px;
	cursor: pointer;
	display: inline-block;
	height: 25px;
	opacity: .3;
	overflow: hidden;
	position: absolute;
	right: 11px;
	text-indent: -9999px;
	top: 0;
	transition: opacity .25s ease-in-out;
	width: 25px;
	z-index: 2;
}

.settings-field-map-table .custom-key-reset:hover,
.settings-field-map-table .custom-value-reset:hover {
	opacity: 1;
}

.settings-field-map-table .add-item span,
.settings-field-map-table .remove-item span {
	background: url( '../images/field-map-buttons.png' ) no-repeat center top transparent;
	background-size: 20px 100px;
	cursor: pointer;
	display: inline-block;
	height: 25px;
	overflow: hidden;
	text-indent: -9999px;
	width: 20px;
}

.settings-field-map-table .add-item span:hover {
	background-position: 0 -25px;
}

.settings-field-map-table .remove-item span {
	background-position: 0 -50px;
}

.settings-field-map-table .remove-item span:hover {
	background-position: 0 -75px;
}

@media screen and ( max-width: 782px ) {

	.settings-field-map-table .custom-key-container input:not(:only-child),
	.settings-field-map-table .custom-value-container input:not(:only-child) {
		padding-right: 45px;
	}

	.settings-field-map-table .custom-key-reset,
	.settings-field-map-table .custom-value-reset {
		height: 40px;
		right: 0;
		width: 40px;
	}

}


/* ---------------- Select Custom ---------------- */

.gaddon-setting-select-custom-container .select-custom-reset {
	background: url( ../../../images/xit.gif ) no-repeat scroll 0 0 transparent;
	cursor:pointer;
	display:none;
	position:absolute;
	text-indent:-9999px;
	width:10px;
	height: 10px;
	-moz-transition: none;
	-webkit-transition: none;
	-o-transition: color 0 ease-in;
	transition: none;
	z-index: 99;
}

.gaddon-setting-select-custom-container .select-custom-reset:hover { background-position-x: -10px; }

.gaddon-setting-select-custom-container:hover .select-custom-reset {
	display:block;
}

.gaddon-setting-select-custom-container {
	display:inline-block;
	position:relative;
	width: 210px;
}
.gaddon-setting-select-custom-container .select-custom-reset {
	left: 171px;
	top: 10px;
}
.gaddon-section .required { color: #f00; }
.gaddon-setting-inline{
    display:inline;
    margin-right:6px;
}

.gaddon-section-description ol { }
.gaddon-section-description ol li {
    list-style: decimal;
}

.repeater-buttons .add-item {
	margin-right: 6px;
}

.add-item, .remove-item {
	color: #444;
}

.mt-gaddon-editor {
	float: right;
	position: relative;
	right: 0.625rem;
	top: 2.8125rem;
	z-index: 1;
}

div#tab_notification .mt-gaddon-editor {
	top: 3.3125rem;
}

html[dir="rtl"] .mt-gaddon-editor {
	float: left;
}

.mt-gaddon-editor ~ .wp-editor-wrap {
	margin-right: 23px;
}

/* Visual Radio Buttons */
.gaddon-setting-choice-visual {
	display: inline-block;
	margin-bottom: 5px;
	text-align: center;
}

.gaddon-setting-choice-visual label {
	background: #F9F9F9;
	border: 1px solid #eee;
	display: inline-block;
}

.gaddon-setting-choice-visual label > span {
	display: inline-block;
	-webkit-filter: brightness( 1.8 ) grayscale( 1 ) opacity( .5 );
	-moz-filter: brightness( 1.8 ) grayscale( 1 ) opacity( .5 );
	filter: brightness( 1.8 ) grayscale( 1 ) opacity( .5 );
	height: 65px;
	min-width: 110px;
	padding: 5px 10px 0;
	-webkit-transition: all 100ms ease-in;
	-moz-transition: all 100ms ease-in;
	transition: all 100ms ease-in;
	vertical-align: top;
}

.gaddon-setting-choice-visual label > span > i {
	color: #0074a2;
	display: inline-block;
	font-size: 2.5em;
	height: 32px;
	margin: 5px;
	width: 32px;
}

.gaddon-setting-choice-visual label > span > img{
    height: 32px;
    margin: 5px;
    vertical-align: middle;
    width: 32px;
}

.gaddon-setting-choice-visual input {
	display: none;
}

.gaddon-setting-choice-visual input:checked + label {
	background-color: #fff;
	border: 1px solid #ccc;
}

.gaddon-setting-choice-visual input:checked + label > span {
	-webkit-filter: none;
	-moz-filter: none;
	filter: none;
}

.gaddon-setting-choice-visual input:not([disabled]):not([checked]) + label > span:hover{
    -webkit-filter: brightness(1.2) grayscale(.5) opacity(.9);
    -moz-filter: brightness(1.2) grayscale(.5) opacity(.9);
    filter: brightness(1.2) grayscale(.5) opacity(.9);
}

/* Feed Ordering */
.ui-sortable-helper {
	background-color: #fff !important;
	-webkit-box-shadow: 6px 6px 28px -9px rgba(0,0,0,0.75);
	-moz-box-shadow: 6px 6px 28px -9px rgba(0,0,0,0.75);
	box-shadow: 6px 6px 28px -9px rgba(0,0,0,0.75);
	transform: rotate(1deg);
	-moz-transform: rotate(1deg);
	-webkit-transform: rotate(1deg);
}

.wp-list-table.feed-list-sortable .sort-column {
	vertical-align: top;
	width: 2.2em;
}

.wp-list-table.feed-list-sortable .feed-sort-handle {
	cursor: move;
	font-size: 1.25rem;
	width: 2.2em;
}

/* ------------------ Feed List ------------------ */
@media screen and ( max-width: 782px ) {
	.wp-list-table tbody tr:not(.inline-edit-row):not(.no-items) td:not(.column-primary)::before {
		content: attr(data-colname) ":";
		font-weight: bold;
	}

	.wp-list-table.feeds .manage-column {
		vertical-align: top;
	}

	.wp-list-table.feeds .manage-column img {
		margin-top: 16px;
	}
}

function gform_initialize_tooltips(){var t=jQuery(".gf_tooltip");t.length&&t.tooltip({show:{effect:"fadeIn",duration:200,delay:100},position:{my:"center bottom",at:"center-3 top-11"},tooltipClass:"arrow-bottom",items:"[aria-label]",content:function(){return gform_strip_scripts(jQuery(this).attr("aria-label"))},open:function(t,e){if(void 0===t.originalEvent)return!1;setTimeout(function(){var t=(this.getBoundingClientRect().left-(e.tooltip[0].offsetWidth/2-5)).toFixed(3);e.tooltip.css("left",t+"px")}.bind(this),100);t=e.tooltip.attr("id");jQuery("div.ui-tooltip").not("#"+t).remove()},close:function(t,e){e.tooltip.hover(function(){jQuery(this).stop(!0).fadeTo(400,1)},function(){jQuery(this).fadeOut("500",function(){jQuery(this).remove()})})}})}function gform_strip_scripts(t){for(var e=document.createElement("div"),i=(e.innerHTML=t,e.getElementsByTagName("script")),o=0;o<i.length;o++)i[o].parentNode.removeChild(i[o]);return e.innerHTML}function gform_system_shows_scrollbars(){var t=document.createElement("div"),e=(t.setAttribute("style","width:30px;height:30px;"),t.classList.add("scrollbar-test"),document.createElement("div")),e=(e.setAttribute("style","width:100%;height:40px"),t.appendChild(e),document.body.appendChild(t),30-t.firstChild.clientWidth);return document.body.removeChild(t),!!e}jQuery(function(){gform_initialize_tooltips()});
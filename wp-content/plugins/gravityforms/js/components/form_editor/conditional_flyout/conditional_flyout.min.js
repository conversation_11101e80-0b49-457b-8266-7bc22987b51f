var GF_CONDITIONAL_INSTANCE=!1,GF_CONDITIONAL_INSTANCES_COLLECTION=[],FOCUSABLE_ELEMENTS=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])'],TAB_KEY=9,ESCAPE_KEY=27,FOCUSED_BEFORE_DIALOG=null,FOCUSED_BEFORE_RENDER=null;function setFocusToFirstItem(e,t){t&&t.target&&!gform.tools.getClosest(t.target,"#"+e.id)||(t=getFocusableChildren(e)).length&&t[0].focus()}function getFocusableChildren(e){return $$(FOCUSABLE_ELEMENTS.join(","),e).filter(function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)})}function trapTabKey(e,t){var e=getFocusableChildren(e),i=e.indexOf(document.activeElement);t.shiftKey&&0===i?(e[e.length-1].focus(),t.preventDefault()):t.shiftKey||i!==e.length-1||(e[0].focus(),t.preventDefault())}function $$(e,t){return gform.tools.convertElements((t||document).querySelectorAll(e))}function renderView(e,t,i,o){FOCUSED_BEFORE_RENDER=document.activeElement;var n,s=e;for(n in i)var l=i[n],a=new RegExp("{{ "+n+" }}","g"),s=s.replace(a,l);return o?(t.innerHTML=s,FOCUSED_BEFORE_RENDER.id&&window.setTimeout(function(){null!=document.getElementById(FOCUSED_BEFORE_RENDER.id)&&document.getElementById(FOCUSED_BEFORE_RENDER.id).focus()},10),!0):s}function getFieldById(t){var e=this.form.fields.filter(function(e){return e.id==t});return!!e.length&&e[0]}function getCorrectDefaultFieldId(e){var t;return e?("checkbox"!==e.type&&"radio"!==e.type&&"checkbox"!==e.inputType&&"radio"!==e.inputType&&e.inputs&&e.inputs.length&&(t=e.inputs.filter(function(e){return!e.isHidden})).length?t[0]:e).id:null}function getOptionsFromSelect(e,t){var i=[],o=gf_vars.emptyChoice,o={label:o=e.placeholder?e.placeholder:o,value:"",selected:""===t?'selected="selected"':""};i.push(o);for(var n=0;n<e.choices.length;n++){var s=e.choices[n],s={label:s.text,value:s.value,selected:s.value==t?'selected="selected"':""};i.push(s)}return i}function getCategoryOptions(e,t){for(var i=gf_vars.conditionalLogic.categories,o=[],n=0;n<i.length;n++){var s=i[n],s={label:s.label,value:s.term_id,selected:s.term_id==t?'selected="selected"':""};o.push(s)}return o}function getAddressOptions(e,t,i){var o=[],n=gf_vars.conditionalLogic.addressOptions;if(e.inputs){if(!n[e.addressType])return[];var s=n[e.addressType];if(!Array.isArray(s)){var l,a=!0;for(l in s)if(isNaN(l)){a=!1;break}a&&(s=Object.values(s))}if(Array.isArray(s))for(u=0;u<s.length;u++){d={label:g=s[u],value:g,selected:g==i?'selected="selected"':""};o.push(d)}else for(var r in s){var d,c=s[r];if(Array.isArray(c))for(var u=0;u<c.length;u++){var g=c[u];o.push(d={label:g,value:g,selected:g==i?'selected="selected"':""})}else o.push(d={label:c,value:r,selected:r==i?'selected="selected"':""})}}return o}function generateGFConditionalLogic(t,i){0<GF_CONDITIONAL_INSTANCES_COLLECTION.filter(function(e){return!0!==e.deactivated&&e.fieldId===t&&e.objectType===i}).length||(GF_CONDITIONAL_INSTANCE&&GF_CONDITIONAL_INSTANCE.fieldId!==t&&(GF_CONDITIONAL_INSTANCES_COLLECTION.forEach(function(e,t){e.hideFlyout(),e.removeEventListeners(),e.deactivated=!0}),GF_CONDITIONAL_INSTANCES_COLLECTION=GF_CONDITIONAL_INSTANCES_COLLECTION.filter(function(e){return!0!==e.deactivated})),GF_CONDITIONAL_INSTANCE=new GFConditionalLogic(t,i),GF_CONDITIONAL_INSTANCES_COLLECTION.push(GF_CONDITIONAL_INSTANCE))}function isValidFlyoutClick(e){var t="jsConditonalToggle"in e.target.dataset||"jsAddRule"in e.target.dataset||"jsDeleteRule"in e.target.dataset||e.target.classList.contains("gform-field__toggle-input")||null!==e.target.closest(".gform-dialog__mask");return gform.applyFilters("gform_conditional_logic_is_valid_flyout_click",t,e)}function ruleNeedsTextValue(e){return-1!==["contains","starts_with","ends_with","<",">"].indexOf(e.operator)}function GFConditionalLogic(e,t){this.fieldId=e,this.form=form,this.objectType=t,this.els=this.gatherElements(),this.state=this.getStateForField(e),this.visible=!!document.querySelector(".editor-sidebar .conditional_logic_flyout_container.anim-in-active"),this._handleToggleClick=this.handleToggleClick.bind(this),this._handleFlyoutChange=this.handleFlyoutChange.bind(this),this._handleBodyClick=this.handleBodyClick.bind(this),this._handleAccordionClick=this.handleAccordionClick.bind(this),this._handleSidebarClick=this.handleSidebarClick.bind(this),this._maintainFocus=this._maintainFocus.bind(this),this._bindKeypress=this._bindKeypress.bind(this),this.init()}GFConditionalLogic.prototype.renderSidebar=function(){var e={title:this.getAccordionTitle(),toggleText:gf_vars.configure+" "+gf_vars.conditional_logic_text,active_class:this.isEnabled()?"gform-status--active":"",active_text:this.isEnabled()?"Active":"Inactive",desc_class:GetFirstRuleField()<=0?"active":"",toggle_class:GetFirstRuleField()<=0?"":"active",desc:gf_vars.conditionalLogic.conditionalLogicHelperText};renderView(gf_vars.conditionalLogic.views.sidebar,this.els[this.objectType],e,!0)},GFConditionalLogic.prototype.renderFlyout=function(){var e={objectType:this.objectType,fieldId:this.fieldId,checked:this.state.enabled?"checked":"",activeClass:this.visible?"active":"inactive",enabledText:this.state.enabled?gf_vars.enabled:gf_vars.disabled,configure:gf_vars.configure,conditionalLogic:gf_vars.conditional_logic_text,enable:gf_vars.enable,desc:gf_vars.conditional_logic_desc,main:this.renderMainControls(!1)};renderView(gf_vars.conditionalLogic.views.flyout,this.els.flyouts[this.objectType],e,!0),gform.tools.trigger("gform_render_simplebars")},GFConditionalLogic.prototype.renderLogicDescription=function(){var e={actionType:this.state.actionType,logicType:this.state.logicType,objectTypeText:this.getObjectTypeText(),objectShowText:this.getObjectShowText(),objectHideText:this.getObjectHideText(),matchText:gf_vars.ofTheFollowingMatch,allText:gf_vars.all,anyText:gf_vars.any,hideSelected:"hide"===this.state.actionType?'selected="selected"':"",showSelected:"show"===this.state.actionType?'selected="selected"':"",allSelected:"all"===this.state.logicType?'selected="selected"':"",anySelected:"any"===this.state.logicType?'selected="selected"':""},e=renderView(gf_vars.conditionalLogic.views.logicDescription,this.els.flyouts[this.objectType],e,!1);return gform.applyFilters("gform_conditional_logic_description",e,[],this.objectType,this)},GFConditionalLogic.prototype.renderMainControls=function(e){var t={enabledClass:this.state.enabled?"active":"",logicDescription:this.renderLogicDescription(),a11yWarning:"button"===this.objectType?gf_vars.conditionalLogic.views.a11yWarning:"",a11yWarningText:gf_vars.conditional_logic_a11y},i=gf_vars.conditionalLogic.views.main;if(!e)return renderView(i,this.els.flyouts[this.objectType],t,!1);renderView(i,this.els.flyouts[this.objectType].querySelector(".conditional_logic_flyout__main"),t,!0)},GFConditionalLogic.prototype.renderFieldOptions=function(e){for(var t="",i=gf_vars.conditionalLogic.views.option,o=[],n=0;n<form.fields.length;n++){var s=form.fields[n];if(IsConditionalLogicField(s))if(s.inputs&&-1==jQuery.inArray(GetInputType(s),["checkbox","email","consent"])&&"radio"!==GetInputType(s))for(var l=0;l<s.inputs.length;l++){var a=s.inputs[l];a.isHidden||(r={label:GetLabel(s,a.id),value:a.id,selected:a.id==e.fieldId?'selected="selected"':""},o.push(r))}else{var r={label:GetLabel(s),value:s.id,selected:s.id==e.fieldId?'selected="selected"':""};o.push(r)}}for(o=gform.applyFilters("gform_conditional_logic_fields",o,form,e.fieldId),n=0;n<o.length;n++)(r=o[n]).selected||(r.selected=r.value==e.fieldId?'selected="selected"':""),t+=renderView(i,null,r,!1);return t},GFConditionalLogic.prototype.renderOperatorOptions=function(e){var t="",i=gf_vars.conditionalLogic.views.option,o={is:gf_vars.is,isnot:gf_vars.isNot,">":gf_vars.greaterThan,"<":gf_vars.lessThan,contains:gf_vars.contains,starts_with:gf_vars.startsWith,ends_with:gf_vars.endsWith};for(key in o=gform.applyFilters("gform_conditional_logic_operators",o,this.objectType,e.fieldId))t+=renderView(i,null,{label:o[key],value:key,selected:key==e.operator?'selected="selected"':""},!1);return t},GFConditionalLogic.prototype.renderValueOptions=function(e,t){var i=getFieldById(e.fieldId),o="",n=gf_vars.conditionalLogic.views.option,s=[];if((i=-1!==e.fieldId.toString().indexOf(".")?getFieldById(e.fieldId.toString().split(".")[0]):i)||IsAddressSelect(e.fieldId,i)){IsAddressSelect(e.fieldId,i)&&(s=getAddressOptions(i,e.fieldId,e.value)),i&&"post_category"==i.type&&i.displayAllCategories&&(s=getCategoryOptions(i,e.value)),i&&i.choices&&"post_category"!=i.type&&(s=getOptionsFromSelect(i,e.value));for(var l=0;l<s.length;l++)o+=renderView(n,null,s[l],!1)}return o},GFConditionalLogic.prototype.renderInput=function(e,t){t={ruleIdx:t,value:e.value};return renderView(gf_vars.conditionalLogic.views.input,null,t,!1)},GFConditionalLogic.prototype.renderSelect=function(e,t){e={ruleIdx:t,fieldValueOptions:this.renderValueOptions(e,t)};return renderView(gf_vars.conditionalLogic.views.select,null,e,!1)},GFConditionalLogic.prototype.renderRuleValue=function(e,t){var i=this.renderValueOptions(e,t).length,o="",n=ruleNeedsTextValue(e),o=!i||n?this.renderInput(e,t):this.renderSelect(e,t),i=(o=gform.applyFilters("gform_conditional_logic_values_input",o,this.objectType,t,e.fieldId,e.value),gform.tools.htmlToElement(o));return i.classList.contains("active")||i.classList.add("active"),i.hasAttribute("data-js-rule-input")||i.setAttribute("data-js-rule-input","value"),gform.tools.elementToHTML(i)},GFConditionalLogic.prototype.renderRule=function(e,t){getFieldById(e.fieldId);e={rule_idx:t,fieldOptions:this.renderFieldOptions(e),operatorOptions:this.renderOperatorOptions(e),deleteClass:1<this.state.rules.length?"active":"",value:e.value,valueMarkup:this.renderRuleValue(e,t),addRuleText:gf_vars.conditionalLogic.addRuleText,removeRuleText:gf_vars.conditionalLogic.removeRuleText};return renderView(gf_vars.conditionalLogic.views.rule,null,e,!1)},GFConditionalLogic.prototype.renderRules=function(){for(var e=this.els.flyouts[this.objectType].querySelector(".conditional_logic_flyout__logic"),t="",i=0;i<this.state.rules.length;i++)t+=this.renderRule(this.state.rules[i],i);renderView(t,e,{},!0)},GFConditionalLogic.prototype.updateCompactView=function(){var e;"next_button"!=this.objectType&&(e=document.querySelector("#gfield_"+this.fieldId+"-conditional-logic-icon"))&&(this.state.enabled?e.style.display="block":e.style.display="none")},GFConditionalLogic.prototype.gatherElements=function(){return{field:document.querySelector(".conditional_logic_field_setting"),page:document.querySelector(".conditional_logic_page_setting"),next_button:document.querySelector(".conditional_logic_nextbutton_setting"),button:document.querySelector(".conditional_logic_submit_setting"),flyouts:{page:document.getElementById("conditional_logic_flyout_container"),field:document.getElementById("conditional_logic_flyout_container"),next_button:document.getElementById("conditional_logic_next_button_flyout_container"),button:document.getElementById("conditional_logic_submit_flyout_container")}}},GFConditionalLogic.prototype.getDefaultRule=function(){var e=GetFirstRuleField();return{fieldId:getCorrectDefaultFieldId(GetFieldById(e)),operator:"is",value:""}},GFConditionalLogic.prototype.getDefaultState=function(){return{enabled:!1,actionType:"show",logicType:"all",rules:[this.getDefaultRule()]}},GFConditionalLogic.prototype.getStateForField=function(e){var t;return"submit"===e?(t=form.button.conditionalLogic)?(t.enabled=!0,t):this.getDefaultState():!1!==(e=getFieldById(e))&&(t=("next_button"===this.objectType?e.nextButton:e).conditionalLogic)&&t.actionType?("enabled"in t||(t.enabled=!0),t):this.getDefaultState()},GFConditionalLogic.prototype.isEnabled=function(){return this.state.enabled&&0<GetFirstRuleField()},GFConditionalLogic.prototype.getAccordionTitle=function(){var e="";switch(this.objectType){case"page":e=gf_vars.page+" ";break;case"next_button":e=gf_vars.next_button+" ";break;case"button":e=gf_vars.button+" "}return e+gf_vars.conditional_logic_text},GFConditionalLogic.prototype.getObjectTypeText=function(){switch(this.objectType){case"section":return gf_vars.thisSectionIf;case"field":return gf_vars.thisFieldIf;case"page":return gf_vars.thisPage;case"confirmation":return gf_vars.thisConfirmation;case"notification":return gf_vars.thisNotification;default:return gf_vars.thisFormButton}},GFConditionalLogic.prototype.getObjectShowText=function(){return"next_button"===this.objectType?gf_vars.enable:gf_vars.show},GFConditionalLogic.prototype.getObjectHideText=function(){return"next_button"===this.objectType?gf_vars.disable:gf_vars.hide},GFConditionalLogic.prototype.hideFlyout=function(){var e=this.els.flyouts[this.objectType];e.classList.contains("anim-in-active")&&(e.classList.remove("anim-in-ready"),e.classList.remove("anim-in-active"),e.classList.add("anim-out-ready"),window.setTimeout(function(){e.classList.add("anim-out-active")},25),window.setTimeout(function(){e.classList.remove("anim-out-ready"),e.classList.remove("anim-out-active")},215))},GFConditionalLogic.prototype.showFlyout=function(){for(type in this.els.flyouts){var e=this.els.flyouts[type];e.classList.remove("anim-in-ready"),e.classList.remove("anim-in-active"),e.classList.remove("anim-out-ready"),e.classList.remove("anim-out-active")}var t=this.els.flyouts[this.objectType];t.classList.add("anim-in-ready"),window.setTimeout(function(){t.classList.add("anim-in-active")},25)},GFConditionalLogic.prototype.toggleFlyout=function(e){this.renderFlyout(),this.renderRules(),this.visible?this.hideFlyout():this.showFlyout(),this.visible=!this.visible;var t=this;e&&window.setTimeout(function(){t.handleFocus()},325)},GFConditionalLogic.prototype.updateState=function(e,t){this.state[e]=t,this.updateForm(),"enabled"===e&&(this.renderSidebar(),this.renderMainControls(!0),this.renderRules(),this.updateCompactView())},GFConditionalLogic.prototype.updateRule=function(e,t,i){this.state.rules[i][e]=t,this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.addRule=function(){this.state.rules.push(this.getDefaultRule()),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.deleteRule=function(e){this.state.rules.splice(e,1),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.updateFormConditionalData=function(e,t){"next_button"===this.objectType?form.fields[e].nextButton.conditionalLogic=t:"button"===this.objectType?form.button.conditionalLogic=t:form.fields[e].conditionalLogic=t},GFConditionalLogic.prototype.updateForm=function(){"submit"===this.fieldId&&this.updateFormButtonConditionalData(this.state);for(var e=0;e<form.fields.length;e++){var t=form.fields[e];if(t.id==this.fieldId)return this.isEnabled()?void this.updateFormConditionalData(e,this.state):void this.updateFormConditionalData(e,"")}},GFConditionalLogic.prototype.updateFormButtonConditionalData=function(e){this.isEnabled()?form.button.conditionalLogic=e:form.button.conditionalLogic=""},GFConditionalLogic.prototype.handleToggleClick=function(e){(e.target.classList.contains("conditional_logic_accordion__toggle_button")||e.target.classList.contains("conditional_logic_accordion__toggle_button_icon"))&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleSidebarClick=function(e){var t;"jsConditonalToggle"in e.target.dataset&&this.updateState("enabled",e.target.checked),"jsAddRule"in e.target.dataset&&this.addRule(),"jsDeleteRule"in e.target.dataset&&(t=gform.tools.getClosest(e.target,"[data-js-rule-idx]"),this.deleteRule(t.dataset.jsRuleIdx)),"jsCloseFlyout"in e.target.dataset&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleFlyoutChange=function(e){var t,i,o;"jsStateUpdate"in e.target.dataset&&(i=e.target.dataset.jsStateUpdate,o=e.target.value,this.updateState(i,o)),"jsRuleInput"in e.target.dataset&&(t=e.target.parentNode,i=e.target.dataset.jsRuleInput,o=e.target.value,this.updateRule(i,o,t.dataset.jsRuleIdx))},GFConditionalLogic.prototype.handleBodyClick=function(e){isValidFlyoutClick(e)||this.visible&&!this.els.flyouts[this.objectType].contains(e.target)&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleAccordionClick=function(e){!this.visible||e.target.classList.contains("conditional_logic_accordion__toggle_button")||e.target.classList.contains("conditional_logic_accordion__toggle_button_icon")||this.toggleFlyout(!1)},GFConditionalLogic.prototype.addEventListeners=function(){this.els[this.objectType].addEventListener("click",this._handleToggleClick),this.els.flyouts[this.objectType].addEventListener("click",this._handleSidebarClick),this.els.flyouts[this.objectType].addEventListener("change",this._handleFlyoutChange),document.body.addEventListener("click",this._handleBodyClick),gform.addAction("formEditorNullClick",this._handleAccordionClick)},GFConditionalLogic.prototype.removeEventListeners=function(){this.els[this.objectType].removeEventListener("click",this._handleToggleClick),this.els.flyouts[this.objectType].removeEventListener("click",this._handleSidebarClick),this.els.flyouts[this.objectType].removeEventListener("change",this._handleFlyoutChange),document.body.removeEventListener("click",this._handleBodyClick)},GFConditionalLogic.prototype._bindKeypress=function(e){this.visible&&e.which===ESCAPE_KEY&&(e.preventDefault(),this.toggleFlyout(!0)),this.visible&&e.which===TAB_KEY&&trapTabKey(this.els.flyouts[this.objectType],e)},GFConditionalLogic.prototype.addFocusToFlyout=function(){FOCUSED_BEFORE_DIALOG=document.activeElement,setFocusToFirstItem(this.els.flyouts[this.objectType]),document.body.addEventListener("focus",this._maintainFocus,!0),document.addEventListener("keydown",this._bindKeypress)},GFConditionalLogic.prototype.removeFocusFromFlyout=function(){FOCUSED_BEFORE_DIALOG&&FOCUSED_BEFORE_DIALOG.focus(),document.body.removeEventListener("focus",this._maintainFocus,!0),document.removeEventListener("keydown",this._bindKeypress)},GFConditionalLogic.prototype.handleFocus=function(){this.visible?this.addFocusToFlyout():this.removeFocusFromFlyout()},GFConditionalLogic.prototype._maintainFocus=function(e){this.visible&&!this.els.flyouts[this.objectType].contains(e.target)&&setFocusToFirstItem(this.els.flyouts[this.objectType],e)},GFConditionalLogic.prototype.render=function(){this.renderSidebar(),this.renderFlyout(),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.init=function(){this.addEventListeners(),this.renderSidebar()};
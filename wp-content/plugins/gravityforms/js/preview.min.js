jQuery(document).ready(function(){function o(e){for(var i=document.cookie.split("; "),o=0;o<i.length;o++){var r=i[o].split("=");if(e==r[0])return unescape(r[1])}return null}jQuery(".toggle_helpers input[type=checkbox]").prop("checked",!1),jQuery("#showgrid").on("click",function(){jQuery(this).is(":checked")?jQuery("#preview_form_container").addClass("showgrid"):jQuery("#preview_form_container").removeClass("showgrid")}),jQuery("#showme").on("click",function(){jQuery(this).is(":checked")?(jQuery(".gform_wrapper form").addClass("gf_showme"),jQuery("#helper_legend_container").css("display","inline-block")):(jQuery(".gform_wrapper form").removeClass("gf_showme"),jQuery("#helper_legend_container").css("display","none"))}),o("dismissed-notifications")&&jQuery(o("dismissed-notifications")).hide(),jQuery(".hidenotice").on("click",function(){var e=jQuery(this).closest(".preview_notice").attr("id"),e=o("dismissed-notifications")+",#"+e,i=(jQuery(this).closest(".preview_notice").slideToggle("slow"),"dismissed-notifications"),e=e.replace("null,","");document.cookie=i+"="+escape(e),(i=new Date).setMonth(i.getMonth()+1),document.cookie+="; expires="+i.toUTCString()}),jQuery("#browser_size_info").text("Viewport ( Width : "+jQuery(window).width()+"px , Height :"+jQuery(window).height()+"px )"),jQuery(window).on("resize",function(){jQuery("#browser_size_info").text("Viewport ( Width : "+jQuery(window).width()+"px , Height :"+jQuery(window).height()+"px )")})});
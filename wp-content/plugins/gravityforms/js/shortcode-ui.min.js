var GformShortcodeUI;(u=>{var n=window.gform_admin_config.i18n,r=window.GformShortcodeUI={models:{},collections:{},views:{},utils:{},strings:{}};r.models.ShortcodeAttribute=Backbone.Model.extend({defaults:{attr:"",label:"",type:"",section:"",description:"",default:"",value:""}}),r.models.ShortcodeAttributes=Backbone.Collection.extend({model:r.models.ShortcodeAttribute,clone:function(){return new this.constructor(_.map(this.models,function(e){return e.clone()}))}}),r.models.Shortcode=Backbone.Model.extend({defaults:{label:"",shortcode_tag:"",action_tag:"",attrs:r.models.ShortcodeAttributes},set:function(e,t){return void 0===e.attrs||e.attrs instanceof r.models.ShortcodeAttributes||(_.each(e.attrs,function(e){null!=e.default&&(e.value=e.default)}),e.attrs=new r.models.ShortcodeAttributes(e.attrs)),Backbone.Model.prototype.set.call(this,e,t)},toJSON:function(e){return void 0!==(e=Backbone.Model.prototype.toJSON.call(this,e)).attrs&&e.attrs instanceof r.models.ShortcodeAttributes&&(e.attrs=e.attrs.toJSON()),e},clone:function(){var e=Backbone.Model.prototype.clone.call(this);return e.set("attrs",e.get("attrs").clone()),e},formatShortcode:function(){var e,r,i=[];return this.get("attrs").each(function(e){var t=e.get("value"),o=e.get("type"),n=e.get("default");(!t||t.length<1)&&"checkbox"!=o||"checkbox"==o&&"true"!=n&&!t||("content"===e.get("attr")?r=e.get("value"):i.push(e.get("attr")+'="'+t+'"'))}),e="[{{ shortcode }} {{ attributes }}]",r&&0<r.length&&(e+="{{ content }}[/{{ shortcode }}]"),e=(e=(e=e.replace(/{{ shortcode }}/g,this.get("shortcode_tag"))).replace(/{{ attributes }}/g,i.join(" "))).replace(/{{ content }}/g,r)},validate:function(e){var t=[];return e.attrs.findWhere({attr:"id"}).get("value")||t.push({id:r.strings.pleaseSelectAForm}),t.length?t:null}}),r.collections.Shortcodes=Backbone.Collection.extend({model:r.models.Shortcode}),r.views.editShortcodeForm=wp.Backbone.View.extend({el:"#gform-shortcode-ui-container",template:wp.template("gf-shortcode-default-edit-form"),hasAdvancedValue:!1,events:{"click #gform-update-shortcode":"insertShortcode","click #gform-insert-shortcode":"insertShortcode","click #gform-cancel-shortcode":"cancelShortcode"},initialize:function(){_.bindAll(this,"beforeRender","render","afterRender");var t=this;this.render=_.wrap(this.render,function(e){return t.beforeRender(),e(),t.afterRender(),t}),this.model.get("attrs").each(function(e){switch(e.get("section")){case"required":t.views.add(".gf-edit-shortcode-form-required-attrs",new r.views.editAttributeField({model:e,parent:t}));break;case"standard":t.views.add(".gf-edit-shortcode-form-standard-attrs",new r.views.editAttributeField({model:e,parent:t}));break;default:t.views.add(".gf-edit-shortcode-form-advanced-attrs",new r.views.editAttributeField({model:e,parent:t})),t.hasAdvancedVal||(t.hasAdvancedVal=""!==e.get("value"))}}),this.listenTo(this.model,"change",this.render)},beforeRender:function(){},afterRender:function(){gform_initialize_tooltips(),u("#gform-insert-shortcode").toggle("insert"==this.options.viewMode),u("#gform-update-shortcode").toggle("insert"!=this.options.viewMode),u("#gf-edit-shortcode-form-advanced-attrs").toggle(this.hasAdvancedVal)},insertShortcode:function(e){this.model.isValid({validate:!0})?(send_to_editor(this.model.formatShortcode()),tb_remove(),this.dispose()):_.each(this.model.validationError,function(e){_.each(e,function(e,t){alert(e)})})},cancelShortcode:function(e){tb_remove(),this.dispose()},dispose:function(){this.remove(),u("#gform-shortcode-ui-wrap").append('<div id="gform-shortcode-ui-container"></div>')}}),r.views.editAttributeField=Backbone.View.extend({tagName:"div",initialize:function(e){this.parent=e.parent},events:{'keyup  input[type="text"]':"updateValue","keyup  textarea":"updateValue","change select":"updateValue","change #gf-shortcode-attr-action":"updateAction","change input[type=checkbox]":"updateCheckbox","change input[type=radio]":"updateValue","change input[type=email]":"updateValue","change input[type=number]":"updateValue","change input[type=date]":"updateValue","change input[type=url]":"updateValue"},render:function(){return this.template=wp.media.template("gf-shortcode-ui-field-"+this.model.get("type")),this.$el.html(this.template(this.model.toJSON()))},updateValue:function(e){e=u(e.target);this.model.set("value",e.val())},updateCheckbox:function(e){e=u(e.target).prop("checked");this.model.set("value",e)},updateAction:function(e){var e=u(e.target).val(),t=(this.model.set("value",e),this.parent.model),e=r.shortcodes.findWhere({shortcode_tag:"gravityform",action_tag:e}),n=t.get("attrs"),t=(e.get("attrs").each(function(e){var t=e.get("attr"),o=n.findWhere({attr:t});void 0!==o&&t==o.get("attr")&&(t=o.get("value"),e.set("value",String(t)))}),u(this.parent.el).empty(),this.parent.options.viewMode);this.parent.dispose(),this.parent.model.set(e),(GformShortcodeUI=new r.views.editShortcodeForm({model:e,viewMode:t})).render()}}),r.utils.shortcodeViewConstructor={initialize:function(e){this.shortcodeModel=this.getShortcodeModel(this.shortcode)},getShortcodeModel:function(t){var e=void 0!==t.attrs.named.action?t.attrs.named.action:"",e=r.shortcodes.findWhere({action_tag:e});if(e)return(e=e.clone()).get("attrs").each(function(e){e.get("attr")in t.attrs.named&&e.set("value",t.attrs.named[e.get("attr")]),"content"===e.get("attr")&&"content"in t&&e.set("value",t.content)}),e},getContent:function(){return this.content||this.fetch(),this.content},fetch:function(){var e,t=this;this.fetching||(this.fetching=!0,e=this.shortcodeModel.get("attrs").findWhere({attr:"id"}).get("value"),e={action:"gf_do_shortcode",post_id:u("#post_ID").val(),form_id:e,shortcode:this.shortcodeModel.formatShortcode(),nonce:gfShortcodeUIData.previewNonce},u.post(ajaxurl,e).done(function(e){t.content=e}).fail(function(){t.content='<span class="gf_shortcode_ui_error">'+gfShortcodeUIData.strings.errorLoadingPreview+"</span>"}).always(function(){delete t.fetching,t.render()}))},setLoader:function(){this.setContent('<div class="loading-placeholder"><div class="dashicons dashicons-feedback"></div><div class="wpview-loading"><ins></ins></div></div>')},View:{overlay:!0,shortcodeHTML:!1,setContent:function(r,i){this.getNodes(function(e,t,o){var o="wrap"===i||"replace"===i?t:o,n=r;_.isString(n)&&(n=e.dom.createFragment(n)),"replace"===i?e.dom.replace(n,o):"remove"===i?(t.parentNode.insertBefore(n,t.nextSibling),u(t).remove()):(o.innerHTML="",o.appendChild(n))})},initialize:function(t){var e=void 0!==t.shortcode.attrs.named.action?t.shortcode.attrs.named.action:"",e=r.shortcodes.findWhere({action_tag:e});e?((e=e.clone()).get("attrs").each(function(e){e.get("attr")in t.shortcode.attrs.named&&e.set("value",t.shortcode.attrs.named[e.get("attr")]),"content"===e.get("attr")&&"content"in t.shortcode&&e.set("value",t.shortcode.content)}),this.shortcode=e):(this.shortcodeHTML=decodeURIComponent(t.encodedText),this.shortcode=!1)},loadingPlaceholder:function(){return'<div class="loading-placeholder"><div class="dashicons dashicons-feedback"></div><div class="wpview-loading"><ins></ins></div></div>'},getEditors:function(t){var o=[];return _.each(tinymce.editors,function(e){e.plugins.wpview&&(t&&t(e),o.push(e))},this),o},getNodes:function(n){var r=[],e=this;return this.getEditors(function(o){u(o.getBody()).find('[data-wpview-text="'+e.encodedText+'"]').each(function(e,t){n&&n(o,t,u(t).find(".wpview-content").get(0)),r.push(t)})}),r},setIframes:function(h){var l=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;-1===h.indexOf("<script")?(this.shortcodeHTML=h,this.render()):this.getNodes(function(e,t,o){var n,r,i,d,a=e.dom,s="",c=e.getBody().className||"";o.innerHTML="";wp.mce.views.sandboxStyles?s=wp.mce.views.sandboxStyles:(tinymce.each(a.$('link[rel="stylesheet"]',e.getDoc().head),function(e){e.href&&-1===e.href.indexOf("skins/lightgray/content.min.css")&&-1===e.href.indexOf("skins/wordpress/wp-content.css")&&(s+=a.getOuterHTML(e)+"\n")}),wp.mce.views.sandboxStyles=s),setTimeout(function(){if(n=a.add(o,"iframe",{src:tinymce.Env.ie?'javascript:""':"",frameBorder:"0",id:"gf-shortcode-preview-"+(new Date).getTime(),allowTransparency:"true",scrolling:"no",class:"wpview-sandbox",style:{width:"100%",display:"block"}}),(r=n.contentWindow.document).open(),r.write('<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />'+s+'<style>html {background: transparent;padding: 0;margin: 0;}body#wpview-iframe-sandbox {background: transparent;padding: 1px 0 !important;margin: -1px 0 0 !important;}body#wpview-iframe-sandbox:before,body#wpview-iframe-sandbox:after {display: none;content: "";}</style></head><body id="wpview-iframe-sandbox" class="'+c+'">'+h+"</body></html>"),r.close(),d=function(){n.contentWindow&&u(n).height(u(r.body).height())},l)new l(_.debounce(function(){d()},100)).observe(r.body,{attributes:!0,childList:!0,subtree:!0});else for(i=1;i<6;i++)setTimeout(d,700*i);d(),e.on("wp-body-class-change",function(){r.body.className=e.getBody().className})},50)})},getHtml:function(){var e;if(this.shortcode)return!1===this.shortcodeHTML&&(e=this.shortcode.get("attrs").findWhere({attr:"id"}).get("value"),e={action:"gf_do_shortcode",post_id:u("#post_ID").val(),form_id:e,shortcode:this.shortcode.formatShortcode(),nonce:gfShortcodeUIData.previewNonce},u.post(ajaxurl,e,u.proxy(this.setIframes,this))),this.shortcodeHTML;this.setContent(this.shortcodeHTML,"remove")}},edit:function(e){var o,t;"object"==typeof e&&(e=decodeURIComponent(jQuery(e).attr("data-wpview-text"))),(e=wp.shortcode.next("gravityform",e))&&(t=e.shortcode.attrs.named.action||"",t=r.shortcodes.findWhere({shortcode_tag:e.shortcode.tag,action_tag:t}))&&(o=t.clone(),_.each(e.shortcode.attrs.named,function(e,t){(attr=o.get("attrs").findWhere({attr:t}))&&attr.set("value",e)}),t=o.get("attrs").findWhere({attr:"id"}).get("value"),u("#add_form_id").val(t),(GformShortcodeUI=new r.views.editShortcodeForm({model:o,viewMode:"update"})).render(),u("#gform-insert-shortcode").hide(),u("#gform-update-shortcode").show(),tb_show(n.shortcode_ui.edit_form,"#TB_inline?inlineId=select_gravity_form&width=753&height=686",""))}},u(document).ready(function(){r.strings=gfShortcodeUIData.strings,r.shortcodes=new r.collections.Shortcodes(gfShortcodeUIData.shortcodes),gfShortcodeUIData.previewDisabled||void 0===wp.mce||wp.mce.views.register("gravityform",u.extend(!0,{},r.utils.shortcodeViewConstructor)),u(document).on("click",".gform_media_link",function(){r.shortcodes=new r.collections.Shortcodes(gfShortcodeUIData.shortcodes);var e=r.shortcodes.findWhere({shortcode_tag:"gravityform",action_tag:""});(GformShortcodeUI=new r.views.editShortcodeForm({model:e,viewMode:"insert"})).render(),tb_show(n.shortcode_ui.insert_form,"#TB_inline?inlineId=select_gravity_form&width=753&height=686","")})})})((window.gfShortcodeUI=window.gfShortcodeUI||{},jQuery));
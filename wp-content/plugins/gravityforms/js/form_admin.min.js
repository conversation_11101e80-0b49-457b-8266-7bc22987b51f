function initMergeTagSupport(){"undefined"!=typeof form&&0<=jQuery(".merge-tag-support").length&&jQuery(".merge-tag-support").each(function(){new gfMergeTagsObj(form,jQuery(this))})}function FormatCurrency(e){var t;gf_vars.gf_currency_config&&(t=new gform.Currency(gf_vars.gf_currency_config).toMoney(jQuery(e).val()),jQuery(e).val(t))}function ToggleConditionalLogic(e,t){jQuery("#"+t+"_conditional_logic").is(":checked")?(CreateConditionalLogic(t,GetConditionalObject(t)),SetConditionalProperty(t,"actionType",jQuery("#"+t+"_action_type").val()),SetConditionalProperty(t,"logicType",jQuery("#"+t+"_logic_type").val()),SetRule(t,0),jQ<PERSON>y("#"+t+"_conditional_logic_container").show()):jQuery("#"+t+"_conditional_logic_container").hide()}function GetConditionalObject(e){var t=!1;switch(e){case"page":case"field":t=GetSelectedField();break;case"next_button":var i=GetSelectedField();(t=i.nextButton).id=i.id;break;case"confirmation":t=confirmation;break;case"notification":t=current_notification;break;case"button":t=form.button;break;default:t="undefined"!=typeof form&&form.button}return t=gform.applyFilters("gform_conditional_object",t,e)}function CreateConditionalLogic(t,e){e.conditionalLogic&&0!=e.conditionalLogic.length||(e.conditionalLogic=new ConditionalLogic);var i,o="hide"==e.conditionalLogic.actionType?"selected='selected'":"",n="show"==e.conditionalLogic.actionType?"selected='selected'":"",r="all"==e.conditionalLogic.logicType?"selected='selected'":"",a="any"==e.conditionalLogic.logicType?"selected='selected'":"",s="section"==e.type?gf_vars.thisSectionIf:"field"==t?gf_vars.thisFieldIf:"page"==t?gf_vars.thisPage:"confirmation"==t?gf_vars.thisConfirmation:"notification"==t?gf_vars.thisNotification:gf_vars.thisFormButton,l="next_button"==t?(i=gf_vars.enable,gf_vars.disable):(i=gf_vars.show,gf_vars.hide),c={};c.actionType="<select id='"+t+"_action_type' onchange='SetConditionalProperty(\""+t+"\", \"actionType\", jQuery(this).val());'><option value='show' "+n+">"+i+"</option><option value='hide' "+o+">"+l+"</option></select>",c.objectDescription=s,c.logicType="<select id='"+t+"_logic_type' onchange='SetConditionalProperty(\""+t+"\", \"logicType\", jQuery(this).val());'><option value='all' "+r+">"+gf_vars.all+"</option><option value='any' "+a+">"+gf_vars.any+"</option></select>",c.ofTheFollowingMatch=gf_vars.ofTheFollowingMatch;for(var u,d=makeArray(c).join(" "),d=gform.applyFilters("gform_conditional_logic_description",d,c,t,e),g=0;g<e.conditionalLogic.rules.length;g++)u=e.conditionalLogic.rules[g],d=(d=(d=(d=(d+="<div width='100%' class='gf_conditional_logic_rules_container'>")+GetRuleFields(t,g,e.conditionalLogic.rules[g].fieldId))+GetRuleOperators(t,g,e.conditionalLogic.rules[g].fieldId,u.operator))+GetRuleValues(t,g,e.conditionalLogic.rules[g].fieldId,u.value))+"<button type='button' class='add_field_choice gform-st-icon gform-st-icon--circle-plus' title='add another rule' onclick=\"InsertRule('"+t+"', "+(g+1)+');" onkeypress="InsertRule(\''+t+"', "+(g+1)+');"></button>',1<e.conditionalLogic.rules.length&&(d+="<button type='button' class='delete_field_choice gform-st-icon gform-st-icon--circle-minus' title='remove this rule' onclick=\"DeleteRule('"+t+"', "+g+');" onkeypress="DeleteRule(\''+t+"', "+g+');"></button></li>'),d+="</div>";jQuery("#"+t+"_conditional_logic_container").html(d),Placeholders.enable(),jQuery("#"+t+"_conditional_logic",document).parents("form").on("submit",function(e){jQuery("#"+t+"_conditional_logic_object").val(JSON.stringify(GetConditionalObject(t).conditionalLogic))})}function GetRuleOperators(e,t,i,o){var n={is:"is",isnot:"isNot",">":"greaterThan","<":"lessThan",contains:"contains",starts_with:"startsWith",ends_with:"endsWith"},r="<select id='"+e+"_rule_operator_"+t+"' class='gfield_rule_select' onchange='SetRuleProperty(\""+e+'", '+t+', "operator", jQuery(this).val());var valueSelector="#'+e+"_rule_value_"+t+'"; jQuery(valueSelector).replaceWith(GetRuleValues("'+e+'", '+t+',"'+i+'", ""));jQuery(valueSelector).change();\'>',t=IsEntryMeta(i)?GetOperatorsForMeta(n,i):n;return t=gform.applyFilters("gform_conditional_logic_operators",t,e,i),jQuery.each(t,function(e,t){var i=gf_vars[t];r+="<option value='"+e+"' "+(o==e?"selected='selected'":"")+">"+(i=void 0===i?t:i)+"</option>"}),r+="</select>"}function GetOperatorsForMeta(e,i){var o={};return entry_meta[i]&&entry_meta[i].filter&&entry_meta[i].filter.operators?jQuery.each(e,function(e,t){0<=jQuery.inArray(e,entry_meta[i].filter.operators)&&(o[e]=t)}):o=e,o}function GetRuleFields(e,t,i){for(var e="<select id='"+e+"_rule_field_"+t+"' class='gfield_rule_select' onchange='jQuery(\"#"+e+"_rule_operator_"+t+'").replaceWith(GetRuleOperators("'+e+'", '+t+', jQuery(this).val()));jQuery("#'+e+"_rule_value_"+t+'").replaceWith(GetRuleValues("'+e+'", '+t+', jQuery(this).val())); SetRule("'+e+'", '+t+"); '>",o=[],n=0;n<form.fields.length;n++){var r=form.fields[n];if(IsConditionalLogicField(r))if(r.inputs&&-1==jQuery.inArray(GetInputType(r),["checkbox","email","consent"])&&"radio"!==GetInputType(r))for(var a=0;a<r.inputs.length;a++){var s=r.inputs[a];s.isHidden||o.push({label:GetLabel(r,s.id),value:s.id})}else o.push({label:GetLabel(r),value:r.id})}return jQuery.merge(o,GetEntryMetaFields(i)),e+GetRuleFieldsOptions(o=gform.applyFilters("gform_conditional_logic_fields",o,form,i),i)+"</select>"}function GetRuleFieldsOptions(e,t){for(var i="",o=0;o<e.length;o++){var n,r=e[o];void 0!==r.options?i=(i+='<optgroup label=" '+r.label+'">')+GetRuleFieldsOptions(r.options,t)+"</optgroup>":(n=r.value==t?"selected='selected'":"",i+="<option value='"+r.value+"' "+n+">"+r.label+"</option>")}return i}function GetEntryMetaFields(i){var o=[];return"undefined"!=typeof entry_meta&&jQuery.each(entry_meta,function(e,t){void 0!==t.filter&&o.push({label:t.label,value:e,isSelected:i==e?"selected='selected'":""})}),o}function IsConditionalLogicField(e){var t=e.inputType||e.type,i=GetConditionalLogicFields(),t=jQuery.inArray(t,i);return gform.applyFilters("gform_is_conditional_logic_field",0<=t,e)}function IsEntryMeta(e){return"undefined"!=typeof entry_meta&&void 0!==entry_meta[e]}function GetRuleValues(t,i,e,o,n){var r,a,s,l,c,u,d,g=0==(n=n||!1)?t+"_rule_value_"+i:n;return 0==(e=0==e?GetFirstRuleField():e)?"":(r=GetFieldById(e),a=IsEntryMeta(e),s=GetConditionalObject(t).conditionalLogic.rules[i].operator,u="",u=r&&"post_category"==r.type&&r.displayAllCategories?0<(l=jQuery("#"+g+".gfield_category_dropdown")).length?(l=(l=(l=l.html()).replace(/ selected="selected"/g,"")).replace('value="'+o+'"','value="'+o+'" selected="selected"'),"<select id='"+g+"' class='gfield_rule_select gfield_rule_value_dropdown gfield_category_dropdown'>"+l+"</select>"):(c=0==n?"gfield_ajax_placeholder_"+i:n+"_placeholder",jQuery.post(ajaxurl,{action:"gf_get_post_categories",objectType:t,ruleIndex:i,inputName:n,selectedValue:o},function(e){e&&(jQuery("#"+c).replaceWith(e.trim()),SetRuleProperty(t,i,"value",jQuery("#"+g).val()))}),"<select id='"+c+"' class='gfield_rule_select'><option>"+gf_vars.loading+"</option></select>"):r&&r.choices&&-1<jQuery.inArray(s,["is","isnot"])?("multiselect"===GetInputType(r)?d=gf_vars.emptyChoice:r.placeholder&&(d=r.placeholder),GetRuleValuesDropDown(d?[{text:d,value:""}].concat(r.choices):r.choices,t,i,o,n)):IsAddressSelect(e,r)?(jQuery.post(ajaxurl,{action:"gf_get_address_rule_values_select",address_type:r.addressType||gf_vars.defaultAddressType,value:o,id:g,form_id:r.formId},function(e){e&&($select=jQuery(e.trim()),($placeholder=jQuery("#"+g)).replaceWith($select),SetRuleProperty(t,i,"value",$select.val()))}),"<select id='"+g+"' class='gfield_rule_select'><option>"+gf_vars.loading+"</option></select>"):a&&entry_meta&&entry_meta[e]&&entry_meta[e].filter&&void 0!==entry_meta[e].filter.choices?GetRuleValuesDropDown(entry_meta[e].filter.choices,t,i,o,n):(o=o?o.replace(/'/g,"&#039;"):"","<input type='text' placeholder='"+gf_vars.enterValue+"' class='gfield_rule_select gfield_rule_input' id='"+g+"' name='"+g+"' value='"+o.replace(/'/g,"&#039;")+"' onchange='SetRuleProperty(\""+t+'", '+i+', "value", jQuery(this).val());\' onkeyup=\'SetRuleProperty("'+t+'", '+i+', "value", jQuery(this).val());\'>'),gform.applyFilters("gform_conditional_logic_values_input",u,t,i,e,o))}function IsAddressSelect(e,t){var i,o;return!(!t||"address"!=GetInputType(t)||(i=t.addressType||gf_vars.defaultAddressType,!gf_vars.addressTypes[i]))&&(o=gf_vars.addressTypes[i],e==t.id+".6"&&"international"==i||e==t.id+".4"&&"object"==typeof o.states)}function GetFirstRuleField(){for(var e=0;e<form.fields.length;e++)if(IsConditionalLogicField(form.fields[e]))return form.fields[e].id;return 0}function GetRuleValuesDropDown(e,t,i,o,n){for(var t=0==n?t+"_rule_value_"+i:n,r="<select class='gfield_rule_select gfield_rule_value_dropdown' id='"+t+"' name='"+t+"'>",a=!1,s=0;s<e.length;s++){var l=void 0===e[s].value||null==e[s].value?e[s].text+"":e[s].value+"",c=l==o,u=c?"selected='selected'":"",c=(c&&(a=!0),l=l.replace(/'/g,"&#039;"),""===jQuery.trim(jQuery("<div>"+e[s].text+"</div>").text())?l:e[s].text);r+="<option value='"+l.replace(/'/g,"&#039;")+"' "+u+">"+c+"</option>"}return!a&&o&&""!=o&&(r+="<option value='"+o.replace(/'/g,"&#039;")+"' selected='selected'>"+o+"</option>"),r+="</select>"}function SetRuleProperty(e,t,i,o){e=GetConditionalObject(e);e.conditionalLogic.rules&&(e.conditionalLogic.rules[t][i]=o)}function GetFieldById(e){if("submit"===e)return GetSubmitField();if("last_page_settings"===e)return{type:"page"};e=parseInt(e);for(var t=0;t<form.fields.length;t++)if(form.fields[t].id==e)return form.fields[t];return null}function GetSubmitField(){return{type:"submit",cssClass:""}}function SetConditionalProperty(e,t,i){GetConditionalObject(e).conditionalLogic[t]=i}function SetRuleValueDropDown(e){var t=e.attr("id").split("_rule_value_");t.length<2||SetRuleProperty(t[0],t[1],"value",e.val())}function InsertRule(e,t){var i=GetConditionalObject(e);i.conditionalLogic.rules.splice(t,0,new ConditionalRule),CreateConditionalLogic(e,i),SetRule(e,t)}function SetRule(e,t){SetRuleProperty(e,t,"fieldId",jQuery("#"+e+"_rule_field_"+t).val()),SetRuleProperty(e,t,"operator",jQuery("#"+e+"_rule_operator_"+t).val()),SetRuleProperty(e,t,"value",jQuery("#"+e+"_rule_value_"+t).val())}function DeleteRule(e,t){var i=GetConditionalObject(e);i.conditionalLogic.rules.splice(t,1),CreateConditionalLogic(e,i)}function TruncateRuleText(e){return!e||e.length<=18?e:e.substr(0,9)+"..."+e.substr(e.length-8,9)}function gfAjaxSpinner(e,t,i){return t=void 0!==t&&t?t:gf_vars.baseUrl+"/images/spinner.svg",i=void 0!==i?i:"",this.elem=e,this.image='<img class="gfspinner" src="'+t+'" style="'+i+'" />',this.init=function(){return this.spinner=jQuery(this.image),jQuery(this.elem).after(this.spinner),this},this.destroy=function(){jQuery(this.spinner).remove()},this.init()}function InsertVariable(e,t,i){i=i||jQuery("#"+e+"_variable_select").val();var o=document.getElementById(e),n=jQuery(o),r=(document.selection?(n[0].focus(),document.selection.createRange().text=i):"selectionStart"in o?(r=o.selectionStart,o.value=o.value.substr(0,r)+i+o.value.substr(o.selectionEnd,o.value.length),o.selectionStart=r+o.value.length,o.selectionEnd=r+o.value.length):n.val(i+messageElement.val()),jQuery("#"+e+"_variable_select"));0<r.length&&(r[0].selectedIndex=0),t&&window[t]&&window[t].call(null,e,i)}function InsertEditorVariable(e,t){var i;t||((i=jQuery("#"+e+"_variable_select"))[0].selectedIndex=0,t=i.val()),wpActiveEditor=e,window.send_to_editor(t)}function GetInputType(e){return e.inputType||e.type}function FieldIsChoiceType(e){return void 0===e&&(e=GetSelectedField()),["multi_choice","image_choice"].includes(e.type)}function HasPostField(){for(var e=0;e<form.fields.length;e++){var t=form.fields[e].type;if("post_title"==t||"post_content"==t||"post_excerpt"==t)return!0}return!1}function HasPageField(){for(var e=0;e<form.fields.length;e++)if("page"==form.fields[e].type)return!0;return!1}function GetInput(e,t){if(void 0!==e.inputs&&jQuery.isArray(e.inputs))for(i in e.inputs)if(e.inputs.hasOwnProperty(i)){var o=e.inputs[i];if(o.id==t)return o}return null}function IsPricingField(e){return IsProductField(e)||"donation"==e}function IsProductField(e){return-1!=jQuery.inArray(e,["option","quantity","product","total","shipping","calculation"])}function GetLabel(e,t,i){void 0===i&&(i=!1);var t=GetInput(e,t=void 0===t?0:t),o="",o=null!=e.adminLabel&&0<e.adminLabel.length?e.adminLabel:e.label;return null!=t?i?t.label:o+" ("+t.label+")":o}function DeleteNotification(e){jQuery("#action_argument").val(e),jQuery("#action").val("delete"),jQuery("#notification_list_form")[0].submit()}function DuplicateNotification(e){jQuery("#action_argument").val(e),jQuery("#action").val("duplicate"),jQuery("#notification_list_form")[0].submit()}function DeleteConfirmation(e){jQuery("#action_argument").val(e),jQuery("#action").val("delete"),jQuery("#confirmation_list_form")[0].submit()}function DuplicateConfirmation(e){jQuery("#action_argument").val(e),jQuery("#action").val("duplicate"),jQuery("#confirmation_list_form")[0].submit()}function SetConfirmationConditionalLogic(){confirmation.conditionalLogic=jQuery("#conditional_logic").val()?jQuery.parseJSON(jQuery("#conditional_logic").val()):new ConditionalLogic}function ToggleConfirmation(){var e,t="",i=jQuery("#form_confirmation_redirect").is(":checked"),o=jQuery("#form_confirmation_show_page").is(":checked");i?(e=".form_confirmation_redirect_container",t="#form_confirmation_message_container, .form_confirmation_page_container",ClearConfirmationSettings(["text","page"])):o?(e=".form_confirmation_page_container",t="#form_confirmation_message_container, .form_confirmation_redirect_container",ClearConfirmationSettings(["text","redirect"])):(e="#form_confirmation_message_container",t=".form_confirmation_page_container, .form_confirmation_redirect_container",ClearConfirmationSettings(["page","redirect"])),ToggleQueryString(),TogglePageQueryString(),jQuery(t).hide(),jQuery(e).show()}function ToggleQueryString(){jQuery("#form_redirect_use_querystring").is(":checked")?jQuery("#form_redirect_querystring_container").show():(jQuery("#form_redirect_querystring_container").hide(),jQuery("#form_redirect_querystring").val(""),jQuery("#form_redirect_use_querystring").val(""))}function TogglePageQueryString(){jQuery("#form_page_use_querystring").is(":checked")?jQuery("#form_page_querystring_container").show():(jQuery("#form_page_querystring_container").hide(),jQuery("#form_page_querystring").val(""),jQuery("#form_page_use_querystring").val(""))}function ClearConfirmationSettings(e){var t=jQuery.isArray(e)?e:[e];for(i in t)if(t.hasOwnProperty(i))switch(t[i]){case"text":jQuery("#form_confirmation_message").val(""),jQuery("#form_disable_autoformatting").prop("checked",!1);break;case"page":jQuery("#form_confirmation_page").val(""),jQuery("#form_page_querystring").val(""),jQuery("#form_page_use_querystring").prop("checked",!1);break;case"redirect":jQuery("#form_confirmation_url").val(""),jQuery("#form_redirect_querystring").val(""),jQuery("#form_redirect_use_querystring").prop("checked",!1)}}function StashConditionalLogic(){var e=JSON.stringify(confirmation.conditionalLogic);jQuery("#conditional_logic").val(e)}function ConfirmationObj(){this.id=!1,this.name=gf_vars.confirmationDefaultName,this.type="message",this.message=gf_vars.confirmationDefaultMessage,this.isDefault=0}function Copy(e){if(e&&"object"==typeof e)for(i in e=jQuery.isArray(e)?e.slice():jQuery.extend({},e))e[i]=Copy(e[i]);return e}jQuery(document).ready(function(e){gaddon.init(),gform.adminUtils.handleUnsavedChanges("#gform-settings"),e(document).on("change",".gfield_rule_value_dropdown",function(){SetRuleValueDropDown(e(this))}),window.form&&(window.gfMergeTags=new gfMergeTagsObj(form)),e(document).ready(function(){e(".gform_currency").bind("change",function(){FormatCurrency(this)}).each(function(){FormatCurrency(this)})})}),((r,t)=>{r.init=function(){void 0!==(f=window.form)&&f.id},r.toggleFeedSwitch=function(e,t){var i=window.gform_admin_config.i18n;t?jQuery(e).removeClass("gform-status--active").addClass("gform-status--inactive").find(".gform-status-indicator-status").html(i.form_admin.toggle_feed_inactive):jQuery(e).removeClass("gform-status--inactive").addClass("gform-status--active").find(".gform-status-indicator-status").html(i.form_admin.toggle_feed_active)},r.toggleFeedActive=function(o,e,t){var n=jQuery(o).hasClass("gform-status--active");return jQuery.post(ajaxurl,{action:"gf_feed_is_active_"+e,feed_id:t,is_active:n?0:1,nonce:jQuery("#feed_list").val()},function(e){e.success?r.toggleFeedSwitch(o,n):(r.toggleFeedSwitch(o,!n),gform.instances.dialogAlert(e.data.message))}).fail(function(e,t,i){r.toggleFeedSwitch(o,!n),gform.instances.dialogAlert(i)}),!0},r.deleteFeed=function(e){t("#single_action").val("delete"),t("#single_action_argument").val(e),t("#gform-settings").submit()},r.duplicateFeed=function(e){t("#single_action").val("duplicate"),t("#single_action_argument").val(e),t("#gform-settings").submit()}})(window.gaddon=window.gaddon||{},jQuery);var gfMergeTagsObj=function(e,t){var f=this;f.form=e,f.elem=t,f.init=function(){f.elem.data("mergeTags")||(f.mergeTagList=jQuery('<ul id="gf_merge_tag_list" class=""></ul>'),f.mergeTagListHover=!1,f.bindKeyDown(),f.initAutocomplete(),f.addMergeTagIcon(),f.mergeTagIcon.find(".open-list").on("click.gravityforms",function(e){e.preventDefault();var e=jQuery(this),t=f.getTargetElement(e);f.mergeTagList.html(""),f.mergeTagList.append(f.getMergeTagListItems(t)),f.mergeTagList.insertAfter(e).show()}),f.mergeTagList.hover(function(){f.mergeTagListHover=!0},function(){f.mergeTagListHover=!1}),jQuery("body").mouseup(function(){f.mergeTagListHover||f.mergeTagList.hide()}),f.elem.data("mergeTags",f))},f.destroy=function(e){(e=f.elem||e).next(".all-merge-tags").remove(),e.off("keydown.gravityforms"),e.autocomplete("destroy"),e.data("mergeTags",null)},f.bindKeyDown=function(){f.elem.on("keydown.gravityforms",function(e){var t=!(!f.elem.data("autocomplete")||!f.elem.data("autocomplete").menu)&&f.elem.data("autocomplete").menu.active;e.keyCode===jQuery.ui.keyCode.TAB&&t&&e.preventDefault()})},f.initAutocomplete=function(){f.elem.autocomplete({minLength:1,focus:function(){return!1},source:function(e,t){var i=f.extractLast(e.term);i.length<f.elem.autocomplete("option","minLength")?t([]):t(jQuery.map(f.getAutoCompleteMergeTags(f.elem),function(e){return f.startsWith(e,i)?e:null}))},select:function(e,t){var i=this.value.split(" ");return i.pop(),i.push(t.item.value),this.value=i.join(" "),f.elem.trigger("input").trigger("propertychange"),!1}})},f.addMergeTagIcon=function(){var e=f.elem.is("input")?"input":"textarea",t=f.getClassProperty(f.elem,"position");f.mergeTagIcon=jQuery('<span class="all-merge-tags '+t+" "+e+'"><button class="open-list tooltip-merge-tag gform-button gform-button--unstyled" title="'+gf_vars.mergeTagsText+'"><i class="gform-icon gform-icon--merge-tag gform-button__icon"></i>'+gf_vars.mergeTagsText+"</button></span>"),f.mergeTagIcon.data("targetElement",f.elem.attr("id")),f.getClassProperty(f.elem,"manual_position")?(t=f.elem.attr("id").substring(1,f.elem.attr("id").length),jQuery("#"+t).find(".gform-tinymce-mergetag-button").append(f.mergeTagIcon)):f.elem.after(f.mergeTagIcon)},f.bindMergeTagListClick=function(e){f.mergeTagList.hide();var t=jQuery(e.target).data("value"),e=f.getTargetElement(e.target);f.isWpEditor(e)?InsertEditorVariable(e.attr("id"),t):InsertVariable(e.attr("id"),null,t),e.trigger("input").trigger("propertychange"),f.mergeTagList.hide()},this.getMergeTags=function(e,t,i,o,n,r){void 0===e&&(e=[]),void 0===o&&(o=[]);var a,s,l,c,u,d,g=[],f=[],p=[],m=[],h=[],_=[],y=[],v=[],b=[];if(i||m.push({tag:"{all_fields}",label:this.getMergeTagLabel("{all_fields}")}),!n){for(j in e)e.hasOwnProperty(j)&&((a=e[j]).displayOnly||(s=GetInputType(a),-1==jQuery.inArray(s,o)&&(a.isRequired?"name"===s?(s=Copy(a),"extended"==a.nameFormat?(l=GetInput(a,a.id+".2"),u=GetInput(a,a.id+".8"),(d=Copy(a)).inputs=[l,u],f.push(d),delete s.inputs[0],delete s.inputs[3]):"advanced"==a.nameFormat&&(l=GetInput(a,a.id+".2"),c=GetInput(a,a.id+".4"),u=GetInput(a,a.id+".8"),(d=Copy(a)).inputs=[l,c,u],f.push(d),delete s.inputs[0],delete s.inputs[2],delete s.inputs[4]),g.push(s)):g.push(a):f.push(a),IsPricingField(a.type))&&p.push(a)));if(0<g.length)for(j in g)g.hasOwnProperty(j)&&(h=h.concat(this.getFieldMergeTags(g[j],r)));if(0<f.length)for(j in f)f.hasOwnProperty(j)&&(_=_.concat(this.getFieldMergeTags(f[j],r)));if(0<p.length)for(j in i||y.push({tag:"{pricing_fields}",label:this.getMergeTagLabel("{pricing_fields}")}),p)p.hasOwnProperty(j)&&y.concat(this.getFieldMergeTags(p[j],r))}var j,w=["ip","date_mdy","date_dmy","embed_post:ID","embed_post:post_title","embed_url","entry_id","entry_url","form_id","form_title","user_agent","referer","post_id","post_edit_url","user:display_name","user:user_email","user:user_login"];for(j in n&&(w.splice(w.indexOf("entry_id"),1),w.splice(w.indexOf("entry_url"),1),w.splice(w.indexOf("form_id"),1),w.splice(w.indexOf("form_title"),1)),HasPostField()&&!n||(w.splice(w.indexOf("post_id"),1),w.splice(w.indexOf("post_edit_url"),1)),w)-1==jQuery.inArray(w[j],o)&&v.push({tag:"{"+w[j]+"}",label:this.getMergeTagLabel("{"+w[j]+"}")});var Q,T=this.getCustomMergeTags();if(0<T.tags.length)for(j in T.tags)T.tags.hasOwnProperty(j)&&(Q=T.tags[j],b.push({tag:Q.tag,label:Q.label}));m={ungrouped:{label:this.getMergeGroupLabel("ungrouped"),tags:m},required:{label:this.getMergeGroupLabel("required"),tags:h},optional:{label:this.getMergeGroupLabel("optional"),tags:_},pricing:{label:this.getMergeGroupLabel("pricing"),tags:y},other:{label:this.getMergeGroupLabel("other"),tags:v},custom:{label:this.getMergeGroupLabel("custom"),tags:b}};return gform.applyFilters("gform_merge_tags",m,t,i,o,n,r,this)},this.getMergeTagLabel=function(e){for(groupName in gf_vars.mergeTags)if(gf_vars.mergeTags.hasOwnProperty(groupName)){var t=gf_vars.mergeTags[groupName].tags;for(i in t)if(t.hasOwnProperty(i)&&t[i].tag==e)return t[i].label}return""},this.getMergeGroupLabel=function(e){return gf_vars.mergeTags[e].label},this.getFieldMergeTags=function(e,t){void 0===t&&(t="");var o,n=[],r=GetInputType(e),a="list"==r?":"+t:"",s="",l="";if(-1<jQuery.inArray(r,["date","email","time","password"])&&(e.inputs=null),void 0!==e.inputs&&jQuery.isArray(e.inputs))for(i in"checkbox"==r&&(s="{"+(l=GetLabel(e,e.id).replace("'","\\'"))+":"+e.id+a+"}",n.push({tag:s,label:l})),e.inputs)e.inputs.hasOwnProperty(i)&&(o=e.inputs[i],"creditcard"==r&&-1<jQuery.inArray(parseFloat(o.id),[parseFloat(e.id+".2"),parseFloat(e.id+".3"),parseFloat(e.id+".5")])||(s="{"+(l=GetLabel(e,o.id).replace("'","\\'"))+":"+o.id+a+"}",n.push({tag:s,label:l})));else s="{"+(l=GetLabel(e).replace("'","\\'"))+":"+e.id+a+"}",n.push({tag:s,label:l});return n},f.getCustomMergeTags=function(){for(groupName in gf_vars.mergeTags)if(gf_vars.mergeTags.hasOwnProperty(groupName)&&"custom"==groupName)return gf_vars.mergeTags[groupName];return[]},this.getAutoCompleteMergeTags=function(e){var t=this.form.fields,o=e.attr("id"),n=1==this.getClassProperty(e,"hide_all_fields"),r=this.getClassProperty(e,"exclude"),a=this.getClassProperty(e,"option"),e=this.getClassProperty(e,"prepopulate"),s=this.getMergeTags(t,o,n=e?!0:n,r,e,a),l=[];for(group in s)if(s.hasOwnProperty(group)){var c=s[group].tags;for(i in c)c.hasOwnProperty(i)&&l.push(c[i].tag)}return l},this.getMergeTagListItems=function(e){var t=this.form.fields,o=e.attr("id"),n=1==this.getClassProperty(e,"hide_all_fields"),r=this.getClassProperty(e,"exclude"),a=this.getClassProperty(e,"prepopulate"),e=this.getClassProperty(e,"option"),s=this.getMergeTags(t,o,n=a?!0:n,r,a,e),l=this.hasMultipleGroups(s),c=[];for(group in s)if(s.hasOwnProperty(group)){var u,d=s[group].label,g=s[group].tags;if(!(g.length<=0))for(i in d&&l&&c.push(jQuery('<li class="group-header">'+d+"</li>")),g)g.hasOwnProperty(i)&&(u=g[i],d=gform.tools.stripSlashes(u.label),(u=jQuery('<a class="" data-value="'+escapeAttr(u.tag)+'">'+escapeHtml(d)+"</a>")).on("click.gravityforms",f.bindMergeTagListClick),c.push(jQuery("<li></li>").html(u)))}return c},this.hasMultipleGroups=function(e){var t=0;for(group in e)e.hasOwnProperty(group)&&0<e[group].tags.length&&t++;return 1<t},f.getClassProperty=function(e,t){e=(e=jQuery(e)).attr("class");if(e){var o=e.split(" ");for(i in o)if(o.hasOwnProperty(i)){var n=o[i].split("-");if("mt"==n[0]&&n[1]==t)return 3<n.length?(delete n[0],delete n[1],n):2==n.length||n[2]}}return""},f.getTargetElement=function(e){e=(e=jQuery(e)).parents("span.all-merge-tags").data("targetElement");return jQuery("#"+e.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g,"\\$&"))},f.isWpEditor=function(e){e=jQuery(e);return 1==this.getClassProperty(e,"wp_editor")},f.split=function(e){return e.split(" ")},f.extractLast=function(e){return this.split(e).pop()},f.startsWith=function(e,t){return 0===e.indexOf(t)},f.elem&&f.init()},FeedConditionObj=function(e){this.strings=isSet(e.strings)?e.strings:{},this.logicObject=e.logicObject,this.init=function(){gform.addFilter("gform_conditional_object","FeedConditionConditionalObject"),gform.addFilter("gform_conditional_logic_description","FeedConditionConditionalDescription"),jQuery(document).ready(function(){ToggleConditionalLogic(!0,"feed_condition")})},this.init()};function SimpleConditionObject(e,t){return t.indexOf("simple_condition")<0?e:(e=t.substring(17)+"_object",window[e])}function FeedConditionConditionalObject(e,t){return"feed_condition"!=t?e:feedCondition.logicObject}function FeedConditionConditionalDescription(e,t,i,o){return"feed_condition"!=i?e:(t.actionType=t.actionType.replace("<select",'<select style="display:none;"'),t.objectDescription=feedCondition.strings.objectDescription,makeArray(t).join(" "))}function makeArray(e){var t=[];for(i in e)t.push(e[i]);return t}function isSet(e){return void 0!==e}jQuery(document).ready(function(){var e,t=jQuery(".gform-form-toolbar__form-title span:not(.gform-dropdown__trigger-text):not(.gform-dropdown__control-text):not(.gform-visually-hidden)");t&&((e=t.clone().css({display:"inline",width:"auto",visibility:"hidden"}).appendTo(t)).width()>t.width()&&jQuery(".gform-form-toolbar__form-title span").tooltip({position:{my:"left center",at:"right+6 center"},tooltipClass:"arrow-left"}),e.remove())});var entityMap={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function escapeAttr(e){return String(e).replace(/["']/g,function(e){return entityMap[e]})}function escapeHtml(e){return String(e).replace(/[&<>"'`=\/]/g,function(e){return entityMap[e]})}var gform=window.gform||{};gform.components=gform.components||{},gform.components.dropdown=function(e){this.el=null,this.control=null,this.controlText=null,this.triggers=[],this.state={open:!1,unloading:!1},this.options={closeOnSelect:!0,container:document,detectTitleLength:!1,onItemSelect:function(){},reveal:"click",selector:"",showSpinner:!1,swapLabel:!0,titleLengthThresholdMedium:23,titleLengthThresholdLong:32},this.options=gform.tools.mergeObjects(this.options,gform.tools.defaultFor(e,{})),this.el=gform.tools.getNodes(this.options.selector,!1,this.options.container)[0],this.el?(this.titleEl=gform.tools.getNodes("gform-dropdown-control-text",!1,this.el)[0],this.storeTriggers(),this.bindEvents(),this.setupUI(),this.hideSpinner=function(){this.el.classList.remove("gform-dropdown--show-spinner")},this.showSpinner=function(){this.el.classList.add("gform-dropdown--show-spinner")}):gform.console.error("Gform dropdown couldn't find [data-js=\""+this.options.selector+'"] to instantiate on.')},gform.components.dropdown.prototype.handleChange=function(e){this.options.onItemSelect(e.target.dataset.value),this.options.showSpinner&&this.showSpinner(),this.options.swapLabel&&(this.controlText.innerText=e.target.innerText),this.options.closeOnSelect&&this.handleControl()},gform.components.dropdown.prototype.handleControl=function(){this.state.open?this.closeDropdown():this.openDropdown()},gform.components.dropdown.prototype.openDropdown=function(){this.state.open||(this.el.classList.add("gform-dropdown--reveal"),setTimeout(function(){this.el.classList.add("gform-dropdown--open"),this.control.setAttribute("aria-expanded","true"),this.state.open=!0}.bind(this),25),setTimeout(function(){this.el.classList.remove("gform-dropdown--reveal")}.bind(this),200))},gform.components.dropdown.prototype.closeDropdown=function(){this.state.open=!1,this.el.classList.remove("gform-dropdown--open"),this.el.classList.add("gform-dropdown--hide"),this.control.setAttribute("aria-expanded","false"),setTimeout(function(){this.el.classList.remove("gform-dropdown--hide")}.bind(this),150)},gform.components.dropdown.prototype.handleMouseenter=function(){"hover"!==this.options.reveal||this.state.open||this.state.unloading||this.openDropdown()},gform.components.dropdown.prototype.handleMouseleave=function(e){"hover"!==this.options.reveal||this.state.unloading||this.closeDropdown()},gform.components.dropdown.prototype.handleA11y=function(e){this.state.open&&(27===e.keyCode?(this.closeDropdown(),this.control.focus()):9!==e.keyCode||gform.tools.getClosest(e.target,'[data-js="'+this.options.selector+'"]')||this.triggers[0].focus())},gform.components.dropdown.prototype.handleSearch=function(e){var t=e.target.value.toLowerCase();this.triggers.forEach(function(e){e.innerText.toLowerCase().includes(t)?e.parentNode.style.display="":e.parentNode.style.display="none"})},gform.components.dropdown.prototype.setupUI=function(){var e;"hover"===this.options.reveal&&this.el.classList.add("gform-dropdown--hover"),this.options.detectTitleLength&&((e=this.titleEl?this.titleEl.innerText:"").length>this.options.titleLengthThresholdMedium&&e.length<=this.options.titleLengthThresholdLong?this.el.parentNode.classList.add("gform-dropdown--medium-title"):e.length>this.options.titleLengthThresholdLong&&this.el.parentNode.classList.add("gform-dropdown--long-title"))},gform.components.dropdown.prototype.storeTriggers=function(){this.control=gform.tools.getNodes("gform-dropdown-control",!1,this.el)[0],this.controlText=gform.tools.getNodes("gform-dropdown-control-text",!1,this.control)[0],this.triggers=gform.tools.getNodes("gform-dropdown-trigger",!0,this.el)},gform.components.dropdown.prototype.bindEvents=function(){gform.tools.delegate('[data-js="'+this.options.selector+'"]',"click",'[data-js="gform-dropdown-trigger"], [data-js="gform-dropdown-trigger"] > span',this.handleChange.bind(this)),gform.tools.delegate('[data-js="'+this.options.selector+'"]',"click",'[data-js="gform-dropdown-trigger"]',this.handleChange.bind(this)),gform.tools.delegate('[data-js="'+this.options.selector+'"]',"click",'[data-js="gform-dropdown-control"], [data-js="gform-dropdown-control"] *',this.handleControl.bind(this)),gform.tools.delegate('[data-js="'+this.options.selector+'"]',"keyup",'[data-js="gform-dropdown-search"]',this.handleSearch.bind(this)),this.el.addEventListener("mouseenter",this.handleMouseenter.bind(this)),this.el.addEventListener("mouseleave",this.handleMouseleave.bind(this)),this.el.addEventListener("keyup",this.handleA11y.bind(this)),document.addEventListener("keyup",this.handleA11y.bind(this)),document.addEventListener("click",function(e){!this.el.contains(e.target)&&this.state.open&&this.handleControl()}.bind(this)),addEventListener("beforeunload",function(){this.state.unloading=!0}.bind(this))},gform.components.alert={instances:[],getInstance:function(t){return gform.components.alert.instances.filter(function(e){return e.id===t.getAttribute("data-gform-alert-instance")})[0]},initializeInstance:function(e){var t,i;e.hasAttribute("data-gform-alert-instance")||(t=gform.tools.uniqueId("gform-alert"),i=e.hasAttribute("data-gform-alert-cookie")?e.getAttribute("data-gform-alert-cookie"):"",e.setAttribute("data-gform-alert-instance",t),e.classList.add("gform-initialized"),gform.components.alert.instances.push({id:t,cookie:i}))},initializeInstances:function(){gform.tools.getNodes('[data-js="gform-alert"]:not(.gform-initialized)',!0,document,!0).forEach(gform.components.alert.initializeInstance)},dismissAlert:function(e){var e=gform.tools.getClosest(e.target,'[data-js="gform-alert"]'),t=gform.components.alert.getInstance(e);e.style.display="none",t.cookie&&gform.tools.setCookie(t.cookie,form.id,1,!0)},bindEvents:function(){document.addEventListener("gform_init_alerts",gform.components.alert.initializeInstances),gform.tools.delegate("body","click",'[data-js="gform-alert-dismiss-trigger"]',gform.components.alert.dismissAlert)},init:function(){gform.components.alert.bindEvents(),gform.components.alert.initializeInstances()}},document.addEventListener("gform_main_scripts_loaded",gform.components.alert.init),gform.simplebar={instances:[],cleanInstances:function(){gform.simplebar.instances=gform.simplebar.instances.filter(function(e,t){return!!gform.tools.getNodes('[data-simplebar-instance="'+e.id+'"]',!1,document,!0)[0]||(gform.simplebar.instances[t].instance.unMount(),!1)})},getInstance:function(t){return gform.simplebar.instances.filter(function(e){return e.id===t.getAttribute("data-simplebar-instance")})[0].instance},initializeInstance:function(t){var i,e;t.hasAttribute("data-simplebar-instance")||(i=gform.tools.uniqueId("simplebar"),e=(e=t.getAttribute("data-simplebar-delay"))?parseInt(e,10):0,setTimeout(function(){var e=gform.tools.isRtl()?"rtl":"ltr",e=("rtl"==e&&t.setAttribute("data-simplebar-direction","rtl"),t.setAttribute("data-simplebar-instance",i),t.classList.add("gform-initialized"),new SimpleBar(t,{direction:e}));gform.simplebar.instances.push({id:i,instance:e})},e))},initializeInstances:function(){gform.simplebar.cleanInstances(),gform.tools.getNodes('[data-js="gform-simplebar"]:not(.gform-initialized)',!0,document,!0).forEach(gform.simplebar.initializeInstance)},bindEvents:function(){document.addEventListener("gform_render_simplebars",gform.simplebar.initializeInstances)},init:function(){window.SimpleBar&&(gform.simplebar.bindEvents(),gform.simplebar.initializeInstances())}},document.addEventListener("gform_main_scripts_loaded",gform.simplebar.init);
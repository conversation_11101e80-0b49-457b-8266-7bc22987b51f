/*
----------------------------------------------------------------

browsers.css
Gravity Forms Browser-Specific CSS
http://www.gravityforms.com
updated: May 22, 2018 03:13 PM US Eastern Time

Gravity Forms is a Rocketgenius project
copyright 2008-2025 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be redistributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE! MAKE ANY MODIFICATIONS IN YOUR
THEME STYLESHEET. THIS FILE IS REPLACED DURING AUTO-UPDATES
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

If you need to make extensive customizations,
copy the contents of this file to your theme
style sheet for editing. Then, go to the form
settings page & set the 'output CSS' option
to no.

----------------------------------------------------------------
*/

/* Safari specific styles */

.gform_legacy_markup_wrapper.gf_browser_safari ul li:before,
.gform_legacy_markup_wrapper.gf_browser_safari ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_safari ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_safari ul li:after {
    content: none;
}

.gform_legacy_markup_wrapper.gf_browser_safari .gform_body ul.gform_fields li.gfield .ginput_container #recaptcha_widget_div #recaptcha_area {
    width: 99% !important;
}

.gform_legacy_markup_wrapper.gf_browser_safari .left_label #recaptcha_area #recaptcha_table,
.gform_legacy_markup_wrapper.gf_browser_safari .right_label #recaptcha_area #recaptcha_table {
    margin-left: 32%;
}

.gform_legacy_markup_wrapper.gf_browser_safari .gfield_checkbox li input[type=checkbox],
.gform_legacy_markup_wrapper.gf_browser_safari .gfield_radio li input[type=radio],
.gform_legacy_markup_wrapper.gf_browser_safari .gfield_checkbox li input {
    margin-top: 4px;
}

.gform_legacy_markup_wrapper.gf_browser_safari select[multiple=multiple] {
    height: auto !important;
}

.gform_legacy_markup_wrapper.gf_browser_safari input.button.gform_button_select_files {
    padding: 6px 12px !important;
}

/* Google Chrome (and now Opera) styles */

.gform_legacy_markup_wrapper.gf_browser_chrome select {
    padding: 2px 0 2px 3px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome ul li:before,
.gform_legacy_markup_wrapper.gf_browser_chrome ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_chrome ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_chrome ul li:after {
    content: none;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .gform_body ul.gform_fields li.gfield .ginput_container #recaptcha_widget_div #recaptcha_area {
    width: 99% !important;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .left_label #recaptcha_area #recaptcha_table,
.gform_legacy_markup_wrapper.gf_browser_chrome .right_label #recaptcha_area #recaptcha_table {
    margin-left: 32%;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .ginput_complex select,
.gform_legacy_markup_wrapper.gf_browser_chrome .ginput_complex .ginput_right select {
    text-indent: 2px;
    line-height: 1.5em;
    margin-bottom: 5px;
    margin-top: 2px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_checkbox li input[type=checkbox],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_radio li input[type=radio],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_checkbox li input {
    margin-top: 6px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield div.ginput_complex span.ginput_left select,
.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield div.ginput_complex span.ginput_right select,
.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield select {
    margin-left: 1px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield input[type=radio] {
    margin-left: 1px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield span.name_first,
.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield span.name_middle,
.gform_legacy_markup_wrapper.gf_browser_chrome ul.gform_fields li.gfield span.name_last {
    padding-top: 2px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome select[multiple=multiple] {
    height: auto !important;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error .ginput_complex.ginput_container.has_first_name.has_middle_name.has_last_name span.name_middle,
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error .ginput_complex.ginput_container.has_first_name.has_middle_name.has_last_name span.name_last {
    margin-left: 1.1%;
}

.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=text],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=email],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=tel],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=url],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=number],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error input[type=password],
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error select,
.gform_legacy_markup_wrapper.gf_browser_chrome .gfield_error textarea,
.gform_legacy_markup_wrapper.gf_browser_chrome li.gfield_error.field_sublabel_above .ginput_complex input[type=text] {
    margin-bottom: 2px;
}

.gform_legacy_markup_wrapper.gf_browser_chrome input.button.gform_button_select_files {
    padding: 6px 12px !important;
}

.gform_legacy_markup_wrapper.gf_browser_chrome span.address_country {
        margin-top:-2px;
}

/* Firefox specific styles */

.gform_legacy_markup_wrapper.gf_browser_gecko select {
    padding: 2px 12px 2px 2px;
}

.gform_legacy_markup_wrapper.gf_browser_gecko ul li:before,
.gform_legacy_markup_wrapper.gf_browser_gecko ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_gecko ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_gecko ul li:after {
    content: none;
}

.gform_legacy_markup_wrapper.gf_browser_gecko .ginput_complex .ginput_cardinfo_left select.ginput_card_expiration.ginput_card_expiration_month {
    margin-right: 1px;
}

.gform_legacy_markup_wrappergf_browser_gecko .chosen-container-single .chosen-single {
    height: 32px;
    line-height: 2.4;
}

.gform_legacy_markup_wrappergf_browser_gecko .chosen-container-single .chosen-single div b {
    position: relative;
    top: 5px;
}

/* Internet Explorer specific styles */

.gform_legacy_markup_wrapper.gf_browser_ie ul li:before,
.gform_legacy_markup_wrapper.gf_browser_ie ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_ie ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_ie ul li:after {
    content: none;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gfield_time_hour {
    width: 80px;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gfield_time_minute {
    width: 70px;
}

.gform_legacy_markup_wrapper.gf_browser_ie .ginput_complex .ginput_left label,
.gform_legacy_markup_wrapper.gf_browser_ie .ginput_complex .ginput_right label {
    margin: 3px 0 5px 0;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gform_footer input.button {
    padding: 3px;
}

.gform_legacy_markup_wrapper.gf_browser_ie ul.top_label .clear-multi {
    overflow: hidden;
    clear: both;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gfield_radio li {
    line-height: 20px !important;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gfield_checkbox li input[type=checkbox],
.gform_legacy_markup_wrapper.gf_browser_ie .gfield_radio li input[type=radio],
.gform_legacy_markup_wrapper.gf_browser_ie .gfield_checkbox li input {
    margin-top: 0;
}

.gform_legacy_markup_wrapper.gf_browser_ie .ginput_complex .ginput_left select,
.gform_legacy_markup_wrapper.gf_browser_ie .ginput_complex .ginput_right select {
    padding: 2px 0 2px 0;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gform_body ul.gform_fields li.gfield .ginput_container #recaptcha_widget_div #recaptcha_area {
    width: 99% !important;
}

.gform_legacy_markup_wrapper.gf_browser_ie .gform_body ul.gform_fields li.gfield .ginput_container #recaptcha_widget_div #recaptcha_area {
    width: 99% !important;
}

.gform_legacy_markup_wrapper.gf_browser_ie .left_label #recaptcha_area #recaptcha_table, .gform_legacy_markup_wrapper.gf_browser_ie .right_label #recaptcha_area #recaptcha_table { margin-left: 32%;
}

.gform_legacy_markup_wrapper.gf_browser_ie .ginput_complex .ginput_cardinfo_right span.ginput_card_security_code_icon {
    position: relative;
    top: -1px;
    left: 4px;
}

.gform_legacy_markup_wrapper.gf_browser_ie img.add_list_item,
.gform_legacy_markup_wrapper.gf_browser_ie img.delete_list_item {
    width: 16px !important;
    height: 16px !important;
    background-size: 16px 16px !important;
}

.gform_legacy_markup_wrapper.gf_browser_iphone ul li:before,
.gform_legacy_markup_wrapper.gf_browser_iphone ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_iphone ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_iphone ul li:after {
    content: none;
}

.gform_legacy_markup_wrapper.gf_browser_unknown ul li:before,
.gform_legacy_markup_wrapper.gf_browser_unknown ul li:after,
.entry .gform_legacy_markup_wrapper.gf_browser_unknown ul li:before,
.entry .gform_legacy_markup_wrapper.gf_browser_unknown ul li:after {
    content: none;
}

@media only screen and (max-width: 641px)  {

    /* make the nifty styled selects a little larger for mobile devices */

    .gform_legacy_markup_wrapper.gf_browser_gecko .chosen-container-single .chosen-single,
    .gform_legacy_markup_wrapper.gf_browser_safari .chosen-container-single .chosen-single,
    .gform_legacy_markup_wrapper.gf_browser_chrome .chosen-container-single .chosen-single {
        height: 44px;
        line-height: 3.2;
    }

    .gform_legacy_markup_wrapper.gf_browser_gecko .chosen-container-single .chosen-single div b,
    .gform_legacy_markup_wrapper.gf_browser_safari .chosen-container-single .chosen-single div b,
    .gform_legacy_markup_wrapper.gf_browser_chrome .chosen-container-single .chosen-single div b {
        position: relative;
        top: 50%;
        transform: translateY(-25%);
    }

 }

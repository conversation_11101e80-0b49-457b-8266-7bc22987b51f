/*
----------------------------------------------------------------

Gravity Forms Date Picker Styles
http: //www.gravityforms.com
updated: January 31, 2014 3:32 PM

Customized styles for the jQuery UI Datepicker 1.9.2
copyright 2012 jQuery Foundation and other contributors
Released under the MIT license.
http://jquery.org/license
some styles courtesty of http://www.hongkiat.com/

Gravity Forms is a Rocketgenius project
copyright 2008 - 2014 Rocketgenius Inc.
http: //www.rocketgenius.com

NOTE: DO NOT EDIT THIS FILE! MAKE ANY MODIFICATIONS IN YOUR
THEME STYLESHEET. THIS FILE IS REPLACED DURING AUTO-UPDATES
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

If you need to make extensive customizations,
copy the contents of this file to your theme
style sheet for editing. Then, go to the form
settings page & set the 'output CSS' option
to no.

----------------------------------------------------------------
*/

.gform-legacy-datepicker.ui-datepicker {
	height: auto;
	margin: 5px auto 0;
	font: 9pt Arial, sans-serif;
	min-width: 216px;
	-webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
	-moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
}

.gform-legacy-datepicker.ui-datepicker a {
	text-decoration: none;
}

.gform-legacy-datepicker.ui-datepicker table {
	border-collapse: collapse;
	width: 100%;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-header .ui-datepicker-header,
.gform-legacy-datepicker.ui-datepicker .ui-datepicker-header {
	background-color: #666; /* set the header background color */
	border-color: #666;
	border-style: solid;
	border-width: 1px 0 0 0;
	-moz-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
	box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
	color: #e0e0e0;
	filter: dropshadow(color=#000, offx=1, offy=-1);
	font-weight: bold;
	line-height: 31px;
	min-height: 31px !important;
	text-shadow: 1px -1px 0px #000;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-header .ui-icon {
	display: none;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-title {
	text-align: center;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-title select {
	margin-top: 2.5%;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-prev,
.gform-legacy-datepicker.ui-datepicker .ui-datepicker-next {
	display: inline-block;
	width: 30px;
	height: 30px;
	text-align: center;
	cursor: pointer;
	background-image: url('../images/datepicker/arrow.png');
	background-repeat: no-repeat;
	line-height: 600%;
	overflow: hidden;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-prev {
	float: left;
	background-position: center -30px;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-next {
	float: right;
	background-position: center 0px;
}

.gform-legacy-datepicker.ui-datepicker thead {
	background: #f7f7f7;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Y3ZjdmNyIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmMWYxZjEiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top,  #f7f7f7 0%, #f1f1f1 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#f1f1f1));
	background: -webkit-linear-gradient(top,  #f7f7f7 0%,#f1f1f1 100%);
	background: -o-linear-gradient(top,  #f7f7f7 0%,#f1f1f1 100%);
	background: -ms-linear-gradient(top,  #f7f7f7 0%,#f1f1f1 100%);
	background: linear-gradient(to bottom,  #f7f7f7 0%,#f1f1f1 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#f1f1f1',GradientType=0 );
	border-bottom: 1px solid #bbb;
}

.gform-legacy-datepicker.ui-datepicker th {
	text-transform: uppercase;
	text-align: center;
	font-size: 6pt;
	padding: 5px 0;
	color: #666666;
	text-shadow: 1px 0px 0px #fff;
	filter: dropshadow(color=#fff, offx=1, offy=0);
}

.gform-legacy-datepicker.ui-datepicker tbody td {
	padding: 0;
	border-top: 1px solid #bbb;
	border-right: 1px solid #bbb;
}

.gform-legacy-datepicker.ui-datepicker tbody td:last-child {
	border-right: 0px;
}

.gform-legacy-datepicker.ui-datepicker tbody tr {
	border-bottom: 1px solid #bbb;
}

.gform-legacy-datepicker.ui-datepicker tbody tr:last-child {
	border-bottom: 0;
}

.gform-legacy-datepicker.ui-datepicker td span,
.gform-legacy-datepicker.ui-datepicker td a {
	display: inline-block;
	font-weight: bold;
	text-align: center;
	width: 100%;
	height: 30px;
	line-height: 30px;
	color: #666666;
	text-shadow: 1px 1px 0 #fff;
	filter: dropshadow(color=#fff, offx=1, offy=1);
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar .ui-state-default {
	background: #ededed;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2VkZWRlZCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkZWRlZGUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top,  #ededed 0%, #dedede 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ededed), color-stop(100%,#dedede));
	background: -webkit-linear-gradient(top,  #ededed 0%,#dedede 100%);
	background: -o-linear-gradient(top,  #ededed 0%,#dedede 100%);
	background: -ms-linear-gradient(top,  #ededed 0%,#dedede 100%);
	background: linear-gradient(to bottom,  #ededed 0%,#dedede 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ededed', endColorstr='#dedede',GradientType=0 );
	-webkit-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
	-moz-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
	box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar .ui-state-hover {
	background: #f7f7f7;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar .ui-state-active {
	background: #FFF2AA; /* set the active date background color */
	border: 1px solid #c19163; /* set the active date border color */
	color: #666; /* set the active date font color */
	-webkit-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
	-moz-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
	box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
	text-shadow: 0px 1px 0px #FFF;
	filter: dropshadow(color=#FFF, offx=0, offy=1);
	position: relative;
	margin: -1px;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-unselectable .ui-state-default {
	background: #f4f4f4;
	color: #b4b3b3;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar td:first-child .ui-state-active {
	width: 29px;
	margin-left: 0;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar td:last-child .ui-state-active {
	width: 29px;
	margin-right: 0;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-calendar tr:last-child .ui-state-active {
	height: 29px;
	margin-bottom: 0;
}

.gform-legacy-datepicker.ui-datepicker td.ui-datepicker-unselectable.ui-state-disabled {
	background-color: #d7d7d7;
}

.gform-legacy-datepicker.ui-datepicker table.ui-datepicker-calendar {
	margin: 0 0 0 0 !important;
}

body #ui-datepicker-div.gform-legacy-datepicker[style] {
	z-index: 9999 !important;
}

.gform-legacy-datepicker.ui-datepicker:not(.gform-preview-datepicker) .ui-datepicker-header .ui-datepicker-month,
.gform-legacy-datepicker.ui-datepicker:not(.gform-preview-datepicker) .ui-datepicker-header .ui-datepicker-year {
	border-width: 1px;
	display: inline-block;
	font-size: 0.75rem;
	line-height: 1;
	padding-bottom: 3px;
	padding-left: 3px;
	padding-top: 3px;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-header .ui-datepicker-month {
	max-height: 25px;
	max-width: 40%;
}

.gform-legacy-datepicker.ui-datepicker .ui-datepicker-header .ui-datepicker-year {
	max-height: 25px;
	max-width: 30%;
}

# Copyright (C) 2025 Gravity Forms
# This file is distributed under the GPL-2.0+.
msgid ""
msgstr ""
"Project-Id-Version: Gravity Forms 2.9.15\n"
"Report-Msgid-Bugs-To: https://gravityforms.com/support\n"
"Last-Translator: Gravity Forms <<EMAIL>>\n"
"Language-Team: Gravity Forms <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-08-07T17:08:39+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.12.0\n"
"X-Domain: gravityforms\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: gravityforms.php:1593
#: gravityforms.php:1654
#: gravityforms.php:2862
#: includes/blocks/config/class-gf-blocks-config.php:127
#: includes/system-status/class-gf-system-report.php:367
#: includes/system-status/class-gf-update.php:178
msgid "Gravity Forms"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://gravityforms.com"
msgstr ""

#. Description of the plugin
msgid "Easily create web forms and manage form entries within the WordPress admin."
msgstr ""

#: common.php:703
#: common.php:1037
#: form_detail.php:3074
msgid "Insert Merge Tag"
msgstr ""

#: common.php:765
#: js.php:1671
msgid "All Submitted Fields"
msgstr ""

#: common.php:838
msgid "All Pricing Fields"
msgstr ""

#: common.php:847
#: form_detail.php:3075
#: js.php:1671
msgid "User IP Address"
msgstr ""

#: common.php:850
#: common.php:854
#: entry_detail.php:1158
#: form_detail.php:871
#: form_detail.php:1947
#: form_detail.php:3076
#: form_detail.php:3077
#: includes/addon/class-gf-payment-addon.php:3176
#: includes/fields/class-gf-field-date.php:13
#: js.php:907
#: js.php:1671
msgid "Date"
msgstr ""

#: common.php:858
#: form_detail.php:3078
msgid "Embed Post/Page Id"
msgstr ""

#: common.php:862
#: form_detail.php:3079
msgid "Embed Post/Page Title"
msgstr ""

#: common.php:864
#: form_detail.php:3080
#: includes/class-personal-data.php:685
msgid "Embed URL"
msgstr ""

#: common.php:865
#: entry_detail.php:1298
#: entry_list.php:875
#: export.php:1343
#: forms_model.php:6782
#: select_columns.php:196
msgid "Entry Id"
msgstr ""

#: common.php:866
msgid "Entry URL"
msgstr ""

#: common.php:867
msgid "Form Id"
msgstr ""

#: common.php:868
#: form_list.php:42
#: form_settings.php:144
#: gravityforms.php:4982
#: includes/addon/class-gf-addon.php:3194
#: includes/settings/fields/class-generic-map.php:550
#: includes/template-library/config/class-gf-template-library-config.php:90
#: js.php:1671
#: tooltips.php:24
msgid "Form Title"
msgstr ""

#: common.php:869
#: form_detail.php:3081
msgid "HTTP User Agent"
msgstr ""

#: common.php:870
#: form_detail.php:3082
msgid "HTTP Referer URL"
msgstr ""

#: common.php:873
#: export.php:1351
msgid "Post Id"
msgstr ""

#: common.php:876
msgid "Post Edit URL"
msgstr ""

#: common.php:882
#: form_detail.php:3083
msgid "User Display Name"
msgstr ""

#: common.php:884
#: form_detail.php:3084
msgid "User Email"
msgstr ""

#: common.php:885
#: form_detail.php:3085
msgid "User Login"
msgstr ""

#: common.php:898
msgid "Required form fields"
msgstr ""

#: common.php:902
msgid "Optional form fields"
msgstr ""

#: common.php:906
msgid "Pricing form fields"
msgstr ""

#: common.php:910
#: common.php:4842
#: form_detail.php:1624
#: form_detail.php:1625
#: includes/fields/class-gf-field-radio.php:389
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:92
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:192
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:242
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:307
#: includes/template-library/templates/templates.php:6342
#: includes/template-library/templates/templates.php:10043
#: includes/template-library/templates/templates.php:10156
msgid "Other"
msgstr ""

#: common.php:914
#: common.php:1062
#: form_detail.php:516
#: form_detail.php:1920
msgid "Custom"
msgstr ""

#: common.php:1004
msgid "Select image size"
msgstr ""

#: common.php:1005
msgid "Thumbnail"
msgstr ""

#: common.php:1006
msgid "Thumbnail - Left Aligned"
msgstr ""

#: common.php:1007
msgid "Thumbnail - Centered"
msgstr ""

#: common.php:1008
msgid "Thumbnail - Right Aligned"
msgstr ""

#: common.php:1010
#: form_detail.php:924
#: form_detail.php:1635
#: form_display.php:3886
#: includes/blocks/config/class-gf-blocks-config.php:139
#: includes/fields/class-gf-field.php:2758
msgid "Medium"
msgstr ""

#: common.php:1011
msgid "Medium - Left Aligned"
msgstr ""

#: common.php:1012
msgid "Medium - Centered"
msgstr ""

#: common.php:1013
msgid "Medium - Right Aligned"
msgstr ""

#: common.php:1015
#: form_detail.php:925
#: form_detail.php:1635
#: includes/blocks/config/class-gf-blocks-config.php:138
#: includes/fields/class-gf-field.php:2759
msgid "Large"
msgstr ""

#: common.php:1016
msgid "Large - Left Aligned"
msgstr ""

#: common.php:1017
msgid "Large - Centered"
msgstr ""

#: common.php:1018
msgid "Large - Right Aligned"
msgstr ""

#: common.php:1020
msgid "Full Size"
msgstr ""

#: common.php:1021
msgid "Full Size - Left Aligned"
msgstr ""

#: common.php:1022
msgid "Full Size - Centered"
msgstr ""

#: common.php:1023
msgid "Full Size - Right Aligned"
msgstr ""

#: common.php:1038
msgid "Allowable form fields"
msgstr ""

#. translators: %s: relative time from now, used for generic date comparisons. "1 day ago", or "20 seconds ago"
#: common.php:1504
#: common.php:3392
msgid "%s ago"
msgstr ""

#: common.php:2334
msgid "Cannot send email because the TO address is invalid."
msgstr ""

#: common.php:2341
msgid "Cannot send email because there is no SUBJECT and no MESSAGE."
msgstr ""

#: common.php:2348
msgid "Cannot send email because the FROM address is invalid."
msgstr ""

#: common.php:3240
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms"
msgstr ""

#: common.php:3405
msgid "%1$s at %2$s"
msgstr ""

#. Translators: link to the "Edit Post" page for this post.
#: common.php:3936
msgid "You can <a href=\"%s\">edit this post</a> from the post page."
msgstr ""

#: common.php:3959
msgid "Pricing fields are not editable"
msgstr ""

#: common.php:4060
#: common.php:4119
msgid "Preview this form"
msgstr ""

#: common.php:4067
#: gravityforms.php:5923
#: includes/blocks/config/class-gf-blocks-config.php:146
msgid "Preview"
msgstr ""

#. Translators: %s: Link to article about query strings.
#. Translators: %s: Link to Chosen jQuery framework.
#: common.php:4120
#: common.php:5656
#: entry_detail.php:1321
#: form_detail.php:351
#: form_detail.php:3393
#: form_detail.php:3395
#: form_detail.php:3440
#: form_detail.php:3522
#: form_settings.php:129
#: form_settings.php:717
#: form_settings.php:759
#: gravityforms.php:2702
#: help.php:40
#: help.php:42
#: help.php:53
#: help.php:68
#: help.php:75
#: help.php:82
#: help.php:89
#: help.php:96
#: help.php:112
#: help.php:119
#: help.php:126
#: help.php:133
#: help.php:140
#: help.php:156
#: help.php:163
#: help.php:170
#: help.php:177
#: help.php:184
#: includes/addon/class-gf-feed-addon.php:1328
#: includes/blocks/config/class-gf-blocks-config.php:167
#: includes/class-confirmation.php:521
#: includes/fields/class-gf-field-captcha.php:165
#: includes/fields/class-gf-field-captcha.php:483
#: includes/fields/class-gf-field-fileupload.php:799
#: includes/fields/class-gf-field-post-image.php:214
#: includes/license/class-gf-license-statuses.php:48
#: includes/license/class-gf-license-statuses.php:58
#: includes/license/class-gf-license-statuses.php:66
#: includes/license/class-gf-license-statuses.php:74
#: includes/license/class-gf-license-statuses.php:80
#: includes/license/class-gf-license-statuses.php:86
#: includes/logging/logging.php:440
#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:196
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:101
#: includes/splash-page/gf_splash.php:20
#: includes/splash-page/gf_splash.php:43
#: includes/splash-page/gf_splash.php:72
#: includes/splash-page/gf_splash.php:96
#: includes/splash-page/gf_splash.php:125
#: includes/splash-page/gf_splash.php:137
#: includes/system-status/class-gf-system-report.php:1157
#: includes/template-library/config/class-gf-template-library-config.php:119
#: includes/webapi/webapi.php:572
#: includes/webapi/webapi.php:589
#: js.php:254
#: js.php:258
#: js.php:459
#: js.php:1713
#: js.php:1717
#: js.php:1762
#: js.php:1766
#: notification.php:189
#: settings.php:588
#: settings.php:704
#: settings.php:738
#: settings.php:872
#: settings.php:1077
#: tooltips.php:41
#: tooltips.php:97
#: tooltips.php:106
#: assets/js/src/admin/block-editor/blocks/form/edit.js:390
msgid "(opens in a new tab)"
msgstr ""

#: common.php:4224
msgid "There was an problem while verifying your file."
msgstr ""

#: common.php:4229
msgid "Sorry, this file extension is not permitted for security reasons."
msgstr ""

#: common.php:4232
msgid "Sorry, this file type is not permitted for security reasons."
msgstr ""

#: common.php:4689
msgid "Akismet Spam Filter"
msgstr ""

#: common.php:5605
msgid "New row added."
msgstr ""

#: common.php:5606
msgid "Row removed"
msgstr ""

#: common.php:5607
msgid "The form has been saved.  The content contains the link to return and complete the form."
msgstr ""

#: common.php:5621
#: common.php:6430
#: form_list.php:146
#: form_list.php:756
#: gravityforms.php:2008
#: includes/addon/class-gf-feed-addon.php:2880
#: includes/class-confirmation.php:156
#: includes/class-confirmation.php:1058
#: includes/config/items/class-gf-config-admin.php:43
#: includes/license/class-gf-license-api-response.php:157
#: js.php:306
#: js.php:408
#: notification.php:915
#: notification.php:1497
msgid "Active"
msgstr ""

#: common.php:5622
#: form_list.php:142
#: form_list.php:759
#: gravityforms.php:2004
#: includes/addon/class-gf-feed-addon.php:2883
#: includes/class-confirmation.php:152
#: includes/class-confirmation.php:1061
#: includes/config/items/class-gf-config-admin.php:42
#: js.php:306
#: js.php:410
#: notification.php:911
#: notification.php:1500
msgid "Inactive"
msgstr ""

#: common.php:5623
#: common.php:5676
#: form_detail.php:1686
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:44
#: select_columns.php:276
msgid "Save"
msgstr ""

#: common.php:5624
#: entry_detail.php:1392
#: gravityforms.php:4990
#: includes/webapi/webapi.php:448
#: includes/webapi/webapi.php:541
#: includes/webapi/webapi.php:628
msgid "Update"
msgstr ""

#: common.php:5625
#: form_display.php:347
#: form_display.php:4287
#: form_display.php:5785
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:51
#: includes/template-library/templates/templates.php:2249
#: includes/template-library/templates/templates.php:2445
#: includes/template-library/templates/templates.php:2832
#: includes/template-library/templates/templates.php:3066
#: includes/template-library/templates/templates.php:3262
#: includes/template-library/templates/templates.php:3642
#: includes/template-library/templates/templates.php:3872
#: includes/template-library/templates/templates.php:4068
#: includes/template-library/templates/templates.php:4475
#: includes/template-library/templates/templates.php:5005
#: includes/template-library/templates/templates.php:5364
#: includes/template-library/templates/templates.php:5604
#: includes/template-library/templates/templates.php:6068
#: includes/template-library/templates/templates.php:6399
#: includes/template-library/templates/templates.php:6631
#: includes/template-library/templates/templates.php:7217
#: includes/template-library/templates/templates.php:7407
#: js.php:688
msgid "Previous"
msgstr ""

#: common.php:5626
msgid "Select a format"
msgstr ""

#: common.php:5627
msgid "Column"
msgstr ""

#: common.php:5628
msgid "5 of %d items shown. Edit field to view all"
msgstr ""

#: common.php:5629
#: export.php:656
#: export.php:779
#: includes/fields/class-gf-field-checkbox.php:127
#: includes/fields/class-gf-field-checkbox.php:204
#: includes/fields/class-gf-field-checkbox.php:934
#: includes/fields/class-gf-field-multiple-choice.php:69
#: js.php:758
msgid "Select All"
msgstr ""

#: common.php:5630
msgid "Enter a value"
msgstr ""

#: common.php:5631
#: preview.php:43
msgid "Untitled Form"
msgstr ""

#: common.php:5632
#: includes/template-library/templates/templates.php:340
msgid "We would love to hear from you! Please fill out this form and we will get in touch with you shortly."
msgstr ""

#: common.php:5633
#: common.php:5684
#: forms_model.php:7195
msgid "Thanks for contacting us! We will get in touch with you shortly."
msgstr ""

#: common.php:5634
#: forms_model.php:1037
#: form_display.php:1814
#: form_list.php:322
#: gravityforms.php:3508
#: includes/fields/class-gf-field-submit.php:159
#: includes/settings/fields/class-button.php:50
#: includes/template-library/templates/templates.php:22
#: includes/template-library/templates/templates.php:344
#: includes/template-library/templates/templates.php:1632
#: includes/template-library/templates/templates.php:2062
#: includes/template-library/templates/templates.php:2916
#: includes/template-library/templates/templates.php:3722
#: includes/template-library/templates/templates.php:5691
#: includes/template-library/templates/templates.php:7796
#: includes/template-library/templates/templates.php:8294
#: includes/template-library/templates/templates.php:9333
msgid "Submit"
msgstr ""

#: common.php:5635
msgid "The submit button for this form"
msgstr ""

#: common.php:5636
#: includes/settings/fields/class-notification-routing.php:350
msgid "Loading..."
msgstr ""

#: common.php:5637
msgid "this field if"
msgstr ""

#: common.php:5638
msgid "this section if"
msgstr ""

#: common.php:5639
msgid "this page if"
msgstr ""

#: common.php:5640
msgid "this form button if"
msgstr ""

#: common.php:5641
#: js.php:294
msgid "Show"
msgstr ""

#: common.php:5642
msgid "Hide"
msgstr ""

#: common.php:5643
#: includes/logging/logging.php:365
#: includes/settings/fields/class-checkbox-and-select.php:50
msgid "Enable"
msgstr ""

#: common.php:5644
msgid "Disable"
msgstr ""

#: common.php:5645
#: includes/addon/class-gf-payment-addon.php:2756
#: includes/addon/class-gf-payment-addon.php:2801
#: includes/webapi/webapi.php:561
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Enabled"
msgstr ""

#: common.php:5646
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Disabled"
msgstr ""

#: common.php:5647
msgid "Configure"
msgstr ""

#: common.php:5648
#: export.php:906
#: includes/addon/class-gf-payment-addon.php:2694
#: includes/addon/class-gf-payment-addon.php:2696
#: tooltips.php:104
#: tooltips.php:122
#: tooltips.php:145
msgid "Conditional Logic"
msgstr ""

#: common.php:5649
msgid "Conditional logic allows you to change what the user sees depending on the fields they select."
msgstr ""

#: common.php:5654
msgid "Adding conditional logic to the form submit button could cause usability problems for some users and negatively impact the accessibility of your form. Learn more about button conditional logic in our %1$sdocumentation%2$s."
msgstr ""

#: common.php:5658
#: includes/class-confirmation.php:235
#: includes/class-confirmation.php:285
#: includes/class-confirmation.php:1188
#: includes/embed-form/config/class-gf-embed-config.php:179
#: includes/fields/class-gf-field-page.php:12
#: js.php:90
msgid "Page"
msgstr ""

#: common.php:5659
#: form_detail.php:781
msgid "Next Button"
msgstr ""

#: common.php:5660
msgid "Submit Button"
msgstr ""

#: common.php:5661
msgctxt "Conditional Logic"
msgid "All"
msgstr ""

#: common.php:5662
msgctxt "Conditional Logic"
msgid "Any"
msgstr ""

#: common.php:5663
msgid "of the following match:"
msgstr ""

#: common.php:5664
#: includes/addon/class-gf-addon.php:4233
#: includes/settings/fields/class-notification-routing.php:76
#: includes/settings/fields/class-notification-routing.php:227
msgid "is"
msgstr ""

#: common.php:5665
#: includes/addon/class-gf-addon.php:4237
#: includes/settings/fields/class-notification-routing.php:77
#: includes/settings/fields/class-notification-routing.php:228
msgid "is not"
msgstr ""

#: common.php:5666
#: includes/addon/class-gf-addon.php:4241
#: includes/settings/fields/class-notification-routing.php:78
#: includes/settings/fields/class-notification-routing.php:229
msgid "greater than"
msgstr ""

#: common.php:5667
#: includes/addon/class-gf-addon.php:4245
#: includes/settings/fields/class-notification-routing.php:79
#: includes/settings/fields/class-notification-routing.php:230
msgid "less than"
msgstr ""

#: common.php:5668
#: includes/addon/class-gf-addon.php:4249
#: includes/settings/fields/class-notification-routing.php:80
#: includes/settings/fields/class-notification-routing.php:231
msgid "contains"
msgstr ""

#: common.php:5669
#: includes/addon/class-gf-addon.php:4253
#: includes/settings/fields/class-notification-routing.php:81
#: includes/settings/fields/class-notification-routing.php:232
msgid "starts with"
msgstr ""

#: common.php:5670
#: includes/addon/class-gf-addon.php:4257
#: includes/settings/fields/class-notification-routing.php:82
#: includes/settings/fields/class-notification-routing.php:233
msgid "ends with"
msgstr ""

#: common.php:5671
msgid "Empty (no choices selected)"
msgstr ""

#: common.php:5673
msgid "This form has legacy markup enabled and doesn’t support field resizing within the editor. Please disable legacy markup in the form settings to enable live resizing."
msgstr ""

#: common.php:5674
msgid "Use this confirmation if"
msgstr ""

#: common.php:5675
msgid "Send this notification if"
msgstr ""

#: common.php:5677
msgid "Saving..."
msgstr ""

#: common.php:5678
msgid "Are you sure you wish to cancel these changes?"
msgstr ""

#: common.php:5679
msgid "There was an issue saving this confirmation."
msgstr ""

#: common.php:5680
msgid "Are you sure you wish to delete this confirmation?"
msgstr ""

#: common.php:5681
#: form_settings.php:1303
msgid "There was an issue deleting this confirmation."
msgstr ""

#: common.php:5682
msgid "There are unsaved changes to the current confirmation. Would you like to discard these changes?"
msgstr ""

#: common.php:5683
msgid "Untitled Confirmation"
msgstr ""

#: common.php:5685
msgid "Please select a page."
msgstr ""

#: common.php:5686
msgid "Please enter a URL."
msgstr ""

#: common.php:5687
msgid "Please enter a confirmation name."
msgstr ""

#: common.php:5688
msgid "Deleting this field will also delete all entry data associated with it. 'Cancel' to abort. 'OK' to delete."
msgstr ""

#: common.php:5689
msgid "You're about to delete this field. 'Cancel' to stop. 'OK' to delete"
msgstr ""

#: common.php:5691
#: includes/addon/class-gf-addon.php:5202
msgid "Warning"
msgstr ""

#: common.php:5693
msgid "This form contains {type} conditional logic dependent upon this field. Deleting this field will deactivate those conditional logic rules and also delete all entry data associated with the field. 'Cancel' to abort. 'OK' to delete."
msgstr ""

#: common.php:5694
msgid "This form contains {type} conditional logic dependent upon this choice. Are you sure you want to delete this choice? 'Cancel' to abort. 'OK' to delete."
msgstr ""

#: common.php:5695
msgid "This form contains {type} conditional logic dependent upon this choice. Are you sure you want to modify this choice? 'Cancel' to abort. 'OK' to continue."
msgstr ""

#: common.php:5696
msgid "This form contains {type} conditional logic dependent upon this field. Are you sure you want to mark this field as Administrative? 'Cancel' to abort. 'OK' to continue."
msgstr ""

#: common.php:5697
msgid "This form contains conditional logic dependent upon this field. This will no longer work if the Rich Text Editor is enabled.  Are you sure you want to enable the Rich Text Editor?  'Cancel' to abort. 'OK' to continue."
msgstr ""

#: common.php:5698
msgid "button"
msgstr ""

#: common.php:5699
msgid "confirmation"
msgstr ""

#: common.php:5700
msgid "notification"
msgstr ""

#: common.php:5701
msgid "notification routing"
msgstr ""

#: common.php:5702
msgid "field"
msgstr ""

#: common.php:5703
#: includes/addon/class-gf-feed-addon.php:2786
msgid "feed"
msgstr ""

#: common.php:5704
msgid "Conditional Logic Warning"
msgstr ""

#: common.php:5707
#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:27
msgid "Insert Merge Tags"
msgstr ""

#: common.php:5716
msgid "Add a condition"
msgstr ""

#: common.php:5717
msgid "Remove a condition"
msgstr ""

#: common.php:5718
msgid "{0} of the following match:"
msgstr ""

#: common.php:5720
msgid "Custom Choices"
msgstr ""

#: common.php:5721
msgid "Predefined Choices"
msgstr ""

#. translators: {field_title} and {field_type} should not be translated , they are variables
#: common.php:5724
msgid "{field_label} - {field_type}, jump to this field's settings"
msgstr ""

#: common.php:5726
msgid "Field Limit"
msgstr ""

#: common.php:5727
msgid "A form can only contain one CAPTCHA field."
msgstr ""

#: common.php:5728
msgid "A form can only contain one Shipping field."
msgstr ""

#: common.php:5729
msgid "A form can only contain one Post Body field."
msgstr ""

#: common.php:5730
msgid "A form can only contain one Post Title field."
msgstr ""

#: common.php:5731
msgid "A form can only contain one Post Excerpt field."
msgstr ""

#: common.php:5732
msgid "A form can only contain one Credit Card field."
msgstr ""

#: common.php:5734
#: common.php:5759
msgid "Missing Product field"
msgstr ""

#: common.php:5735
msgid "You must add a Product field to the form first."
msgstr ""

#: common.php:5737
msgid "Unsupported Markup"
msgstr ""

#: common.php:5738
msgid "You cannot add a Multiple Choice field to a form that uses legacy markup. Please edit the form settings and turn off Legacy Markup."
msgstr ""

#: common.php:5739
msgid "You cannot add an Image Choice field to a form that uses legacy markup. Please edit the form settings and turn off Legacy Markup."
msgstr ""

#: common.php:5741
#: export.php:532
#: gravityforms.php:6160
#: includes/locking/class-gf-locking.php:209
msgid "Error"
msgid_plural "Errors"
msgstr[0] ""
msgstr[1] ""

#: common.php:5742
msgid "Ajax error while adding field. Please refresh the page and try again."
msgstr ""

#: common.php:5743
msgid "Ajax error while changing input type. Please refresh the page and try again."
msgstr ""

#: common.php:5745
msgid "Missing Name"
msgstr ""

#: common.php:5746
msgid "Please give this custom choice a name."
msgstr ""

#: common.php:5747
msgid "Duplicate Name"
msgstr ""

#: common.php:5748
msgid "This custom choice name is already in use. Please enter another name."
msgstr ""

#: common.php:5750
msgid "Duplicate Title"
msgstr ""

#: common.php:5751
msgid "The form title you have entered is already taken. Please enter a unique form title."
msgstr ""

#: common.php:5753
msgid "Missing Form Title"
msgstr ""

#: common.php:5754
msgid "Please enter a Title for this form. When adding the form to a page or post, you will have the option to hide the title."
msgstr ""

#: common.php:5755
msgid "Empty Page"
msgstr ""

#: common.php:5756
msgid "This form currently has one or more pages without any fields. Blank pages are a result of Page Breaks that are positioned as the first or last field in the form or right after each other. Please adjust the Page Breaks."
msgstr ""

#: common.php:5757
msgid "Missing Product Label"
msgstr ""

#: common.php:5758
msgid "This form has a Product field with a blank label. Please enter a label for every Product field."
msgstr ""

#: common.php:5760
msgid "This form has an Option field without a Product field. You must add a Product field to your form."
msgstr ""

#: common.php:5762
#: includes/webapi/webapi.php:1537
#: includes/webapi/webapi.php:1568
msgid "Success"
msgstr ""

#: common.php:5763
msgid "The formula appears to be valid."
msgstr ""

#: common.php:5764
msgid "There appears to be a problem with the formula."
msgstr ""

#: common.php:5766
msgid "Confirm"
msgstr ""

#: common.php:5767
msgid "You are about to move this form to the trash. 'Cancel' to abort. 'OK' to delete."
msgstr ""

#: common.php:5768
msgid "Delete this custom choice list? 'Cancel' to abort. 'OK' to delete."
msgstr ""

#: common.php:5770
msgid "field added to form"
msgstr ""

#: common.php:5782
msgid "ID: "
msgstr ""

#. Translators: This string is a list of name prefixes/honorifics.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few prefixes as your language has.
#: common.php:5789
msgid "Mr., Mrs., Miss, Ms., Mx., Dr., Prof., Rev."
msgstr ""

#: common.php:5816
#: js.php:73
msgid "To use conditional logic, please create a field that supports conditional logic."
msgstr ""

#: common.php:5819
msgid "add another rule"
msgstr ""

#: common.php:5820
msgid "remove this rule"
msgstr ""

#: common.php:6252
msgid "Any form field"
msgstr ""

#: common.php:6359
#: includes/addon/class-gf-addon.php:3190
#: includes/settings/fields/class-generic-map.php:534
#: includes/settings/fields/class-generic-map.php:589
msgid "Entry ID"
msgstr ""

#: common.php:6363
#: export.php:1344
#: forms_model.php:6788
#: includes/addon/class-gf-addon.php:3191
#: includes/settings/fields/class-generic-map.php:538
#: includes/settings/fields/class-generic-map.php:582
#: select_columns.php:197
msgid "Entry Date"
msgstr ""

#: common.php:6365
#: common.php:6398
#: includes/fields/class-gf-field-date.php:908
#: includes/fields/class-gf-field-date.php:1102
msgid "yyyy-mm-dd"
msgstr ""

#: common.php:6369
msgid "Starred"
msgstr ""

#: common.php:6383
#: includes/class-personal-data.php:684
#: tooltips.php:168
msgid "IP Address"
msgstr ""

#: common.php:6387
msgid "Source URL"
msgstr ""

#: common.php:6391
#: export.php:1350
#: forms_model.php:6794
#: select_columns.php:200
msgid "Payment Status"
msgstr ""

#: common.php:6396
#: export.php:1349
#: forms_model.php:6800
#: select_columns.php:203
msgid "Payment Date"
msgstr ""

#: common.php:6402
#: export.php:1348
#: forms_model.php:6803
#: includes/addon/class-gf-payment-addon.php:2650
#: includes/addon/class-gf-payment-addon.php:2655
#: select_columns.php:202
msgid "Payment Amount"
msgstr ""

#: common.php:6406
msgid "Transaction ID"
msgstr ""

#: common.php:6410
#: entry_detail.php:1314
#: forms_model.php:6806
#: includes/webapi/includes/class-gf-api-keys-table.php:28
#: includes/webapi/webapi.php:403
#: select_columns.php:204
msgid "User"
msgstr ""

#: common.php:6426
msgid "Authorized"
msgstr ""

#: common.php:6427
msgid "Paid"
msgstr ""

#: common.php:6428
msgid "Processing"
msgstr ""

#: common.php:6429
msgid "Failed"
msgstr ""

#: common.php:6431
#: includes/config/items/class-gf-config-multifile.php:36
msgid "Cancelled"
msgstr ""

#: common.php:6432
#: includes/locking/class-gf-locking.php:206
msgid "Pending"
msgstr ""

#: common.php:6433
msgid "Refunded"
msgstr ""

#: common.php:6434
msgid "Voided"
msgstr ""

#: common.php:7303
#: form_detail.php:2247
msgid "Visible"
msgstr ""

#: common.php:7305
msgid "Default option. The field is visible when viewing the form."
msgstr ""

#: common.php:7308
#: form_detail.php:678
#: form_detail.php:760
#: form_detail.php:868
#: form_detail.php:1408
#: form_detail.php:2212
#: form_detail.php:2248
#: form_detail.php:2269
#: includes/fields/class-gf-field-hidden.php:13
msgid "Hidden"
msgstr ""

#: common.php:7310
msgid "The field is hidden when viewing the form. Useful when you require the functionality of this field but do not want the user to be able to see this field."
msgstr ""

#: common.php:7313
msgid "Administrative"
msgstr ""

#: common.php:7315
msgid "The field is only visible when administering submitted entries. The field is not visible or functional when viewing the form."
msgstr ""

#: common.php:7346
#: form_detail.php:2628
msgid "Visibility"
msgstr ""

#: common.php:7346
msgid "Select the visibility for this field."
msgstr ""

#: currency.php:153
msgid "U.S. Dollar"
msgstr ""

#: currency.php:163
msgid "Pound Sterling"
msgstr ""

#: currency.php:173
msgid "Euro"
msgstr ""

#: currency.php:183
msgid "Australian Dollar"
msgstr ""

#: currency.php:193
msgid "Brazilian Real"
msgstr ""

#: currency.php:203
msgid "Canadian Dollar"
msgstr ""

#: currency.php:213
msgid "Czech Koruna"
msgstr ""

#: currency.php:223
msgid "Danish Krone"
msgstr ""

#: currency.php:233
msgid "Hong Kong Dollar"
msgstr ""

#: currency.php:243
msgid "Hungarian Forint"
msgstr ""

#: currency.php:253
msgid "Israeli New Sheqel"
msgstr ""

#: currency.php:263
msgid "Japanese Yen"
msgstr ""

#: currency.php:273
msgid "Malaysian Ringgit"
msgstr ""

#: currency.php:283
msgid "Mexican Peso"
msgstr ""

#: currency.php:293
msgid "Norwegian Krone"
msgstr ""

#: currency.php:303
msgid "New Zealand Dollar"
msgstr ""

#: currency.php:313
msgid "Philippine Peso"
msgstr ""

#: currency.php:323
msgid "Polish Zloty"
msgstr ""

#: currency.php:333
msgid "Russian Ruble"
msgstr ""

#: currency.php:343
msgid "Singapore Dollar"
msgstr ""

#: currency.php:353
msgid "South African Rand"
msgstr ""

#: currency.php:363
msgid "Swedish Krona"
msgstr ""

#: currency.php:373
msgid "Swiss Franc"
msgstr ""

#: currency.php:384
msgid "Taiwan New Dollar"
msgstr ""

#: currency.php:394
msgid "Thai Baht"
msgstr ""

#: currency.php:448
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:84
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:28
msgid "Select a Currency"
msgstr ""

#: entry_detail.php:44
#: entry_detail.php:623
msgid "Entry"
msgstr ""

#: entry_detail.php:52
#: form_settings.php:1207
#: gravityforms.php:2098
#: notification.php:111
#: notification.php:231
#: notification.php:854
msgid "Notifications"
msgstr ""

#: entry_detail.php:60
#: entry_detail.php:834
msgid "Notes"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1116
msgid "Subscription Details"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1116
#: includes/template-library/templates/templates.php:6429
msgid "Payment Details"
msgstr ""

#: entry_detail.php:75
msgid "Print entry"
msgstr ""

#: entry_detail.php:240
msgid "Oops! We couldn't find your entry. Please try again"
msgstr ""

#: entry_detail.php:319
msgid "%s: Unchecked \"%s\""
msgstr ""

#: entry_detail.php:319
msgid "%s: Checked \"%s\""
msgstr ""

#: entry_detail.php:384
msgid "You don't have adequate permission to delete notes."
msgstr ""

#: entry_detail.php:393
msgid "You don't have adequate permission to trash entries."
msgstr ""

#: entry_detail.php:407
msgid "You don't have adequate permission to restore entries."
msgstr ""

#: entry_detail.php:431
#: entry_list.php:1446
#: entry_list.php:1486
#: form_list.php:1032
msgid "You don't have adequate permission to delete entries."
msgstr ""

#: entry_detail.php:460
msgid "Would you like to delete this file? 'Cancel' to stop. 'OK' to delete"
msgstr ""

#: entry_detail.php:471
msgid "Ajax error while deleting field."
msgstr ""

#: entry_detail.php:537
#: entry_list.php:1791
msgid "You must select at least one type of notification to resend."
msgstr ""

#: entry_detail.php:556
msgid "Notifications were resent successfully."
msgstr ""

#: entry_detail.php:600
msgid "Entry Updated."
msgstr ""

#: entry_detail.php:725
msgid "Details"
msgstr ""

#: entry_detail.php:810
msgid " Bulk action"
msgstr ""

#: entry_detail.php:812
msgid " Bulk action "
msgstr ""

#: entry_detail.php:813
#: form_detail.php:1688
#: includes/addon/class-gf-feed-addon.php:2053
#: includes/addon/class-gf-feed-addon.php:2100
#: includes/class-confirmation.php:1093
#: includes/editor-button/config/class-gf-editor-config.php:56
#: includes/fields/class-gf-field.php:1740
#: includes/settings/config/class-gf-settings-config-i18n.php:37
#: includes/settings/config/class-gf-settings-config-i18n.php:61
#: js.php:1594
#: notification.php:1541
msgid "Delete"
msgstr ""

#: entry_detail.php:816
#: entry_list.php:222
#: form_list.php:406
#: includes/settings/config/class-gf-settings-config-i18n.php:45
msgid "Apply"
msgstr ""

#: entry_detail.php:886
msgid "added"
msgstr ""

#: entry_detail.php:903
msgid "Add Note"
msgstr ""

#: entry_detail.php:917
msgid "Also email this note to"
msgstr ""

#: entry_detail.php:925
msgid "Subject:"
msgstr ""

#: entry_detail.php:955
#: print-entry.php:185
msgid "Entry # "
msgstr ""

#: entry_detail.php:973
msgid "show empty fields"
msgstr ""

#: entry_detail.php:1142
#: form_list.php:439
#: form_list.php:683
msgid "Status"
msgstr ""

#: entry_detail.php:1158
msgid "Start Date"
msgstr ""

#: entry_detail.php:1175
msgid "Subscription Id"
msgstr ""

#: entry_detail.php:1175
#: export.php:1347
#: forms_model.php:6797
#: select_columns.php:201
msgid "Transaction Id"
msgstr ""

#: entry_detail.php:1193
#: includes/addon/class-gf-payment-addon.php:2602
#: includes/addon/class-gf-payment-addon.php:2606
msgid "Recurring Amount"
msgstr ""

#: entry_detail.php:1193
#: includes/addon/class-gf-payment-addon.php:2479
msgid "Amount"
msgstr ""

#: entry_detail.php:1255
msgid "Include Notes"
msgstr ""

#: entry_detail.php:1259
#: entry_list.php:1364
#: entry_list.php:2207
msgid "Print"
msgstr ""

#: entry_detail.php:1299
msgid "Submitted on"
msgstr ""

#: entry_detail.php:1303
msgid "Updated"
msgstr ""

#: entry_detail.php:1308
#: export.php:1353
#: forms_model.php:6785
#: includes/addon/class-gf-addon.php:3192
#: includes/settings/fields/class-generic-map.php:542
#: select_columns.php:198
msgid "User IP"
msgstr ""

#: entry_detail.php:1320
msgid "Embed Url"
msgstr ""

#: entry_detail.php:1327
msgid "Edit Post"
msgstr ""

#: entry_detail.php:1350
#: entry_list.php:1227
#: entry_list.php:1349
msgid "Not Spam"
msgstr ""

#: entry_detail.php:1356
#: entry_detail.php:1367
msgid "You are about to delete this entry. 'Cancel' to stop, 'OK' to delete."
msgstr ""

#: entry_detail.php:1356
#: entry_detail.php:1367
#: entry_list.php:1206
#: entry_list.php:1231
#: entry_list.php:1344
#: entry_list.php:1352
msgid "Delete Permanently"
msgstr ""

#: entry_detail.php:1365
#: entry_list.php:1204
#: entry_list.php:1343
#: form_list.php:664
#: form_list.php:822
msgid "Restore"
msgstr ""

#: entry_detail.php:1376
msgid "Move to Trash"
msgstr ""

#: entry_detail.php:1382
#: entry_list.php:1258
msgid "Mark as Spam"
msgstr ""

#: entry_detail.php:1392
#: gravityforms.php:5607
#: gravityforms.php:5885
#: includes/addon/class-gf-feed-addon.php:2098
#: includes/class-confirmation.php:1091
#: includes/webapi/includes/class-gf-api-keys-table.php:68
#: notification.php:1539
msgid "Edit"
msgstr ""

#: entry_detail.php:1406
#: form_detail.php:1675
#: form_detail.php:1687
#: includes/addon/class-gf-results.php:308
#: includes/blocks/config/class-gf-blocks-config.php:107
#: includes/config/items/class-gf-config-multifile.php:34
#: includes/embed-form/config/class-gf-embed-config-i18n.php:54
#: includes/form-editor/dialog-alert/config/class-gf-dialog-config-i18n.php:32
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:42
#: includes/locking/class-gf-locking.php:201
#: includes/template-library/config/class-gf-template-library-config.php:105
#: select_columns.php:277
msgid "Cancel"
msgstr ""

#: entry_detail.php:1434
msgid "You cannot resend notifications for this entry because this form does not currently have any notifications configured."
msgstr ""

#: entry_detail.php:1436
#: entry_list.php:2144
msgid "Configure Notifications"
msgstr ""

#: entry_detail.php:1452
#: entry_list.php:2164
#: notification.php:206
msgid "Send To"
msgstr ""

#: entry_detail.php:1458
msgid "Resend"
msgstr ""

#: entry_detail.php:1460
#: entry_list.php:2171
msgid "Resending..."
msgstr ""

#: entry_list.php:47
#: entry_list.php:1504
msgid "%s restored from the Trash."
msgstr ""

#: entry_list.php:48
#: entry_list.php:62
#: entry_list.php:1476
msgid "1 entry"
msgstr ""

#: entry_list.php:61
msgid "%s permanently deleted."
msgstr ""

#: entry_list.php:79
msgid "1 entry moved to the Trash. %sUndo%s"
msgstr ""

#: entry_list.php:91
msgid "You don't have any active forms. Let's go %screate one%s"
msgstr ""

#: entry_list.php:216
msgid "Default Filter"
msgstr ""

#: entry_list.php:217
#: form_list.php:401
msgid "Pagination"
msgstr ""

#: entry_list.php:218
msgid "Number of entries per page:"
msgstr ""

#: entry_list.php:219
msgid "Display Mode"
msgstr ""

#: entry_list.php:261
#: form_detail.php:1916
msgid "Standard"
msgstr ""

#: entry_list.php:265
msgid "Full Width"
msgstr ""

#: entry_list.php:337
#: help.php:52
msgid "Search"
msgstr ""

#: entry_list.php:471
msgctxt "Entry List"
msgid "All"
msgstr ""

#: entry_list.php:479
msgctxt "Entry List"
msgid "Unread"
msgstr ""

#: entry_list.php:487
msgctxt "Entry List"
msgid "Starred"
msgstr ""

#: entry_list.php:495
#: entry_list.php:1367
msgid "Spam"
msgstr ""

#: entry_list.php:502
#: entry_list.php:1264
#: entry_list.php:1370
#: form_list.php:857
#: gravityforms.php:2012
msgid "Trash"
msgstr ""

#: entry_list.php:886
msgid "Click to select columns to display"
msgstr ""

#: entry_list.php:887
msgid "Select Entry Table Columns"
msgstr ""

#: entry_list.php:1028
msgid "View entry number %s"
msgstr ""

#: entry_list.php:1149
msgid "This form does not have any unread entries matching the search criteria."
msgstr ""

#: entry_list.php:1149
msgid "This form does not have any unread entries."
msgstr ""

#: entry_list.php:1153
msgid "This form does not have any starred entries matching the search criteria."
msgstr ""

#: entry_list.php:1153
msgid "This form does not have any starred entries."
msgstr ""

#: entry_list.php:1157
msgid "This form does not have any spam."
msgstr ""

#: entry_list.php:1161
msgid "This form does not have any entries in the trash matching the search criteria."
msgstr ""

#: entry_list.php:1161
msgid "This form does not have any entries in the trash."
msgstr ""

#: entry_list.php:1165
msgid "This form does not have any entries matching the search criteria."
msgstr ""

#: entry_list.php:1165
msgid "This form does not have any entries yet."
msgstr ""

#: entry_list.php:1199
#: entry_list.php:1222
#: entry_list.php:1247
#: includes/addon/class-gf-payment-addon.php:3453
#: includes/addon/class-gf-payment-addon.php:3454
msgid "View"
msgstr ""

#: entry_list.php:1227
msgid "Mark this entry as not spam"
msgstr ""

#: entry_list.php:1252
msgid "Mark read"
msgstr ""

#: entry_list.php:1252
msgid "Mark this entry as unread"
msgstr ""

#: entry_list.php:1252
msgid "Mark unread"
msgstr ""

#: entry_list.php:1358
msgid "Mark as Read"
msgstr ""

#: entry_list.php:1359
msgid "Mark as Unread"
msgstr ""

#: entry_list.php:1360
msgid "Add Star"
msgstr ""

#: entry_list.php:1361
msgid "Remove Star"
msgstr ""

#: entry_list.php:1363
#: entry_list.php:1748
#: entry_list.php:2102
#: entry_list.php:2169
msgid "Resend Notifications"
msgstr ""

#: entry_list.php:1403
msgid "WARNING! This operation cannot be undone. Empty trash? 'Ok' to empty trash. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1403
msgid "WARNING! This operation cannot be undone. Permanently delete all spam? 'Ok' to delete. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1404
msgid "Empty Trash"
msgstr ""

#: entry_list.php:1404
msgid "Delete All Spam"
msgstr ""

#: entry_list.php:1444
msgid "Entry deleted."
msgstr ""

#: entry_list.php:1476
msgid "%d entries"
msgstr ""

#: entry_list.php:1484
msgid "%s deleted."
msgstr ""

#: entry_list.php:1494
msgid "%s moved to Trash."
msgstr ""

#: entry_list.php:1496
msgid "You don't have adequate permissions to trash entries."
msgstr ""

#: entry_list.php:1506
msgid "You don't have adequate permissions to restore entries."
msgstr ""

#: entry_list.php:1513
msgid "%s restored from the spam."
msgstr ""

#: entry_list.php:1518
msgid "%s marked as spam."
msgstr ""

#: entry_list.php:1523
msgid "%s marked as read."
msgstr ""

#: entry_list.php:1528
msgid "%s marked as unread."
msgstr ""

#: entry_list.php:1533
msgid "%s starred."
msgstr ""

#: entry_list.php:1538
msgid "%s unstarred."
msgstr ""

#: entry_list.php:1683
msgid "Ajax error while setting entry property"
msgstr ""

#: entry_list.php:1740
msgid "Please select at least one entry."
msgstr ""

#: entry_list.php:1754
#: entry_list.php:2108
msgid "Print Entries"
msgstr ""

#: entry_list.php:1816
msgid "Notifications for %s were resent successfully."
msgstr ""

#: entry_list.php:1818
msgid "entry"
msgstr ""

#: entry_list.php:1818
msgid "entries"
msgstr ""

#: entry_list.php:1938
msgid "All %s{0}%s entries on this page are selected."
msgstr ""

#: entry_list.php:1939
msgid "Select all %s{0}%s entries."
msgstr ""

#: entry_list.php:1940
msgid "All %s{0}%s entries have been selected."
msgstr ""

#: entry_list.php:1941
msgid "Clear selection"
msgstr ""

#: entry_list.php:2029
msgid "Entry List"
msgstr ""

#: entry_list.php:2094
msgid "Please select at least one entry..."
msgstr ""

#: entry_list.php:2142
msgid "You cannot resend notifications for these entries because this form does not currently have any notifications configured."
msgstr ""

#: entry_list.php:2148
msgid "Specify which notifications you would like to resend for the selected entries."
msgstr ""

#: entry_list.php:2162
msgid "You may override the default notification settings by entering a comma delimited list of emails to which the selected notifications should be sent."
msgstr ""

#: entry_list.php:2180
msgid "Close Window"
msgstr ""

#: entry_list.php:2195
msgid "Print all of the selected entries at once."
msgstr ""

#: entry_list.php:2199
msgid "Include notes"
msgstr ""

#: entry_list.php:2204
msgid "Add page break between entries"
msgstr ""

#: export.php:19
msgid "Please select the forms to be exported"
msgstr ""

#: export.php:477
msgid "Forms could not be imported. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool."
msgstr ""

#: export.php:483
msgid "Forms could not be imported. Your export file is not compatible with your current version of Gravity Forms."
msgstr ""

#: export.php:543
#: export.php:549
msgid "form"
msgid_plural "forms"
msgstr[0] ""
msgstr[1] ""

#: export.php:548
msgid "View imported forms."
msgstr ""

#: export.php:549
msgid "Gravity Forms imported %d %s successfully"
msgstr ""

#: export.php:551
msgid "Edit form."
msgstr ""

#: export.php:552
msgid "Gravity Forms imported %d form successfully"
msgstr ""

#: export.php:569
#: export.php:1410
msgid "Import Forms"
msgstr ""

#: export.php:574
msgid "Select the Gravity Forms export files you would like to import. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool. When you click the import button below, Gravity Forms will import the forms."
msgstr ""

#: export.php:584
#: tooltips.php:148
msgid "Select Files"
msgstr ""

#: export.php:590
#: tooltips.php:149
msgid "Import Images"
msgstr ""

#: export.php:592
msgid "Import images used in this form into your media library."
msgstr ""

#: export.php:596
msgid "Import"
msgstr ""

#: export.php:642
#: export.php:1402
msgid "Export Forms"
msgstr ""

#: export.php:645
msgid "Select the forms you would like to export. When you click the download button below, Gravity Forms will create a JSON file for you to save to your computer. Once you've saved the download file, you can use the Import tool to import the forms."
msgstr ""

#: export.php:650
msgid "Select Forms"
msgstr ""

#: export.php:656
#: export.php:779
#: includes/fields/class-gf-field-checkbox.php:215
#: includes/fields/class-gf-field-checkbox.php:945
msgid "Deselect All"
msgstr ""

#: export.php:686
#: export.php:939
msgid "Download Export File"
msgstr ""

#: export.php:764
msgid "Ajax error while selecting a form"
msgstr ""

#: export.php:788
msgid "Export entries if {0} of the following match:"
msgstr ""

#: export.php:797
msgid "Please select the fields to be exported"
msgstr ""

#: export.php:858
#: export.php:1393
msgid "Export Entries"
msgstr ""

#: export.php:861
msgid "Select a form below to export entries. Once you have selected a form you may select the fields you would like to export and then define optional filters for field values and the date range. When you click the download button below, Gravity Forms will create a CSV file for you to save to your computer."
msgstr ""

#: export.php:867
#: gravityforms.php:6297
#: includes/blocks/config/class-gf-blocks-config.php:150
#: widget.php:142
msgid "Select a Form"
msgstr ""

#: export.php:872
msgid "Select a form"
msgstr ""

#: export.php:897
msgid "Select Fields"
msgstr ""

#: export.php:916
msgid "Select Date Range"
msgstr ""

#: export.php:922
msgid "Start"
msgstr ""

#: export.php:927
msgid "End"
msgstr ""

#: export.php:931
msgid "Date Range is optional, if no date range is selected all entries will be exported."
msgstr ""

#: export.php:941
msgid "Exporting entries. Progress:"
msgstr ""

#: export.php:1342
msgid "Created By (User Id)"
msgstr ""

#: export.php:1345
msgid "Date Updated"
msgstr ""

#: export.php:1346
#: forms_model.php:6791
#: includes/addon/class-gf-addon.php:3193
#: includes/settings/fields/class-generic-map.php:546
#: select_columns.php:199
msgid "Source Url"
msgstr ""

#: export.php:1352
msgid "User Agent"
msgstr ""

#: export.php:1534
msgid "The PHP readfile function is not available, please contact the web host."
msgstr ""

#: forms_model.php:1303
#: includes/save-form/class-gf-form-crud-handler.php:401
msgid "Admin Notification"
msgstr ""

#: forms_model.php:1327
msgid "User Notification"
msgstr ""

#: forms_model.php:1509
msgid "Notification not found"
msgstr ""

#: forms_model.php:1541
msgid "Confirmation not found"
msgstr ""

#: forms_model.php:1868
#: forms_model.php:1895
#: forms_model.php:1947
#: forms_model.php:1980
#: forms_model.php:2016
#: forms_model.php:8455
#: includes/api.php:131
#: includes/api.php:158
#: includes/api.php:180
#: includes/api.php:212
#: includes/api.php:328
#: includes/api.php:389
#: includes/api.php:410
#: includes/api.php:463
#: includes/api.php:746
#: includes/api.php:780
#: includes/api.php:830
#: includes/api.php:1224
#: includes/api.php:1348
#: includes/api.php:1524
#: includes/api.php:1579
#: includes/api.php:1681
#: includes/api.php:2146
#: includes/api.php:2185
#: includes/api.php:2232
msgid "Submissions are currently blocked due to an upgrade in progress"
msgstr ""

#: forms_model.php:2952
msgid "WordPress successfully passed the notification email to the sending server."
msgstr ""

#: forms_model.php:2958
msgid "WordPress was unable to send the notification email."
msgstr ""

#. translators: Notification name followed by its ID. e.g. Admin Notification (ID: 5d4c0a2a37204).
#: forms_model.php:2981
msgid "%1$s (ID: %2$s)"
msgstr ""

#: forms_model.php:3045
#: includes/legacy/forms_model_legacy.php:519
msgid "You don't have adequate permission to edit entries."
msgstr ""

#: forms_model.php:3063
#: includes/legacy/forms_model_legacy.php:554
msgid "An error prevented the entry for this form submission being saved. Please contact support."
msgstr ""

#: forms_model.php:3237
msgid "Error while saving field values: %s"
msgstr ""

#: forms_model.php:3259
msgid "Error while saving calculation field values: %s"
msgstr ""

#: forms_model.php:3278
msgid "Error while saving total field values: %s"
msgstr ""

#: forms_model.php:6893
#: forms_model.php:6901
#: forms_model.php:6906
#: form_settings.php:306
msgid "(Required)"
msgstr ""

#: forms_model.php:7128
#: forms_model.php:7192
msgid "Default Confirmation"
msgstr ""

#: forms_model.php:7157
msgid "Save and Continue Confirmation"
msgstr ""

#: forms_model.php:7162
msgid "Link to continue editing later"
msgstr ""

#: forms_model.php:7163
msgid "Please use the following link to return and complete this form from any computer."
msgstr ""

#: forms_model.php:7164
msgid "Note: This link will expire after 30 days."
msgstr ""

#: forms_model.php:7165
msgid "Enter your email address if you would like to receive the link via email."
msgstr ""

#: forms_model.php:7176
msgid "Save and Continue Email Sent Confirmation"
msgstr ""

#: forms_model.php:7181
msgid "Success!"
msgstr ""

#: forms_model.php:7182
msgid "The link was sent to the following email address:"
msgstr ""

#: forms_model.php:8447
msgid "Updating the id property is not supported"
msgstr ""

#: forms_model.php:8451
msgid "%s is not a valid feed property"
msgstr ""

#: forms_model.php:8461
msgid "Feed meta should be an associative array or JSON"
msgstr ""

#: forms_model.php:8478
#: includes/api.php:2207
msgid "There was an error while updating feed id %s"
msgstr ""

#: forms_model.php:8482
#: includes/api.php:2163
msgid "Feed id %s not found"
msgstr ""

#: form_detail.php:157
#: form_detail.php:482
msgid "General"
msgstr ""

#: form_detail.php:160
#: form_detail.php:2127
#: includes/blocks/config/class-gf-blocks-config.php:102
msgid "Appearance"
msgstr ""

#: form_detail.php:163
#: form_detail.php:1325
#: form_detail.php:2375
#: includes/blocks/config/class-gf-blocks-config.php:100
msgid "Advanced"
msgstr ""

#: form_detail.php:179
#: includes/blocks/config/class-gf-blocks-config.php:118
#: includes/config/items/class-gf-config-admin.php:46
msgid "Edit Form"
msgstr ""

#: form_detail.php:190
msgid "Return to form list"
msgstr ""

#: form_detail.php:239
#: form_detail.php:250
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:40
msgid "Save Form"
msgstr ""

#: form_detail.php:253
#: includes/embed-form/config/class-gf-embed-config-i18n.php:53
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:39
msgid "Saving"
msgstr ""

#: form_detail.php:301
msgid "The Form"
msgstr ""

#: form_detail.php:313
msgid "Pagination Options"
msgstr ""

#: form_detail.php:313
msgid "Manage pagination options"
msgstr ""

#: form_detail.php:314
msgid "Start Paging"
msgstr ""

#: form_detail.php:332
msgid "Simply drag and drop the fields or elements you want in this form."
msgstr ""

#: form_detail.php:335
msgid "Last page options"
msgstr ""

#: form_detail.php:335
msgid "Manage last page options"
msgstr ""

#: form_detail.php:336
msgid "End Paging"
msgstr ""

#: form_detail.php:344
msgid "You have successfully saved your form!"
msgstr ""

#: form_detail.php:346
msgid "What would you like to do next?"
msgstr ""

#: form_detail.php:350
msgid "Preview this Form"
msgstr ""

#: form_detail.php:358
msgid "Setup Email Notifications for this Form"
msgstr ""

#: form_detail.php:363
msgid "Continue Editing this Form"
msgstr ""

#: form_detail.php:367
msgid "Return to Form List"
msgstr ""

#: form_detail.php:392
msgid "Form Options and Settings"
msgstr ""

#: form_detail.php:395
msgid "Search a form field by name"
msgstr ""

#: form_detail.php:396
msgid "Search for a field"
msgstr ""

#: form_detail.php:400
msgid "Add Fields"
msgstr ""

#: form_detail.php:401
#: tooltips.php:167
msgid "Field Settings"
msgstr ""

#: form_detail.php:407
msgid "Custom settings"
msgstr ""

#: form_detail.php:420
msgid "Drag a field to the left to start building your form and then start configuring it."
msgstr ""

#: form_detail.php:436
msgid "No Matching Fields"
msgstr ""

#: form_detail.php:460
msgid "No field selected"
msgstr ""

#: form_detail.php:488
#: tooltips.php:114
msgid "Progress Indicator"
msgstr ""

#: form_detail.php:493
msgid "Progress Bar"
msgstr ""

#: form_detail.php:495
msgid "Steps"
msgstr ""

#: form_detail.php:497
#: form_detail.php:1833
msgid "None"
msgstr ""

#: form_detail.php:504
#: tooltips.php:115
msgid "Progress Bar Style"
msgstr ""

#: form_detail.php:508
msgid "Blue"
msgstr ""

#: form_detail.php:509
msgid "Gray"
msgstr ""

#: form_detail.php:510
#: form_detail.php:1624
msgid "Green"
msgstr ""

#: form_detail.php:511
msgid "Orange"
msgstr ""

#: form_detail.php:512
msgid "Red"
msgstr ""

#: form_detail.php:513
msgid "Gradient: Spring"
msgstr ""

#: form_detail.php:514
msgid "Gradient: Blues"
msgstr ""

#: form_detail.php:515
msgid "Gradient: Rainbow"
msgstr ""

#: form_detail.php:521
msgid "Text Color"
msgstr ""

#: form_detail.php:527
#: form_detail.php:942
msgid "Background Color"
msgstr ""

#: form_detail.php:534
#: tooltips.php:116
msgid "Page Names"
msgstr ""

#: form_detail.php:545
msgid "Display completed progress bar on confirmation"
msgstr ""

#: form_detail.php:552
msgid "Completion Text"
msgstr ""

#: form_detail.php:560
#: form_detail.php:812
msgid "Previous Button"
msgstr ""

#: form_detail.php:565
#: form_detail.php:784
#: form_detail.php:818
msgid "Default"
msgstr ""

#: form_detail.php:568
#: form_detail.php:615
#: form_detail.php:787
#: form_detail.php:821
msgid "Image"
msgstr ""

#: form_detail.php:573
msgid "Button Text:"
msgstr ""

#: form_detail.php:580
#: form_detail.php:800
#: form_detail.php:834
msgid "Image Path:"
msgstr ""

#: form_detail.php:599
#: tooltips.php:42
#: tooltips.php:43
msgid "Field Label"
msgstr ""

#: form_detail.php:608
msgid "Submit Input Type"
msgstr ""

#: form_detail.php:612
#: includes/blocks/config/class-gf-blocks-config.php:159
#: includes/class-confirmation.php:231
#: includes/class-confirmation.php:1185
#: includes/template-library/templates/templates.php:1497
#: includes/template-library/templates/templates.php:5539
#: includes/template-library/templates/templates.php:10211
#: js.php:1076
msgid "Text"
msgstr ""

#: form_detail.php:621
msgid "Submit Button Text"
msgstr ""

#: form_detail.php:627
msgid "Submit Button Image URL"
msgstr ""

#: form_detail.php:637
msgid "Checkbox Label"
msgstr ""

#: form_detail.php:647
#: form_detail.php:1228
#: includes/fields/class-gf-field-post-image.php:169
#: includes/fields/class-gf-field-post-image.php:171
#: includes/fields/class-gf-field-post-image.php:237
#: includes/fields/class-gf-field-post-image.php:246
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:93
#: includes/system-status/class-gf-update.php:53
#: includes/template-library/templates/templates.php:1503
#: includes/template-library/templates/templates.php:5545
#: includes/template-library/templates/templates.php:10217
#: includes/webapi/includes/class-gf-api-keys-table.php:27
#: includes/webapi/webapi.php:397
#: js.php:1076
msgid "Description"
msgstr ""

#: form_detail.php:657
msgid "Product Field Mapping"
msgstr ""

#: form_detail.php:669
#: form_detail.php:687
#: form_detail.php:723
#: form_detail.php:738
#: form_detail.php:753
#: form_detail.php:855
#: form_detail.php:886
#: form_detail.php:1100
msgid "Field Type"
msgstr ""

#: form_detail.php:674
msgid "Single Product"
msgstr ""

#: form_detail.php:675
#: form_detail.php:693
#: form_detail.php:728
#: form_detail.php:743
#: form_detail.php:759
#: form_detail.php:863
#: form_detail.php:892
#: form_detail.php:1104
#: includes/fields/class-gf-field-select.php:22
msgid "Drop Down"
msgstr ""

#: form_detail.php:676
#: form_detail.php:694
#: form_detail.php:730
#: form_detail.php:745
#: form_detail.php:867
#: form_detail.php:894
#: form_detail.php:1106
#: includes/fields/class-gf-field-radio.php:23
msgid "Radio Buttons"
msgstr ""

#: form_detail.php:677
#: form_detail.php:744
msgid "User Defined Price"
msgstr ""

#: form_detail.php:679
msgid "Calculation"
msgstr ""

#: form_detail.php:692
msgid "Single Method"
msgstr ""

#: form_detail.php:702
#: form_detail.php:1561
#: includes/fields/class-gf-field-calculation.php:107
#: includes/fields/class-gf-field-singleproduct.php:153
#: includes/fields/class-gf-field-singleproduct.php:163
#: includes/fields/class-gf-field-singleproduct.php:164
#: includes/fields/class-gf-field-singleproduct.php:172
#: includes/orders/summaries/class-gf-order-summary.php:65
#: includes/template-library/templates/templates.php:2083
#: includes/template-library/templates/templates.php:2144
#: includes/template-library/templates/templates.php:2940
#: includes/template-library/templates/templates.php:3003
#: includes/template-library/templates/templates.php:3746
#: includes/template-library/templates/templates.php:3809
#: js.php:989
#: js.php:1049
msgid "Price"
msgstr ""

#: form_detail.php:713
msgid "Disable quantity field"
msgstr ""

#: form_detail.php:729
#: form_detail.php:866
#: form_detail.php:893
#: form_detail.php:1105
#: includes/fields/class-gf-field-checkbox.php:37
msgid "Checkboxes"
msgstr ""

#: form_detail.php:758
#: form_detail.php:865
#: form_detail.php:1719
#: includes/fields/class-gf-field-number.php:13
#: js.php:887
msgid "Number"
msgstr ""

#: form_detail.php:769
#: includes/class-confirmation.php:869
#: tooltips.php:134
msgid "Content"
msgstr ""

#: form_detail.php:793
#: form_detail.php:827
msgid "Text:"
msgstr ""

#: form_detail.php:846
msgid "Disable default margins"
msgstr ""

#: form_detail.php:860
#: form_detail.php:2824
#: tooltips.php:139
msgid "Standard Fields"
msgstr ""

#: form_detail.php:861
#: form_detail.php:891
msgid "Single line text"
msgstr ""

#: form_detail.php:862
#: includes/fields/class-gf-field-textarea.php:13
msgid "Paragraph Text"
msgstr ""

#: form_detail.php:864
#: form_detail.php:895
#: form_detail.php:1107
#: includes/fields/class-gf-field-multiselect.php:30
msgid "Multi Select"
msgstr ""

#: form_detail.php:870
#: form_detail.php:2842
#: tooltips.php:140
msgid "Advanced Fields"
msgstr ""

#: form_detail.php:872
#: includes/fields/class-gf-field-time.php:43
#: js.php:912
msgid "Time"
msgstr ""

#: form_detail.php:873
#: includes/fields/class-gf-field-phone.php:38
#: includes/template-library/templates/templates.php:664
#: includes/template-library/templates/templates.php:5317
#: includes/template-library/templates/templates.php:5922
#: js.php:896
msgid "Phone"
msgstr ""

#: form_detail.php:874
#: includes/fields/class-gf-field-website.php:13
#: includes/template-library/templates/templates.php:8513
#: js.php:918
msgid "Website"
msgstr ""

#: form_detail.php:875
#: includes/addon/class-gf-payment-addon.php:2881
#: includes/fields/class-gf-field-email.php:13
#: includes/template-library/templates/templates.php:157
#: includes/template-library/templates/templates.php:660
#: includes/template-library/templates/templates.php:1361
#: includes/template-library/templates/templates.php:1762
#: includes/template-library/templates/templates.php:2395
#: includes/template-library/templates/templates.php:3212
#: includes/template-library/templates/templates.php:4018
#: includes/template-library/templates/templates.php:5865
#: includes/template-library/templates/templates.php:6894
#: includes/template-library/templates/templates.php:7605
#: includes/template-library/templates/templates.php:7928
#: includes/template-library/templates/templates.php:8425
#: includes/template-library/templates/templates.php:9456
#: includes/template-library/templates/templates.php:9795
#: js.php:880
msgid "Email"
msgstr ""

#: form_detail.php:876
#: includes/fields/class-gf-field-fileupload.php:109
msgid "File Upload"
msgstr ""

#: form_detail.php:877
#: includes/fields/class-gf-field-list.php:36
#: js.php:699
msgid "List"
msgstr ""

#: form_detail.php:907
#: includes/class-confirmation.php:868
#: settings.php:1103
msgid "Type"
msgstr ""

#: form_detail.php:911
msgid "Really Simple CAPTCHA"
msgstr ""

#: form_detail.php:912
msgid "Math Challenge"
msgstr ""

#: form_detail.php:920
#: form_detail.php:1635
#: includes/blocks/config/class-gf-blocks-config.php:154
msgid "Size"
msgstr ""

#: form_detail.php:923
#: form_detail.php:1635
#: includes/blocks/config/class-gf-blocks-config.php:155
#: includes/fields/class-gf-field.php:2757
msgid "Small"
msgstr ""

#: form_detail.php:933
msgid "Font Color"
msgstr ""

#: form_detail.php:954
msgid "Theme"
msgstr ""

#: form_detail.php:958
msgid "Light"
msgstr ""

#: form_detail.php:959
msgid "Dark"
msgstr ""

#: form_detail.php:968
msgid "Badge Position"
msgstr ""

#: form_detail.php:972
msgid "Bottom Right"
msgstr ""

#: form_detail.php:973
msgid "Bottom Left"
msgstr ""

#: form_detail.php:974
msgid "Inline"
msgstr ""

#: form_detail.php:984
#: tooltips.php:48
msgid "Custom Field Name"
msgstr ""

#: form_detail.php:990
msgid "Existing"
msgstr ""

#: form_detail.php:994
msgid "New"
msgstr ""

#: form_detail.php:1001
msgid "Select an existing custom field"
msgstr ""

#: form_detail.php:1017
#: tooltips.php:124
msgid "Post Status"
msgstr ""

#: form_detail.php:1022
msgid "Draft"
msgstr ""

#: form_detail.php:1023
msgid "Pending Review"
msgstr ""

#: form_detail.php:1024
msgid "Published"
msgstr ""

#: form_detail.php:1038
msgid "Default Post Author"
msgstr ""

#: form_detail.php:1048
msgid "Use logged in user as author"
msgstr ""

#: form_detail.php:1060
#: tooltips.php:126
msgid "Post Format"
msgstr ""

#: form_detail.php:1088
#: js.php:668
#: tooltips.php:123
#: tooltips.php:129
msgid "Post Category"
msgstr ""

#: form_detail.php:1117
#: includes/fields/class-gf-field-post-category.php:12
msgid "Category"
msgstr ""

#: form_detail.php:1122
msgid "All Categories"
msgstr ""

#: form_detail.php:1125
#: form_detail.php:1131
msgid "Select Categories"
msgstr ""

#: form_detail.php:1152
msgid "Display placeholder"
msgstr ""

#: form_detail.php:1158
msgid "Placeholder Label"
msgstr ""

#: form_detail.php:1166
#: form_detail.php:1184
msgid "Content Template"
msgstr ""

#: form_detail.php:1169
#: form_detail.php:1187
#: form_detail.php:1202
msgid "Create content template"
msgstr ""

#: form_detail.php:1217
msgid "Image Metadata"
msgstr ""

#: form_detail.php:1219
#: includes/fields/class-gf-field-post-image.php:145
#: includes/fields/class-gf-field-post-image.php:147
#: includes/fields/class-gf-field-post-image.php:234
#: includes/fields/class-gf-field-post-image.php:243
msgid "Alternative Text"
msgstr ""

#: form_detail.php:1222
#: form_list.php:440
#: form_list.php:684
#: gravityforms.php:2881
#: includes/fields/class-gf-field-post-image.php:153
#: includes/fields/class-gf-field-post-image.php:155
#: includes/fields/class-gf-field-post-image.php:235
#: includes/fields/class-gf-field-post-image.php:244
#: includes/fields/class-gf-field-post-title.php:13
#: widget.php:138
msgid "Title"
msgstr ""

#: form_detail.php:1225
#: includes/fields/class-gf-field-post-image.php:161
#: includes/fields/class-gf-field-post-image.php:163
#: includes/fields/class-gf-field-post-image.php:236
#: includes/fields/class-gf-field-post-image.php:245
msgid "Caption"
msgstr ""

#: form_detail.php:1236
msgid "Featured Image"
msgstr ""

#: form_detail.php:1238
#: tooltips.php:132
msgid "Set as Featured Image"
msgstr ""

#: form_detail.php:1250
#: tooltips.php:53
msgid "Address Type"
msgstr ""

#: form_detail.php:1266
#: tooltips.php:88
msgid "Address Fields"
msgstr ""

#: form_detail.php:1277
#: includes/addon/class-gf-payment-addon.php:2885
#: includes/fields/class-gf-field-address.php:182
#: includes/fields/class-gf-field-address.php:473
#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:326
msgid "State"
msgstr ""

#: form_detail.php:1281
#: includes/fields/class-gf-field-address.php:479
msgid "Postal Code"
msgstr ""

#: form_detail.php:1289
msgid "Default %s"
msgstr ""

#: form_detail.php:1301
#: tooltips.php:56
msgid "Default Country"
msgstr ""

#: form_detail.php:1320
msgid "Name Format"
msgstr ""

#: form_detail.php:1324
msgid "Extended"
msgstr ""

#: form_detail.php:1335
#: tooltips.php:86
msgid "Name Fields"
msgstr ""

#: form_detail.php:1348
#: tooltips.php:52
msgid "Date Input Type"
msgstr ""

#: form_detail.php:1352
msgid "Date Field"
msgstr ""

#: form_detail.php:1353
msgid "Date Picker"
msgstr ""

#: form_detail.php:1354
msgid "Date Drop Down"
msgstr ""

#: form_detail.php:1359
msgid "Date Picker Icon"
msgstr ""

#: form_detail.php:1361
msgid "No Icon"
msgstr ""

#: form_detail.php:1364
msgid "Calendar Icon"
msgstr ""

#: form_detail.php:1367
msgid "Custom Icon"
msgstr ""

#: form_detail.php:1372
msgid "Image Path: "
msgstr ""

#: form_detail.php:1376
msgid "Preview this form to see your custom icon."
msgstr ""

#: form_detail.php:1385
msgid "Date Format"
msgstr ""

#: form_detail.php:1403
msgid "Date Format Placement"
msgstr ""

#: form_detail.php:1406
#: form_detail.php:2203
#: form_detail.php:2223
#: form_detail.php:2256
#: form_detail.php:2267
#: form_settings.php:231
#: form_settings.php:248
#: form_settings.php:264
msgid "Below inputs"
msgstr ""

#: form_detail.php:1407
#: form_detail.php:2203
#: form_detail.php:2224
#: form_detail.php:2256
#: form_detail.php:2268
#: form_settings.php:235
#: form_settings.php:252
#: form_settings.php:268
msgid "Above inputs"
msgstr ""

#: form_detail.php:1409
#: form_detail.php:2152
#: form_detail.php:2163
#: js.php:228
#: tooltips.php:91
msgid "Placeholder"
msgstr ""

#: form_detail.php:1417
msgid "Customize Fields"
msgstr ""

#: form_detail.php:1430
msgid "Allowed file extensions"
msgstr ""

#: form_detail.php:1435
msgid "Separated with commas (i.e. jpg, gif, png, pdf)"
msgstr ""

#: form_detail.php:1442
msgid "Multiple Files"
msgstr ""

#: form_detail.php:1446
#: tooltips.php:68
msgid "Enable Multi-File Upload"
msgstr ""

#: form_detail.php:1455
#: tooltips.php:69
msgid "Maximum Number of Files"
msgstr ""

#: form_detail.php:1469
#: tooltips.php:70
msgid "Maximum File Size"
msgstr ""

#: form_detail.php:1476
msgid "Maximum allowed on this server: %sMB"
msgstr ""

#: form_detail.php:1485
msgid "Columns"
msgstr ""

#: form_detail.php:1488
msgid "Enable multiple columns"
msgstr ""

#: form_detail.php:1501
#: tooltips.php:51
msgid "Maximum Rows"
msgstr ""

#: form_detail.php:1513
#: tooltips.php:66
msgid "Time Format"
msgstr ""

#: form_detail.php:1517
msgid "12 hour"
msgstr ""

#: form_detail.php:1518
msgid "24 hour"
msgstr ""

#: form_detail.php:1528
msgid "Phone Format"
msgstr ""

#: form_detail.php:1547
#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:33
msgid "Choices"
msgstr ""

#: form_detail.php:1554
msgid "Edit Choices"
msgstr ""

#: form_detail.php:1559
#: form_detail.php:2430
msgid "Label"
msgstr ""

#: form_detail.php:1560
#: form_detail.php:2430
#: includes/settings/fields/class-generic-map.php:188
#: includes/system-status/class-gf-system-report.php:112
msgid "Value"
msgstr ""

#: form_detail.php:1565
msgid "Clear Default Choices"
msgstr ""

#: form_detail.php:1570
#: form_detail.php:1741
#: includes/addon/class-gf-payment-addon.php:2686
msgid "Options"
msgstr ""

#: form_detail.php:1582
msgid "Show Values"
msgstr ""

#: form_detail.php:1588
msgid "Bulk Add / Predefined Choices"
msgstr ""

#: form_detail.php:1589
msgid "Select a category and customize the predefined choices or paste your own list to bulk add choices."
msgstr ""

#: form_detail.php:1592
msgid "Add Bulk Choices"
msgstr ""

#. Translators: This string is a list of genders.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few genders as your language has.
#: form_detail.php:1610
msgid "Male, Female, Non-binary, Agender, My gender is not listed, Prefer not to answer"
msgstr ""

#: form_detail.php:1615
msgid "Countries"
msgstr ""

#: form_detail.php:1616
msgid "U.S. States"
msgstr ""

#: form_detail.php:1617
msgid "Canadian Province/Territory"
msgstr ""

#: form_detail.php:1618
msgid "Continents"
msgstr ""

#: form_detail.php:1618
msgid "Africa"
msgstr ""

#: form_detail.php:1618
#: includes/fields/class-gf-field-address.php:626
msgid "Antarctica"
msgstr ""

#: form_detail.php:1618
msgid "Asia"
msgstr ""

#: form_detail.php:1618
#: includes/fields/class-gf-field-address.php:631
msgid "Australia"
msgstr ""

#: form_detail.php:1618
msgid "Europe"
msgstr ""

#: form_detail.php:1618
msgid "North America"
msgstr ""

#: form_detail.php:1618
msgid "South America"
msgstr ""

#: form_detail.php:1619
#: includes/template-library/templates/templates.php:6141
msgid "Gender"
msgstr ""

#: form_detail.php:1620
#: includes/template-library/templates/templates.php:6222
msgid "Age"
msgstr ""

#: form_detail.php:1620
msgid "Under 18"
msgstr ""

#: form_detail.php:1620
msgid "18-24"
msgstr ""

#: form_detail.php:1620
#: includes/template-library/templates/templates.php:6237
msgid "25-34"
msgstr ""

#: form_detail.php:1620
#: includes/template-library/templates/templates.php:6243
msgid "35-44"
msgstr ""

#: form_detail.php:1620
#: includes/template-library/templates/templates.php:6249
msgid "45-54"
msgstr ""

#: form_detail.php:1620
#: includes/template-library/templates/templates.php:6255
msgid "55-64"
msgstr ""

#: form_detail.php:1620
msgid "65 or Above"
msgstr ""

#: form_detail.php:1620
#: form_detail.php:1622
#: form_detail.php:1625
#: includes/template-library/templates/templates.php:6180
msgid "Prefer Not to Answer"
msgstr ""

#: form_detail.php:1621
msgid "Marital Status"
msgstr ""

#: form_detail.php:1621
msgid "Single"
msgstr ""

#: form_detail.php:1621
msgid "Married"
msgstr ""

#: form_detail.php:1621
msgid "Divorced"
msgstr ""

#: form_detail.php:1621
msgid "Widowed"
msgstr ""

#: form_detail.php:1621
msgid "Separated"
msgstr ""

#: form_detail.php:1621
msgid "Domestic Partnership"
msgstr ""

#: form_detail.php:1622
msgid "Employment"
msgstr ""

#: form_detail.php:1622
msgid "Employed Full-Time"
msgstr ""

#: form_detail.php:1622
msgid "Employed Part-Time"
msgstr ""

#: form_detail.php:1622
msgid "Self-employed"
msgstr ""

#: form_detail.php:1622
msgid "Not employed but looking for work"
msgstr ""

#: form_detail.php:1622
msgid "Not employed and not looking for work"
msgstr ""

#: form_detail.php:1622
msgid "Homemaker"
msgstr ""

#: form_detail.php:1622
msgid "Retired"
msgstr ""

#: form_detail.php:1622
msgid "Student"
msgstr ""

#: form_detail.php:1623
msgid "Job Type"
msgstr ""

#: form_detail.php:1623
msgid "Full-Time"
msgstr ""

#: form_detail.php:1623
msgid "Part-Time"
msgstr ""

#: form_detail.php:1623
msgid "Per Diem"
msgstr ""

#: form_detail.php:1623
msgid "Employee"
msgstr ""

#: form_detail.php:1623
msgid "Temporary"
msgstr ""

#: form_detail.php:1623
msgid "Contract"
msgstr ""

#: form_detail.php:1623
msgid "Intern"
msgstr ""

#: form_detail.php:1623
msgid "Seasonal"
msgstr ""

#: form_detail.php:1624
msgid "Industry"
msgstr ""

#: form_detail.php:1624
msgid "Accounting/Finance"
msgstr ""

#: form_detail.php:1624
msgid "Advertising/Public Relations"
msgstr ""

#: form_detail.php:1624
msgid "Aerospace/Aviation"
msgstr ""

#: form_detail.php:1624
msgid "Arts/Entertainment/Publishing"
msgstr ""

#: form_detail.php:1624
msgid "Automotive"
msgstr ""

#: form_detail.php:1624
msgid "Banking/Mortgage"
msgstr ""

#: form_detail.php:1624
msgid "Business Development"
msgstr ""

#: form_detail.php:1624
msgid "Business Opportunity"
msgstr ""

#: form_detail.php:1624
msgid "Clerical/Administrative"
msgstr ""

#: form_detail.php:1624
msgid "Construction/Facilities"
msgstr ""

#: form_detail.php:1624
msgid "Consumer Goods"
msgstr ""

#: form_detail.php:1624
msgid "Customer Service"
msgstr ""

#: form_detail.php:1624
msgid "Education/Training"
msgstr ""

#: form_detail.php:1624
msgid "Energy/Utilities"
msgstr ""

#: form_detail.php:1624
#: includes/template-library/templates/templates.php:5095
msgid "Engineering"
msgstr ""

#: form_detail.php:1624
msgid "Government/Military"
msgstr ""

#: form_detail.php:1624
msgid "Healthcare"
msgstr ""

#: form_detail.php:1624
msgid "Hospitality/Travel"
msgstr ""

#: form_detail.php:1624
msgid "Human Resources"
msgstr ""

#: form_detail.php:1624
msgid "Installation/Maintenance"
msgstr ""

#: form_detail.php:1624
msgid "Insurance"
msgstr ""

#: form_detail.php:1624
msgid "Internet"
msgstr ""

#: form_detail.php:1624
msgid "Job Search Aids"
msgstr ""

#: form_detail.php:1624
msgid "Law Enforcement/Security"
msgstr ""

#: form_detail.php:1624
msgid "Legal"
msgstr ""

#: form_detail.php:1624
msgid "Management/Executive"
msgstr ""

#: form_detail.php:1624
msgid "Manufacturing/Operations"
msgstr ""

#: form_detail.php:1624
#: includes/template-library/templates/templates.php:5089
#: includes/template-library/templates/templates.php:10019
msgid "Marketing"
msgstr ""

#: form_detail.php:1624
msgid "Non-Profit/Volunteer"
msgstr ""

#: form_detail.php:1624
msgid "Pharmaceutical/Biotech"
msgstr ""

#: form_detail.php:1624
msgid "Professional Services"
msgstr ""

#: form_detail.php:1624
msgid "QA/Quality Control"
msgstr ""

#: form_detail.php:1624
msgid "Real Estate"
msgstr ""

#: form_detail.php:1624
msgid "Restaurant/Food Service"
msgstr ""

#: form_detail.php:1624
#: includes/template-library/templates/templates.php:10025
msgid "Retail"
msgstr ""

#: form_detail.php:1624
#: includes/template-library/templates/templates.php:5083
msgid "Sales"
msgstr ""

#: form_detail.php:1624
msgid "Science/Research"
msgstr ""

#: form_detail.php:1624
msgid "Skilled Labor"
msgstr ""

#: form_detail.php:1624
#: includes/template-library/templates/templates.php:10037
msgid "Technology"
msgstr ""

#: form_detail.php:1624
msgid "Telecommunications"
msgstr ""

#: form_detail.php:1624
msgid "Transportation/Logistics"
msgstr ""

#: form_detail.php:1625
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:234
#: includes/template-library/templates/templates.php:9989
msgid "Education"
msgstr ""

#: form_detail.php:1625
msgid "High School"
msgstr ""

#: form_detail.php:1625
msgid "Associate Degree"
msgstr ""

#: form_detail.php:1625
msgid "Bachelor's Degree"
msgstr ""

#: form_detail.php:1625
msgid "Graduate or Professional Degree"
msgstr ""

#: form_detail.php:1625
msgid "Some College"
msgstr ""

#: form_detail.php:1626
msgid "Days of the Week"
msgstr ""

#: form_detail.php:1626
msgid "Sunday"
msgstr ""

#: form_detail.php:1626
#: includes/template-library/templates/templates.php:5179
msgid "Monday"
msgstr ""

#: form_detail.php:1626
#: includes/template-library/templates/templates.php:5185
msgid "Tuesday"
msgstr ""

#: form_detail.php:1626
#: includes/template-library/templates/templates.php:5191
msgid "Wednesday"
msgstr ""

#: form_detail.php:1626
#: includes/template-library/templates/templates.php:5197
msgid "Thursday"
msgstr ""

#: form_detail.php:1626
#: includes/template-library/templates/templates.php:5203
msgid "Friday"
msgstr ""

#: form_detail.php:1626
msgid "Saturday"
msgstr ""

#: form_detail.php:1627
msgid "Months of the Year"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:37
msgid "January"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:38
msgid "February"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:39
msgid "March"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:40
msgid "April"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:41
msgctxt "Full month name"
msgid "May"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:42
msgid "June"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:43
msgid "July"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:44
msgid "August"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:45
msgid "September"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:46
msgid "October"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:47
msgid "November"
msgstr ""

#: form_detail.php:1627
#: includes/config/items/class-gf-config-i18n.php:48
msgid "December"
msgstr ""

#: form_detail.php:1628
msgid "How Often"
msgstr ""

#: form_detail.php:1628
msgid "Every day"
msgstr ""

#: form_detail.php:1628
msgid "Once a week"
msgstr ""

#: form_detail.php:1628
msgid "2 to 3 times a week"
msgstr ""

#: form_detail.php:1628
msgid "Once a month"
msgstr ""

#: form_detail.php:1628
msgid "2 to 3 times a month"
msgstr ""

#: form_detail.php:1628
msgid "Less than once a month"
msgstr ""

#: form_detail.php:1629
msgid "How Long"
msgstr ""

#: form_detail.php:1629
msgid "Less than a month"
msgstr ""

#: form_detail.php:1629
msgid "1-6 months"
msgstr ""

#: form_detail.php:1629
msgid "1-3 years"
msgstr ""

#: form_detail.php:1629
msgid "Over 3 years"
msgstr ""

#: form_detail.php:1629
msgid "Never used"
msgstr ""

#: form_detail.php:1630
msgid "Satisfaction"
msgstr ""

#: form_detail.php:1630
msgid "Very Satisfied"
msgstr ""

#: form_detail.php:1630
msgid "Satisfied"
msgstr ""

#: form_detail.php:1630
#: includes/template-library/templates/templates.php:8581
#: includes/template-library/templates/templates.php:9115
msgid "Neutral"
msgstr ""

#: form_detail.php:1630
msgid "Unsatisfied"
msgstr ""

#: form_detail.php:1630
msgid "Very Unsatisfied"
msgstr ""

#: form_detail.php:1631
msgid "Importance"
msgstr ""

#: form_detail.php:1631
msgid "Very Important"
msgstr ""

#: form_detail.php:1631
msgid "Important"
msgstr ""

#: form_detail.php:1631
msgid "Somewhat Important"
msgstr ""

#: form_detail.php:1631
msgid "Not Important"
msgstr ""

#: form_detail.php:1632
msgid "Agreement"
msgstr ""

#: form_detail.php:1632
msgid "Strongly Agree"
msgstr ""

#: form_detail.php:1632
#: includes/template-library/templates/templates.php:9121
msgid "Agree"
msgstr ""

#: form_detail.php:1632
#: includes/template-library/templates/templates.php:9109
msgid "Disagree"
msgstr ""

#: form_detail.php:1632
msgid "Strongly Disagree"
msgstr ""

#: form_detail.php:1633
msgid "Comparison"
msgstr ""

#: form_detail.php:1633
msgid "Much Better"
msgstr ""

#: form_detail.php:1633
msgid "Somewhat Better"
msgstr ""

#: form_detail.php:1633
msgid "About the Same"
msgstr ""

#: form_detail.php:1633
msgid "Somewhat Worse"
msgstr ""

#: form_detail.php:1633
msgid "Much Worse"
msgstr ""

#: form_detail.php:1634
msgid "Would You"
msgstr ""

#: form_detail.php:1634
msgid "Definitely"
msgstr ""

#: form_detail.php:1634
msgid "Probably"
msgstr ""

#: form_detail.php:1634
msgid "Not Sure"
msgstr ""

#: form_detail.php:1634
msgid "Probably Not"
msgstr ""

#: form_detail.php:1634
msgid "Definitely Not"
msgstr ""

#: form_detail.php:1635
msgid "Extra Small"
msgstr ""

#: form_detail.php:1635
msgid "Extra Large"
msgstr ""

#: form_detail.php:1674
msgid "Insert Choices"
msgstr ""

#: form_detail.php:1679
msgid "Save as new custom choice"
msgstr ""

#: form_detail.php:1683
msgid "Save as"
msgstr ""

#: form_detail.php:1684
msgid "Enter name"
msgstr ""

#: form_detail.php:1684
msgid "enter name"
msgstr ""

#: form_detail.php:1711
msgid "Selections"
msgstr ""

#: form_detail.php:1713
msgid "Select One"
msgstr ""

#: form_detail.php:1714
msgid "Select Multiple"
msgstr ""

#: form_detail.php:1715
msgid "Select Exact Number"
msgstr ""

#: form_detail.php:1716
msgid "Select a Range"
msgstr ""

#: form_detail.php:1724
msgid "Minimum"
msgstr ""

#: form_detail.php:1728
msgid "Maximum"
msgstr ""

#: form_detail.php:1749
msgid "Enable Select All"
msgstr ""

#: form_detail.php:1754
msgid "\"Select All\" text"
msgstr ""

#: form_detail.php:1766
msgid "Enable \"other\" choice"
msgstr ""

#: form_detail.php:1785
msgid "Enable Email Confirmation"
msgstr ""

#: form_detail.php:1798
msgid "Password Fields"
msgstr ""

#: form_detail.php:1811
msgid "Enable Password Visibility Toggle"
msgstr ""

#: form_detail.php:1818
msgid "Enable Password Strength"
msgstr ""

#: form_detail.php:1829
msgid "Minimum Strength"
msgstr ""

#: form_detail.php:1834
msgid "Short"
msgstr ""

#: form_detail.php:1835
msgid "Bad"
msgstr ""

#: form_detail.php:1836
msgid "Good"
msgstr ""

#: form_detail.php:1837
#: form_display.php:3886
msgid "Strong"
msgstr ""

#: form_detail.php:1847
#: tooltips.php:63
msgid "Number Format"
msgstr ""

#: form_detail.php:1853
#: includes/system-status/class-gf-system-report.php:884
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:20
#: tooltips.php:157
msgid "Currency"
msgstr ""

#: form_detail.php:1863
#: tooltips.php:81
msgid "Sub-Labels"
msgstr ""

#: form_detail.php:1878
msgid "Supported Credit Cards"
msgstr ""

#: form_detail.php:1904
#: tooltips.php:138
msgid "Input Mask"
msgstr ""

#: form_detail.php:1911
msgid "Mask Type"
msgstr ""

#: form_detail.php:1928
msgid "Enter a custom mask"
msgstr ""

#: form_detail.php:1929
msgid "Custom Mask Instructions"
msgstr ""

#: form_detail.php:1929
#: gravityforms.php:1878
#: includes/class-gf-osdxp.php:30
#: includes/class-gf-osdxp.php:283
#: includes/class-gf-osdxp.php:284
msgid "Help"
msgstr ""

#: form_detail.php:1935
msgid "Usage"
msgstr ""

#: form_detail.php:1937
msgid "Use a '9' to indicate a numerical character."
msgstr ""

#: form_detail.php:1938
msgid "Use a lower case 'a' to indicate an alphabetical character."
msgstr ""

#: form_detail.php:1939
msgid "Use an asterisk '*' to indicate any alphanumeric character."
msgstr ""

#: form_detail.php:1940
msgid "Use a question mark '?' to indicate optional characters. Note: All characters after the question mark will be optional."
msgstr ""

#: form_detail.php:1941
msgid "All other characters are literal values and will be displayed automatically."
msgstr ""

#: form_detail.php:1944
msgid "Examples"
msgstr ""

#: form_detail.php:1948
#: form_detail.php:1955
#: form_detail.php:1962
#: form_detail.php:1969
#: form_detail.php:1976
msgid "Mask"
msgstr ""

#: form_detail.php:1950
#: form_detail.php:1957
#: form_detail.php:1964
#: form_detail.php:1971
#: form_detail.php:1978
msgid "Valid Input"
msgstr ""

#: form_detail.php:1954
msgid "Social Security Number"
msgstr ""

#: form_detail.php:1961
msgid "Course Code"
msgstr ""

#: form_detail.php:1968
#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:53
msgid "License Key"
msgstr ""

#: form_detail.php:1975
msgid "Zip Code w/ Optional Plus Four"
msgstr ""

#: form_detail.php:1987
msgid "Select a Mask"
msgstr ""

#: form_detail.php:2006
#: tooltips.php:50
msgid "Maximum Characters"
msgstr ""

#: form_detail.php:2017
msgid "Range"
msgstr ""

#: form_detail.php:2021
msgid "Min"
msgstr ""

#: form_detail.php:2027
msgid "Max"
msgstr ""

#: form_detail.php:2042
#: tooltips.php:77
msgid "Enable Calculation"
msgstr ""

#: form_detail.php:2050
#: tooltips.php:78
msgid "Formula"
msgstr ""

#: form_detail.php:2076
msgid "Validate Formula"
msgstr ""

#: form_detail.php:2080
#: tooltips.php:79
msgid "Rounding"
msgstr ""

#: form_detail.php:2089
msgid "Do not round"
msgstr ""

#: form_detail.php:2104
msgid "Rules"
msgstr ""

#: form_detail.php:2109
#: includes/settings/class-settings.php:901
#: includes/template-library/config/class-gf-template-library-config.php:92
msgid "Required"
msgstr ""

#: form_detail.php:2114
#: tooltips.php:74
msgid "No Duplicates"
msgstr ""

#: form_detail.php:2133
#: form_settings.php:321
#: tooltips.php:100
msgid "CSS Class Name"
msgstr ""

#: form_detail.php:2156
#: form_detail.php:2167
msgid "Placeholder text is not supported when using the Rich Text Editor."
msgstr ""

#: form_detail.php:2176
#: tooltips.php:92
msgid "Placeholders"
msgstr ""

#: form_detail.php:2192
#: form_settings.php:205
msgid "Left aligned"
msgstr ""

#: form_detail.php:2195
#: form_settings.php:209
msgid "Right aligned"
msgstr ""

#: form_detail.php:2199
#: form_settings.php:201
msgid "Top aligned"
msgstr ""

#: form_detail.php:2207
msgid "Field Label Visibility"
msgstr ""

#: form_detail.php:2211
msgid "Visible (%s)"
msgstr ""

#: form_detail.php:2216
#: form_settings.php:217
#: tooltips.php:27
#: tooltips.php:83
msgid "Description Placement"
msgstr ""

#: form_detail.php:2222
#: form_detail.php:2266
msgid "Use Form Setting (%s)"
msgstr ""

#: form_detail.php:2231
msgid "Choice Alignment"
msgstr ""

#: form_detail.php:2235
msgid "Vertical"
msgstr ""

#: form_detail.php:2238
msgid "Horizontal"
msgstr ""

#: form_detail.php:2244
msgid "Choice Label Visibility"
msgstr ""

#: form_detail.php:2260
#: form_settings.php:260
#: tooltips.php:29
#: tooltips.php:84
msgid "Sub-Label Placement"
msgstr ""

#: form_detail.php:2276
msgid "Custom Validation Message"
msgstr ""

#: form_detail.php:2289
msgid "Submit Button Width"
msgstr ""

#: form_detail.php:2293
msgid "Auto"
msgstr ""

#: form_detail.php:2296
msgid "Fill Container"
msgstr ""

#: form_detail.php:2315
msgid "Submit Button Location"
msgstr ""

#: form_detail.php:2319
msgid "End of the form"
msgstr ""

#: form_detail.php:2322
msgid "End of the last row"
msgstr ""

#: form_detail.php:2330
msgid "Custom CSS Class"
msgstr ""

#: form_detail.php:2343
msgid "Enable enhanced user interface"
msgstr ""

#: form_detail.php:2356
#: tooltips.php:85
msgid "Field Size"
msgstr ""

#: form_detail.php:2391
msgid "Admin Field Label"
msgstr ""

#: form_detail.php:2405
#: form_detail.php:2415
#: js.php:203
#: tooltips.php:89
msgid "Default Value"
msgstr ""

#: form_detail.php:2425
msgid "Prefix Choices"
msgstr ""

#: form_detail.php:2441
msgid "Enable Autocomplete"
msgstr ""

#: form_detail.php:2452
#: tooltips.php:90
msgid "Default Values"
msgstr ""

#: form_detail.php:2470
msgid "Display option to use the values submitted in different field"
msgstr ""

#: form_detail.php:2476
msgid "To activate this option, please add a field to be used as the source."
msgstr ""

#: form_detail.php:2482
#: tooltips.php:94
msgid "Option Label"
msgstr ""

#: form_detail.php:2487
#: tooltips.php:95
msgid "Source Field"
msgstr ""

#: form_detail.php:2497
msgid "Activated by default"
msgstr ""

#: form_detail.php:2511
msgid "Language"
msgstr ""

#: form_detail.php:2516
msgid "Arabic"
msgstr ""

#: form_detail.php:2517
msgid "Afrikaans"
msgstr ""

#: form_detail.php:2518
msgid "Amharic"
msgstr ""

#: form_detail.php:2519
msgid "Armenian"
msgstr ""

#: form_detail.php:2520
msgid "Azerbaijani"
msgstr ""

#: form_detail.php:2521
msgid "Basque"
msgstr ""

#: form_detail.php:2522
msgid "Bengali"
msgstr ""

#: form_detail.php:2523
msgid "Bulgarian"
msgstr ""

#: form_detail.php:2524
msgid "Catalan"
msgstr ""

#: form_detail.php:2525
msgid "Chinese (Hong Kong)"
msgstr ""

#: form_detail.php:2526
msgid "Chinese (Simplified)"
msgstr ""

#: form_detail.php:2527
msgid "Chinese (Traditional)"
msgstr ""

#: form_detail.php:2528
msgid "Croatian"
msgstr ""

#: form_detail.php:2529
msgid "Czech"
msgstr ""

#: form_detail.php:2530
msgid "Danish"
msgstr ""

#: form_detail.php:2531
msgid "Dutch"
msgstr ""

#: form_detail.php:2532
msgid "English (UK)"
msgstr ""

#: form_detail.php:2533
msgid "English (US)"
msgstr ""

#: form_detail.php:2534
msgid "Estonian"
msgstr ""

#: form_detail.php:2535
msgid "Filipino"
msgstr ""

#: form_detail.php:2536
msgid "Finnish"
msgstr ""

#: form_detail.php:2537
msgid "French"
msgstr ""

#: form_detail.php:2538
msgid "French (Canadian)"
msgstr ""

#: form_detail.php:2539
msgid "Galician"
msgstr ""

#: form_detail.php:2540
msgid "Georgian"
msgstr ""

#: form_detail.php:2541
msgid "German"
msgstr ""

#: form_detail.php:2542
msgid "German (Austria)"
msgstr ""

#: form_detail.php:2543
msgid "German (Switzerland)"
msgstr ""

#: form_detail.php:2544
msgid "Greek"
msgstr ""

#: form_detail.php:2545
msgid "Gujarati"
msgstr ""

#: form_detail.php:2546
msgid "Hebrew"
msgstr ""

#: form_detail.php:2547
msgid "Hindi"
msgstr ""

#: form_detail.php:2548
msgid "Hungarian"
msgstr ""

#: form_detail.php:2549
msgid "Icelandic"
msgstr ""

#: form_detail.php:2550
msgid "Indonesian"
msgstr ""

#: form_detail.php:2551
msgid "Italian"
msgstr ""

#: form_detail.php:2552
msgid "Japanese"
msgstr ""

#: form_detail.php:2553
msgid "Kannada"
msgstr ""

#: form_detail.php:2554
msgid "Korean"
msgstr ""

#: form_detail.php:2555
msgid "Laothian"
msgstr ""

#: form_detail.php:2556
msgid "Latvian"
msgstr ""

#: form_detail.php:2557
msgid "Lithuanian"
msgstr ""

#: form_detail.php:2558
msgid "Malay"
msgstr ""

#: form_detail.php:2559
msgid "Malayalam"
msgstr ""

#: form_detail.php:2560
msgid "Marathi"
msgstr ""

#: form_detail.php:2561
msgid "Mongolian"
msgstr ""

#: form_detail.php:2562
msgid "Norwegian"
msgstr ""

#: form_detail.php:2563
msgid "Persian"
msgstr ""

#: form_detail.php:2564
msgid "Polish"
msgstr ""

#: form_detail.php:2565
msgid "Portuguese"
msgstr ""

#: form_detail.php:2566
msgid "Portuguese (Brazil)"
msgstr ""

#: form_detail.php:2567
msgid "Portuguese (Portugal)"
msgstr ""

#: form_detail.php:2568
msgid "Romanian"
msgstr ""

#: form_detail.php:2569
msgid "Russian"
msgstr ""

#: form_detail.php:2570
msgid "Serbian"
msgstr ""

#: form_detail.php:2571
msgid "Sinhalese"
msgstr ""

#: form_detail.php:2572
msgid "Slovak"
msgstr ""

#: form_detail.php:2573
msgid "Slovenian"
msgstr ""

#: form_detail.php:2574
msgid "Spanish"
msgstr ""

#: form_detail.php:2575
msgid "Spanish (Latin America)"
msgstr ""

#: form_detail.php:2576
msgid "Swahili"
msgstr ""

#: form_detail.php:2577
msgid "Swedish"
msgstr ""

#: form_detail.php:2578
msgid "Tamil"
msgstr ""

#: form_detail.php:2579
msgid "Telugu"
msgstr ""

#: form_detail.php:2580
msgid "Thai"
msgstr ""

#: form_detail.php:2581
msgid "Turkish"
msgstr ""

#: form_detail.php:2582
msgid "Ukrainian"
msgstr ""

#: form_detail.php:2583
msgid "Urdu"
msgstr ""

#: form_detail.php:2584
msgid "Vietnamese"
msgstr ""

#: form_detail.php:2585
msgid "Zulu"
msgstr ""

#: form_detail.php:2594
#: tooltips.php:34
msgid "Add Icon URL"
msgstr ""

#: form_detail.php:2604
#: tooltips.php:35
msgid "Delete Icon URL"
msgstr ""

#: form_detail.php:2614
msgid "Enable Password Input"
msgstr ""

#: form_detail.php:2621
#: tooltips.php:64
msgid "Force SSL"
msgstr ""

#: form_detail.php:2647
msgid "Use the Rich Text Editor"
msgstr ""

#: form_detail.php:2654
msgid "Allow field to be populated dynamically"
msgstr ""

#: form_detail.php:2708
msgid "Enable Conditional Logic"
msgstr ""

#: form_detail.php:2720
msgid "Enable Page Conditional Logic"
msgstr ""

#: form_detail.php:2732
msgid "Enable Submit Button Conditional Logic"
msgstr ""

#: form_detail.php:2740
msgid "Enable Next Button Conditional Logic"
msgstr ""

#: form_detail.php:2859
#: tooltips.php:141
msgid "Post Fields"
msgstr ""

#: form_detail.php:2872
#: tooltips.php:142
msgid "Pricing Fields"
msgstr ""

#: form_detail.php:2966
#: includes/fields/class-gf-field.php:372
msgid "Add a %s field to your form."
msgstr ""

#: form_detail.php:3157
msgid "No form found."
msgstr ""

#. Translators: 1. Opening <a> tag with link to the form export page, 2. closing <a> tag, 3. Opening <a> tag for documentation link, 4. Closing <a> tag.
#: form_detail.php:3391
msgid "If you continue to encounter this error, you can %1$sexport your form%2$s to include in your support request. You can also disable AJAX saving for this form. %3$sLearn more%4$s."
msgstr ""

#: form_detail.php:3402
#: form_detail.php:3403
msgid "Dismiss notification"
msgstr ""

#: form_detail.php:3431
msgid "This form has legacy markup enabled, which may prevent some new features from functioning."
msgstr ""

#: form_detail.php:3432
msgid "Legacy markup will be removed from Gravity Forms in version 3.1.0, and then all forms will use modern markup.  We recommend updating this form's settings to use modern markup."
msgstr ""

#: form_detail.php:3438
#: form_detail.php:3521
#: form_settings.php:715
#: form_settings.php:757
msgid "Learn More"
msgstr ""

#: form_detail.php:3439
#: form_settings.php:716
msgid "about form legacy markup"
msgstr ""

#: form_detail.php:3513
msgid "This form uses Ready Classes, which will be removed in Gravity Forms 3.1. You can now use settings or code snippets to achieve the same results."
msgstr ""

#: form_detail.php:3519
#: form_settings.php:127
#: js.php:459
msgid "Deprecation of Ready Classes in Gravity Forms 3.1"
msgstr ""

#: form_display.php:341
msgid "Review Form"
msgstr ""

#: form_display.php:1196
msgid "Sorry. You must be logged in to view this form."
msgstr ""

#. Translators: the text or symbol that indicates a field is required
#: form_display.php:1329
msgid "\"%s\" indicates required fields"
msgstr ""

#: form_display.php:1356
msgid "Save and Continue link used is expired or invalid."
msgstr ""

#: form_display.php:1458
msgid "This iframe contains the logic required to handle Ajax powered Gravity Forms."
msgstr ""

#: form_display.php:2093
msgid "Spam Filter"
msgstr ""

#: form_display.php:2094
msgid "This entry has been flagged as spam."
msgstr ""

#. translators: Variable is a complete sentence containing the reason the entry was marked as spam.
#: form_display.php:2097
msgid "Reason: %s"
msgstr ""

#: form_display.php:2483
msgid "At least one field must be filled out"
msgstr ""

#: form_display.php:2564
msgid "This date has already been taken. Please select a new date."
msgstr ""

#: form_display.php:2568
msgid "This field requires a unique entry and the values you entered have already been used."
msgstr ""

#: form_display.php:2569
msgid "This field requires a unique entry and '%s' has already been used"
msgstr ""

#: form_display.php:2602
msgid "Please enter a valid value."
msgstr ""

#: form_display.php:2602
msgid "Invalid selection. Please select from the available choices."
msgstr ""

#: form_display.php:2730
msgid "The text entered contains invalid characters."
msgstr ""

#: form_display.php:3134
msgid "All choices are selected."
msgstr ""

#: form_display.php:3135
msgid "All choices are unselected."
msgstr ""

#: form_display.php:3815
msgid "No results matched"
msgstr ""

#: form_display.php:3869
#: form_display.php:4556
msgid "of"
msgstr ""

#: form_display.php:3869
msgid "max characters"
msgstr ""

#: form_display.php:3886
#: includes/fields/class-gf-field-password.php:193
msgid "Strength indicator"
msgstr ""

#: form_display.php:3886
msgid "Mismatch"
msgstr ""

#: form_display.php:3886
msgid "Password strength unknown"
msgstr ""

#: form_display.php:3886
msgid "Weak"
msgstr ""

#: form_display.php:3886
msgid "Very weak"
msgstr ""

#: form_display.php:4286
#: form_display.php:5784
msgid "Previous Page"
msgstr ""

#: form_display.php:4292
msgid "Next Page"
msgstr ""

#: form_display.php:4293
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:50
#: includes/template-library/templates/templates.php:2244
#: includes/template-library/templates/templates.php:2440
#: includes/template-library/templates/templates.php:3061
#: includes/template-library/templates/templates.php:3257
#: includes/template-library/templates/templates.php:3867
#: includes/template-library/templates/templates.php:4063
#: includes/template-library/templates/templates.php:5000
#: includes/template-library/templates/templates.php:5359
#: includes/template-library/templates/templates.php:6063
#: includes/template-library/templates/templates.php:6394
#: includes/template-library/templates/templates.php:7212
#: includes/wizard/steps/class-gf-installation-wizard-step.php:102
#: js.php:686
msgid "Next"
msgstr ""

#: form_display.php:4556
msgid "Step"
msgstr ""

#: form_display.php:4662
msgid "Sorry. This form is no longer accepting new submissions."
msgstr ""

#: form_display.php:4683
msgid "This form is not yet available."
msgstr ""

#: form_display.php:4689
msgid "Sorry. This form is no longer available."
msgstr ""

#: form_display.php:4847
msgid "Send Link"
msgstr ""

#: form_display.php:4848
#: notification.php:294
msgid "Please enter a valid email address."
msgstr ""

#: form_display.php:4849
msgid "Email Address"
msgstr ""

#: form_display.php:5345
msgid "Oops! We could not locate your form."
msgstr ""

#: form_display.php:5376
msgid "Your form was not submitted. Please try again in a few minutes."
msgstr ""

#: form_display.php:5378
msgid "There was a problem with your submission."
msgstr ""

#: form_display.php:5378
msgid "Please review the fields below."
msgstr ""

#: form_list.php:49
#: form_settings.php:184
#: includes/template-library/config/class-gf-template-library-config.php:89
#: tooltips.php:25
msgid "Form Description"
msgstr ""

#: form_list.php:60
#: includes/template-library/config/class-gf-template-library-config.php:132
msgid "Create Form"
msgstr ""

#: form_list.php:89
msgid "WARNING: You are about to delete this form and ALL entries associated with it. "
msgstr ""

#: form_list.php:89
msgid "Cancel to stop, OK to delete."
msgstr ""

#: form_list.php:136
#: includes/class-confirmation.php:146
#: notification.php:905
msgid "Ajax error while updating form"
msgstr ""

#: form_list.php:162
msgid "WARNING: You are about to delete these forms and ALL entries associated with them. "
msgstr ""

#: form_list.php:162
#: form_list.php:166
#: includes/addon/class-gf-feed-addon.php:2100
#: notification.php:1541
msgid "'Cancel' to stop, 'OK' to delete."
msgstr ""

#: form_list.php:164
msgid "Are you sure you would like to reset the Views for the selected forms? "
msgstr ""

#: form_list.php:164
msgid "'Cancel' to stop, 'OK' to reset."
msgstr ""

#: form_list.php:166
msgid "WARNING: You are about to delete ALL entries associated with the selected forms. "
msgstr ""

#: form_list.php:179
#: gravityforms.php:1827
#: gravityforms.php:1832
#: gravityforms.php:5847
#: includes/class-gf-osdxp.php:236
#: includes/class-gf-osdxp.php:237
#: includes/class-personal-data.php:893
msgid "Forms"
msgstr ""

#: form_list.php:181
#: includes/addon/class-gf-feed-addon.php:1605
#: includes/addon/class-gf-feed-addon.php:2918
#: includes/class-confirmation.php:1214
#: notification.php:1652
msgid "Add New"
msgstr ""

#: form_list.php:196
msgid "Search Forms"
msgstr ""

#: form_list.php:211
#: form_list.php:231
#: includes/template-library/config/class-gf-template-library-config.php:100
msgid "There was an issue creating your form."
msgstr ""

#: form_list.php:223
#: form_settings.php:1497
msgid "Please enter a form title."
msgstr ""

#: form_list.php:235
#: form_settings.php:1501
#: includes/template-library/config/class-gf-template-library-config.php:99
msgid "Please enter a unique form title."
msgstr ""

#: form_list.php:296
msgid "Create a New Form"
msgstr ""

#: form_list.php:296
msgid "Provide a title and a description for this form"
msgstr ""

#: form_list.php:312
msgid "Creating Form..."
msgstr ""

#: form_list.php:347
msgid "Saved! Redirecting..."
msgstr ""

#: form_list.php:402
msgid "Forms per page:"
msgstr ""

#: form_list.php:403
msgid "Sorting Options"
msgstr ""

#: form_list.php:437
msgid "Default Sort Column"
msgstr ""

#: form_list.php:441
#: form_list.php:685
msgid "ID"
msgstr ""

#: form_list.php:442
#: form_list.php:686
#: gravityforms.php:1842
#: gravityforms.php:5636
#: gravityforms.php:5896
msgid "Entries"
msgstr ""

#: form_list.php:443
#: form_list.php:687
msgid "Views"
msgstr ""

#: form_list.php:444
#: form_list.php:688
msgid "Conversion"
msgstr ""

#: form_list.php:448
msgid "Default Sort Order"
msgstr ""

#: form_list.php:450
msgid "Ascending"
msgstr ""

#: form_list.php:451
msgid "Descending"
msgstr ""

#: form_list.php:571
msgctxt "Form List"
msgid "All"
msgstr ""

#: form_list.php:572
msgctxt "Form List"
msgid "Active"
msgstr ""

#: form_list.php:573
msgctxt "Form List"
msgid "Inactive"
msgstr ""

#: form_list.php:574
msgctxt "Form List"
msgid "Trash"
msgstr ""

#: form_list.php:665
#: form_list.php:830
msgid "Delete permanently"
msgstr ""

#: form_list.php:669
msgid "Mark as Active"
msgstr ""

#: form_list.php:670
msgid "Mark as Inactive"
msgstr ""

#: form_list.php:671
msgid "Reset Views"
msgstr ""

#: form_list.php:672
msgid "Permanently Delete Entries"
msgstr ""

#: form_list.php:673
msgid "Move to trash"
msgstr ""

#: form_list.php:848
#: includes/addon/class-gf-feed-addon.php:2099
#: includes/class-confirmation.php:1092
#: includes/editor-button/config/class-gf-editor-config.php:55
#: includes/fields/class-gf-field.php:1719
#: notification.php:1540
msgid "Duplicate"
msgstr ""

#: form_list.php:858
msgid "Move this form to the trash"
msgstr ""

#: form_list.php:883
msgid "No forms were found for your search query. %sView all forms%s."
msgstr ""

#: form_list.php:888
msgid "There are no forms in the trash."
msgstr ""

#: form_list.php:890
msgid "You don't have any forms. Let's go %screate one%s!"
msgstr ""

#: form_list.php:914
#: form_list.php:963
msgid "Form moved to the trash."
msgstr ""

#: form_list.php:917
#: form_list.php:966
msgid "You don't have adequate permission to trash forms."
msgstr ""

#: form_list.php:924
msgid "Form restored."
msgstr ""

#: form_list.php:927
msgid "You don't have adequate permission to restore forms."
msgstr ""

#: form_list.php:934
msgid "Form deleted."
msgstr ""

#: form_list.php:937
msgid "You don't have adequate permission to delete forms."
msgstr ""

#: form_list.php:944
#: form_list.php:975
msgid "Form duplicated."
msgstr ""

#: form_list.php:947
#: form_list.php:978
msgid "You don't have adequate permission to duplicate forms."
msgstr ""

#: form_list.php:997
msgid "%s form moved to the trash."
msgid_plural "%s forms moved to the trash."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:999
msgid "You don't have adequate permissions to trash forms."
msgstr ""

#: form_list.php:1005
msgid "%s form restored."
msgid_plural "%s forms restored."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:1007
msgid "You don't have adequate permissions to restore forms."
msgstr ""

#: form_list.php:1013
msgid "%s form deleted."
msgid_plural "%s forms deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:1015
msgid "You don't have adequate permissions to delete forms."
msgstr ""

#: form_list.php:1023
msgid "Views for %s form have been reset."
msgid_plural "Views for %s forms have been reset."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:1030
msgid "Entries for %s form have been deleted."
msgid_plural "Entries for %s forms have been deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:1040
msgid "%s form has been marked as active."
msgid_plural "%s forms have been marked as active."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:1046
msgid "%s form has been marked as inactive."
msgid_plural "%s forms have been marked as inactive."
msgstr[0] ""
msgstr[1] ""

#: form_settings.php:44
#: form_settings.php:1200
#: gravityforms.php:2031
#: includes/class-confirmation.php:96
#: includes/class-confirmation.php:210
msgid "Confirmations"
msgstr ""

#: form_settings.php:125
msgid "This form uses the \""
msgstr ""

#: form_settings.php:128
#: js.php:459
#: js.php:1712
#: js.php:1761
msgid "Learn more"
msgstr ""

#: form_settings.php:139
msgid "Form Basics"
msgstr ""

#: form_settings.php:172
msgid "The form title you have entered has already been used. Please enter a unique form title."
msgstr ""

#: form_settings.php:191
msgid "Form Layout"
msgstr ""

#: form_settings.php:196
msgid "Label Placement"
msgstr ""

#: form_settings.php:243
#: tooltips.php:28
msgid "Validation Message Placement"
msgstr ""

#: form_settings.php:276
#: tooltips.php:171
msgid "Validation Summary"
msgstr ""

#: form_settings.php:282
msgid "Required Field Indicator"
msgstr ""

#: form_settings.php:289
msgid "Text: (Required)"
msgstr ""

#: form_settings.php:293
msgid "Asterisk: *"
msgstr ""

#: form_settings.php:297
msgid "Custom:"
msgstr ""

#: form_settings.php:305
msgid "Custom Required Indicator"
msgstr ""

#: form_settings.php:327
msgid "Form Button"
msgstr ""

#: form_settings.php:332
msgid "Form button settings are now located in the form editor! To edit the button settings, go to the form editor and click on the submit button."
msgstr ""

#: form_settings.php:337
msgid "Save and Continue"
msgstr ""

#: form_settings.php:342
msgid "Enable Save and Continue"
msgstr ""

#: form_settings.php:347
msgid "Link Text"
msgstr ""

#: form_settings.php:348
msgid "Save & Continue"
msgstr ""

#: form_settings.php:366
msgid "Restrictions"
msgstr ""

#: form_settings.php:371
msgid "Limit number of entries"
msgstr ""

#: form_settings.php:376
msgid "Enable entry limit"
msgstr ""

#: form_settings.php:383
msgid "Number of Entries"
msgstr ""

#: form_settings.php:401
msgid "total entries"
msgstr ""

#: form_settings.php:405
msgid "per day"
msgstr ""

#: form_settings.php:409
msgid "per week"
msgstr ""

#: form_settings.php:413
msgid "per month"
msgstr ""

#: form_settings.php:417
msgid "per year"
msgstr ""

#: form_settings.php:427
msgid "Entry Limit Reached Message"
msgstr ""

#: form_settings.php:443
#: form_settings.php:448
#: tooltips.php:20
msgid "Schedule Form"
msgstr ""

#: form_settings.php:455
msgid "Schedule Start Date/Time"
msgstr ""

#: form_settings.php:468
msgid "Schedule Form End Date/Time"
msgstr ""

#: form_settings.php:481
msgid "Form Pending Message"
msgstr ""

#: form_settings.php:495
msgid "Form Expired Message"
msgstr ""

#: form_settings.php:512
#: form_settings.php:517
#: tooltips.php:111
msgid "Require user to be logged in"
msgstr ""

#: form_settings.php:524
#: tooltips.php:112
msgid "Require Login Message"
msgstr ""

#: form_settings.php:541
msgid "Form Options"
msgstr ""

#: form_settings.php:546
msgid "Anti-spam honeypot"
msgstr ""

#: form_settings.php:554
msgid "If the honeypot flags a submission as spam:"
msgstr ""

#: form_settings.php:565
msgid "Do not create an entry"
msgstr ""

#: form_settings.php:569
msgid "Create an entry and mark it as spam"
msgstr ""

#: form_settings.php:577
msgid "Animated transitions"
msgstr ""

#: form_settings.php:588
msgid "Enable legacy markup"
msgstr ""

#: form_settings.php:616
#: includes/class-confirmation.php:385
#: notification.php:551
msgid "Legacy Settings"
msgstr ""

#: form_settings.php:708
msgid "Legacy markup is incompatible with many new features, including the Orbital Theme."
msgstr ""

#: form_settings.php:709
msgid "Legacy markup will be removed in Gravity Forms 3.1.0, and then all forms will use modern markup.  We recommend using modern markup on all forms."
msgstr ""

#: form_settings.php:755
msgid "This form uses a deprecated CSS Ready Class, which will be removed in Gravity Forms 3.1."
msgstr ""

#: form_settings.php:758
msgid "about deprecated ready classes"
msgstr ""

#: form_settings.php:1048
#: form_settings.php:1214
#: includes/class-personal-data.php:176
#: includes/class-personal-data.php:578
msgid "Personal Data"
msgstr ""

#: form_settings.php:1193
#: includes/addon/class-gf-addon.php:1789
#: includes/blocks/config/class-gf-blocks-config.php:123
#: includes/class-gf-osdxp.php:245
#: includes/class-gf-osdxp.php:246
msgid "Form Settings"
msgstr ""

#: form_settings.php:1301
msgid "Confirmation deleted."
msgstr ""

#: form_settings.php:1404
msgid "Save and Continue Email"
msgstr ""

#: form_settings.php:1409
msgid "Thank you for saving {form_title}. Please use the unique link below to return to the form from any computer. <br /><br /> {save_link} <br /><br /> Remember that the link will expire after 30 days so please return via the provided link to complete your form submission."
msgstr ""

#: gravityforms.php:1610
msgid "Create Forms"
msgstr ""

#: gravityforms.php:1611
msgid "Delete Forms"
msgstr ""

#: gravityforms.php:1612
msgid "Edit Forms"
msgstr ""

#: gravityforms.php:1613
msgid "Preview Forms"
msgstr ""

#: gravityforms.php:1614
msgid "View Entries"
msgstr ""

#: gravityforms.php:1615
msgid "Edit Entries"
msgstr ""

#: gravityforms.php:1616
msgid "Delete Entries"
msgstr ""

#: gravityforms.php:1617
msgid "View Entry Notes"
msgstr ""

#: gravityforms.php:1618
msgid "Edit Entry Notes"
msgstr ""

#: gravityforms.php:1619
#: gravityforms.php:1861
#: includes/class-gf-osdxp.php:28
#: includes/class-gf-osdxp.php:254
#: includes/class-gf-osdxp.php:255
msgid "Import/Export"
msgstr ""

#: gravityforms.php:1620
msgid "View Plugin Settings"
msgstr ""

#: gravityforms.php:1621
msgid "Edit Plugin Settings"
msgstr ""

#: gravityforms.php:1622
msgid "Manage Updates"
msgstr ""

#: gravityforms.php:1623
msgid "Manage Add-Ons"
msgstr ""

#: gravityforms.php:1624
msgid "View System Status"
msgstr ""

#: gravityforms.php:1625
#: settings.php:167
#: settings.php:274
#: settings.php:288
msgid "Uninstall Gravity Forms"
msgstr ""

#: gravityforms.php:1626
msgid "Logging Settings"
msgstr ""

#: gravityforms.php:1627
msgid "REST API Settings"
msgstr ""

#: gravityforms.php:1699
msgid "Upload folder is not writable. Export and file upload features will not be functional."
msgstr ""

#: gravityforms.php:1823
msgid "Update Available"
msgstr ""

#: gravityforms.php:1832
msgid "Forms - Gravity Forms"
msgstr ""

#: gravityforms.php:1837
msgid "New Form - Gravity Forms"
msgstr ""

#: gravityforms.php:1837
#: gravityforms.php:5949
#: includes/template-library/config/class-gf-template-library-config.php:136
msgid "New Form"
msgstr ""

#: gravityforms.php:1842
msgid "Entries - Gravity Forms"
msgstr ""

#: gravityforms.php:1856
msgid "Settings - Gravity Forms"
msgstr ""

#: gravityforms.php:1856
#: gravityforms.php:2103
#: gravityforms.php:2508
#: gravityforms.php:5621
#: gravityforms.php:5907
#: includes/addon/class-gf-addon.php:4685
#: includes/addon/class-gf-addon.php:4964
#: includes/addon/class-gf-addon.php:5155
#: includes/addon/class-gf-addon.php:5588
#: includes/class-gf-osdxp.php:27
#: includes/fields/class-gf-field.php:1764
#: includes/system-status/class-gf-update.php:81
#: settings.php:1344
msgid "Settings"
msgstr ""

#: gravityforms.php:1861
msgid "Import/Export - Gravity Forms"
msgstr ""

#: gravityforms.php:1867
msgid "Add-Ons - Gravity Forms"
msgstr ""

#: gravityforms.php:1867
#: includes/class-gf-osdxp.php:29
#: includes/class-gf-osdxp.php:264
#: includes/class-gf-osdxp.php:265
#: includes/system-status/class-gf-system-report.php:372
msgid "Add-Ons"
msgstr ""

#: gravityforms.php:1873
msgid "System Status - Gravity Forms"
msgstr ""

#: gravityforms.php:1873
#: gravityforms.php:2154
#: includes/class-gf-osdxp.php:31
#: includes/class-gf-osdxp.php:274
#: includes/class-gf-osdxp.php:275
msgid "System Status"
msgstr ""

#: gravityforms.php:1878
msgid "Help - Gravity Forms"
msgstr ""

#: gravityforms.php:1995
#: includes/addon/class-gf-results.php:913
msgid "Form Not Found"
msgstr ""

#. translators: Search entries page title. 1. Search value
#: gravityforms.php:2018
msgid "Search Forms: %1$s"
msgstr ""

#: gravityforms.php:2035
msgid "New Confirmation"
msgstr ""

#. translators: Starred entry list page title. 1. form title
#: gravityforms.php:2047
msgid "Starred &#8212; %1$s"
msgstr ""

#. translators: Unread entry list page title. 1. form title
#: gravityforms.php:2052
msgid "Unread &#8212; %1$s"
msgstr ""

#. translators: Active entry list page title. 1. form title
#: gravityforms.php:2057
msgid "Spam &#8212; %1$s"
msgstr ""

#. translators: Trash entry list page title. 1. form title
#: gravityforms.php:2062
msgid "Trash &#8212; %1$s"
msgstr ""

#. translators: Search entries page title. 1. Search value, 2. Form title.
#: gravityforms.php:2068
msgid "Search Entries: %1$s &#8212; %2$s"
msgstr ""

#. translators: Single entry page title. 1: entry ID, 2: form title, 3: admin title.
#: gravityforms.php:2082
msgid "Entry # %1$d &lsaquo; %2$s &lsaquo; %3$s"
msgstr ""

#. translators: Entry not found page title. 1: form title, 2: admin title.
#: gravityforms.php:2085
msgid "Entry not found &lsaquo; %1$s &lsaquo; %2$s"
msgstr ""

#: gravityforms.php:2097
msgid "New Notification"
msgstr ""

#: gravityforms.php:2107
#: settings.php:1345
msgid "reCAPTCHA"
msgstr ""

#: gravityforms.php:2111
#: includes/addon/class-gf-addon.php:1792
#: includes/addon/class-gf-addon.php:5167
#: includes/addon/class-gf-addon.php:5522
#: settings.php:1382
msgid "Uninstall"
msgstr ""

#. Translators: Export Form page title. 1: Admin title.
#: gravityforms.php:2129
msgid "Export Forms &lsaquo; %1$s"
msgstr ""

#. Translators: Import form page title. 1: Admin title.
#: gravityforms.php:2134
msgid "Import Forms &lsaquo; %1$s"
msgstr ""

#. Translators: Imported forms page title. 1: Admin title.
#: gravityforms.php:2139
msgid "Imported Forms &lsaquo; %1$s"
msgstr ""

#. Translators: Export Entry page title. 1: Admin title.
#: gravityforms.php:2144
msgid "Export Entries &lsaquo; %1$s"
msgstr ""

#. Translators: Updates page title. 1: Admin title.
#: gravityforms.php:2149
msgid "Updates &lsaquo; %1$s"
msgstr ""

#: gravityforms.php:2442
msgid "Add Gravity Form"
msgstr ""

#: gravityforms.php:2442
msgid "Add Form"
msgstr ""

#: gravityforms.php:2457
msgid "Please select a form"
msgstr ""

#. translators: 1: The name of the add-on, 2: version number.
#: gravityforms.php:2543
msgid "This version of the %1$s is not compatible with the version of Gravity Forms that is installed. Upgrade this add-on to version %2$s or greater to avoid compatibility issues and potential loss of data."
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag
#: gravityforms.php:2641
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s. "
msgstr ""

#. translators: 1: plugin name, 2: version number, 3: changelog URL
#. translators: 1: plugin name, 2: version number, 3: changelog URL
#: gravityforms.php:2645
#: gravityforms.php:2662
msgid "<a class=\"thickbox open-plugin-details-modal\" aria-label=\"View %1$s version %2$s details\" href=\"%3$s\">"
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag, 5: open <a> tag 6. close </a> tag
#: gravityforms.php:2658
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s or %5$supdate now%6$s. "
msgstr ""

#. translators: 1: upgrade URL, 2: plugin name
#: gravityforms.php:2671
msgid "<a href=\"%1$s\" class=\"update-link\" aria-label=\"Update %2$s now\">"
msgstr ""

#. translators: %1$s Plugin name %2$s and %3$s are link tag markup
#: gravityforms.php:2699
msgid "The %1$s is not available with the configured license; please visit the %2$sGravity Forms website%3$s to verify your license. "
msgstr ""

#: gravityforms.php:2713
msgid "There is a new version of %s available. "
msgstr ""

#: gravityforms.php:2715
msgid "%1$sView version %2$s details %3$s. "
msgstr ""

#: gravityforms.php:2721
msgid "%1$sView version %2$s details %3$s or %4$supdate now%5$s."
msgstr ""

#: gravityforms.php:2748
msgid "IMPORTANT: As this is a major update, we strongly recommend creating a backup of your site before updating."
msgstr ""

#. translators: %s: version number
#: gravityforms.php:2768
msgid "The versions of the following add-ons you're running haven't been tested with Gravity Forms %s. Please update them or confirm compatibility before updating Gravity Forms, or you may experience issues:"
msgstr ""

#: gravityforms.php:2830
msgid "Oops!! Something went wrong. %sPlease try again or %scontact us%s."
msgstr ""

#: gravityforms.php:2883
msgid "Unread"
msgstr ""

#: gravityforms.php:2885
#: includes/addon/class-gf-payment-addon.php:2963
#: includes/fields/class-gf-field-total.php:25
#: includes/orders/summaries/views/view-order-summary.php:60
#: includes/orders/summaries/views/view-pricing-fields-html.php:61
#: includes/orders/summaries/views/view-pricing-fields-text.php:25
#: includes/template-library/templates/templates.php:1947
#: includes/template-library/templates/templates.php:2724
#: includes/template-library/templates/templates.php:3542
#: includes/template-library/templates/templates.php:4348
#: includes/template-library/templates/templates.php:6588
#: includes/template-library/templates/templates.php:7356
#: js.php:1012
msgid "Total"
msgstr ""

#: gravityforms.php:2905
msgid "Last Entry: %s"
msgstr ""

#: gravityforms.php:2908
msgid "View All Entries"
msgstr ""

#: gravityforms.php:2920
msgid "View All Forms"
msgstr ""

#: gravityforms.php:2927
msgid "You don't have any forms. Let's go %screate one %s!"
msgstr ""

#: gravityforms.php:2960
msgid "There is an update available for Gravity Forms. %sView Details%s"
msgstr ""

#: gravityforms.php:2963
msgid "Dismiss"
msgstr ""

#: gravityforms.php:3498
msgid "Please select a form."
msgstr ""

#: gravityforms.php:3499
msgid "Failed to load the preview for this form."
msgstr ""

#: gravityforms.php:3881
msgid "Logging disabled."
msgstr ""

#: gravityforms.php:3883
msgid "Unable to disable logging."
msgstr ""

#: gravityforms.php:3907
msgid "Finished"
msgstr ""

#: gravityforms.php:3979
msgid "Add-On browser is currently unavailable. Please try again later."
msgstr ""

#: gravityforms.php:4405
msgid "There was an error while resending the notifications."
msgstr ""

#: gravityforms.php:4411
msgid "No notifications have been selected. Please select a notification to be sent."
msgstr ""

#: gravityforms.php:4415
msgid "The %sSend To%s email address provided is not valid."
msgstr ""

#: gravityforms.php:5027
#: gravityforms.php:5043
msgid "Oops! There was an error saving the form title. Please refresh the page and try again."
msgstr ""

#: gravityforms.php:5119
msgid "Select a different form"
msgstr ""

#: gravityforms.php:5144
msgid "Search forms"
msgstr ""

#: gravityforms.php:5148
msgid "Search for form"
msgstr ""

#: gravityforms.php:5608
#: gravityforms.php:5609
msgid "Editor"
msgstr ""

#: gravityforms.php:5824
#: includes/blocks/config/class-gf-blocks-config.php:121
#: widget.php:38
msgid "Form"
msgstr ""

#: gravityforms.php:5862
msgid "Recent"
msgstr ""

#: gravityforms.php:5938
msgid "All Forms"
msgstr ""

#: gravityforms.php:6081
msgid "Auto-updates unavailable."
msgstr ""

#: gravityforms.php:6108
msgid "Please register your copy of Gravity Forms to enable automatic updates."
msgstr ""

#: gravityforms.php:6313
msgid "Select a form below to add it to your post or page."
msgstr ""

#: gravityforms.php:6314
msgid "Select a form from the list to add it to your post or page."
msgstr ""

#: gravityforms.php:6318
msgid "Can't find your form? Make sure it is active."
msgstr ""

#: gravityforms.php:6322
#: widget.php:158
msgid "Display form title"
msgstr ""

#: gravityforms.php:6327
msgid "Whether or not to display the form title."
msgstr ""

#: gravityforms.php:6330
#: widget.php:160
msgid "Display form description"
msgstr ""

#: gravityforms.php:6335
msgid "Whether or not to display the form description."
msgstr ""

#: gravityforms.php:6338
#: widget.php:167
msgid "Enable Ajax"
msgstr ""

#: gravityforms.php:6342
msgid "Specify whether or not to use Ajax to submit the form."
msgstr ""

#: gravityforms.php:6348
msgid "Specify the starting tab index for the fields of this form."
msgstr ""

#: gravityforms.php:6363
msgid "Select an action"
msgstr ""

#: gravityforms.php:6375
msgid "Select an action for this shortcode. Actions are added by some add-ons."
msgstr ""

#: gravityforms.php:6461
msgid "Gravity Forms logging is currently enabled. "
msgstr ""

#: gravityforms.php:6462
msgid "If you currently have a support ticket open, please do not disable logging until the Support Team has reviewed your logs. "
msgstr ""

#: gravityforms.php:6463
msgid "Since logs may contain sensitive information, please ensure that you only leave it enabled for as long as it is needed for troubleshooting. "
msgstr ""

#: gravityforms.php:6465
msgid "Once troubleshooting is complete, %1$sclick here to disable logging and permanently delete your log files.%2$s "
msgstr ""

#: help.php:33
#: includes/template-library/templates/templates.php:8175
msgid "How can we help you?"
msgstr ""

#: help.php:38
msgid "Please review the %sdocumentation%s first. If you still can't find the answer %sopen a support ticket%s and we will be happy to answer your questions and assist you with any problems."
msgstr ""

#: help.php:49
#: help.php:50
msgid "Search Our Documentation"
msgstr ""

#: help.php:63
msgid "User Documentation"
msgstr ""

#: help.php:67
msgid "Creating a Form"
msgstr ""

#: help.php:74
msgid "Embedding a Form"
msgstr ""

#: help.php:81
msgid "Reviewing Form Submissions"
msgstr ""

#: help.php:88
msgid "Configuring Confirmations"
msgstr ""

#: help.php:95
msgid "Configuring Notifications"
msgstr ""

#: help.php:107
msgid "Developer Documentation"
msgstr ""

#: help.php:111
msgid "Discover the Gravity Forms API"
msgstr ""

#: help.php:118
msgid "API Functions"
msgstr ""

#: help.php:125
msgid "REST API"
msgstr ""

#: help.php:132
msgid "Add-On Framework"
msgstr ""

#: help.php:139
msgid "GFAddOn"
msgstr ""

#: help.php:151
msgid "Designer Documentation"
msgstr ""

#: help.php:155
msgid "CSS Selectors"
msgstr ""

#: help.php:162
msgid "CSS Targeting Examples"
msgstr ""

#: help.php:169
msgid "CSS Ready Classes"
msgstr ""

#: help.php:176
msgid "gform_field_css_class"
msgstr ""

#: help.php:183
msgid "gform_noconflict_styles"
msgstr ""

#: includes/addon/class-gf-addon.php:773
msgid "Required Gravity Forms Add-On is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:781
msgid "Required Gravity Forms Add-On \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:807
msgid "Required WordPress plugin is missing: %1$s %2$s or newer."
msgstr ""

#: includes/addon/class-gf-addon.php:809
msgid "Required WordPress plugin is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:825
msgid "Required WordPress plugin \"%1$s\" is installed but does not meet minimum version requirement: %2$s."
msgstr ""

#: includes/addon/class-gf-addon.php:836
msgid "Current PHP version (%s) does not meet minimum PHP version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:853
msgid "Required PHP extension missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:860
msgid "Required PHP extension \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:875
msgid "Required PHP function missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:888
msgid "Current WordPress version (%s) does not meet minimum WordPress version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:927
msgid "%s is not able to run because your WordPress environment has not met the minimum requirements."
msgstr ""

#: includes/addon/class-gf-addon.php:928
msgid "Please resolve the following issues to use %s:"
msgstr ""

#: includes/addon/class-gf-addon.php:1735
msgid "GF Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1795
msgid "Add-On Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1798
msgid "Add-On Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:1803
msgid "Results Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1822
msgid "Gravity Forms Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1935
#: includes/addon/class-gf-addon.php:5025
#: includes/addon/class-gf-addon.php:5348
#: includes/addon/class-gf-feed-addon.php:2133
msgid "%s Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:2135
#: includes/addon/class-gf-addon.php:2209
msgid "Field could not be rendered."
msgstr ""

#: includes/addon/class-gf-addon.php:2224
msgid "Field type '%s' has not been implemented"
msgstr ""

#: includes/addon/class-gf-addon.php:2432
msgid "%s settings updated."
msgstr ""

#: includes/addon/class-gf-addon.php:2438
#: includes/settings/class-settings.php:992
msgid "There was an error while saving your settings."
msgstr ""

#: includes/addon/class-gf-addon.php:2979
msgid "Please add a %s field to your form."
msgstr ""

#: includes/addon/class-gf-addon.php:3072
#: includes/settings/config/class-gf-settings-config-i18n.php:35
msgid "Add Custom Key"
msgstr ""

#: includes/addon/class-gf-addon.php:3082
#: includes/settings/config/class-gf-settings-config-i18n.php:36
#: includes/settings/fields/class-select-custom.php:94
msgid "Add Custom Value"
msgstr ""

#: includes/addon/class-gf-addon.php:3103
#: includes/settings/fields/class-select-custom.php:150
msgid "Reset"
msgstr ""

#: includes/addon/class-gf-addon.php:3125
#: includes/settings/fields/class-field-map.php:40
msgid "Form Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3143
#: includes/settings/fields/class-field-map.php:35
#: js.php:177
#: js.php:203
#: js.php:228
#: js.php:259
#: js.php:296
msgid "Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3175
#: includes/settings/config/class-gf-settings-config-i18n.php:39
#: includes/settings/fields/class-field-select.php:71
#: notification.php:216
msgid "Select a Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3182
#: includes/settings/fields/class-field-select.php:82
msgid "Select a %s Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3231
#: includes/addon/class-gf-addon.php:3238
#: includes/addon/class-gf-addon.php:3258
#: includes/settings/fields/class-field-select.php:237
#: includes/settings/fields/class-generic-map.php:490
msgid "Full"
msgstr ""

#: includes/addon/class-gf-addon.php:3245
#: includes/settings/fields/class-field-select.php:248
#: includes/settings/fields/class-generic-map.php:467
msgid "Selected"
msgstr ""

#: includes/addon/class-gf-addon.php:3594
msgid "Update Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:3930
#: includes/fields/class-gf-field-calculation.php:32
#: includes/fields/class-gf-field-hiddenproduct.php:50
#: includes/fields/class-gf-field-radio.php:91
#: includes/fields/class-gf-field-repeater.php:655
#: includes/fields/class-gf-field-singleproduct.php:57
#: includes/fields/class-gf-field-website.php:69
#: includes/fields/class-gf-field.php:908
#: includes/settings/fields/class-base.php:793
msgid "This field is required."
msgstr ""

#: includes/addon/class-gf-addon.php:3975
msgid "Validation Error"
msgstr ""

#: includes/addon/class-gf-addon.php:4473
msgid "Unable to render form settings."
msgstr ""

#: includes/addon/class-gf-addon.php:4811
#: includes/addon/class-gf-addon.php:4817
msgid "You don't have adequate permission to view this page"
msgstr ""

#: includes/addon/class-gf-addon.php:4980
msgid "This add-on needs to be updated. Please contact the developer."
msgstr ""

#: includes/addon/class-gf-addon.php:4994
#: includes/addon/class-gf-addon.php:5185
#: includes/addon/class-gf-addon.php:5306
msgid "%s has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: includes/addon/class-gf-addon.php:5196
#: includes/addon/class-gf-addon.php:5210
#: includes/addon/class-gf-addon.php:5520
msgid "Uninstall %s"
msgstr ""

#: includes/addon/class-gf-addon.php:5417
msgid "You don't have sufficient permissions to update the settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5485
msgid "This operation deletes ALL %s settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5515
#: includes/addon/class-gf-addon.php:5582
msgid "%s"
msgstr ""

#: includes/addon/class-gf-addon.php:5533
msgid "Uninstall %s Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5552
msgid "Uninstall Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5583
msgid "To continue uninstalling this add-on click the settings button."
msgstr ""

#: includes/addon/class-gf-addon.php:5597
msgid "%sThis operation deletes ALL %s settings%s. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5601
msgid "Warning! ALL %s settings will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-addon.php:5627
msgid "You don't have adequate permission to uninstall this add-on: "
msgstr ""

#: includes/addon/class-gf-addon.php:5731
#: includes/addon/class-gf-auto-upgrade.php:66
msgid "Gravity Forms %s is required. Activate it now or %spurchase it today!%s"
msgstr ""

#: includes/addon/class-gf-addon.php:5744
msgid "Some features of the add-on are not available on the current version of Gravity Forms. Please update to the latest Gravity Forms version for full compatibility."
msgstr ""

#. translators: 1: Add-on title
#: includes/addon/class-gf-addon.php:6316
msgid "Some features of the %1$s Add-on are not available on this version of Gravity Forms. Please update to the latest version for full compatibility."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:200
msgid "Oops!! Something went wrong.%sPlease try again or %scontact us%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:264
#: includes/addon/class-gf-auto-upgrade.php:316
msgid "View version %s details"
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:266
#: includes/addon/class-gf-auto-upgrade.php:318
msgid "There is a new version of %1$s available. %s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:274
#: includes/addon/class-gf-auto-upgrade.php:330
msgid "Your version of %s is up to date."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:306
#: includes/system-status/class-gf-update.php:165
msgid "%sRegister%s your copy of Gravity Forms to receive access to automatic updates and support. Need a license key? %sPurchase one now%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:322
msgid "You can update to the latest version automatically or download the update and install it manually. %sUpdate Automatically%s %sDownload Update%s"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1282
msgid "Copy 1"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1284
msgid "Copy %d"
msgstr ""

#. translators: %1$s represents the missing table, %2$s is the opening link tag, %3$s is the closing link tag.
#: includes/addon/class-gf-feed-addon.php:1325
msgid "The table `%1$s` does not exist. Please visit the %2$sForms > System Status%3$s page and click the \"Re-run database upgrade\" link (under the Database section) to create the missing table."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1523
#: includes/addon/class-gf-payment-addon.php:3907
#: includes/settings/class-settings.php:1499
#: includes/webapi/webapi.php:1070
#: includes/webapi/webapi.php:1078
#: includes/webapi/webapi.php:1110
msgid "Access denied."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1610
msgid "%s Feeds"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1777
msgid "Unable to render feed settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1796
msgid "Feed Settings"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1898
msgid "You don't have sufficient permissions to update the form settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1946
msgid "Feed updated successfully."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1960
msgid "There was an error updating this feed. Please review all errors below and try again."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2100
msgid "WARNING: You are about to delete this item."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2116
msgid "You don't have any feeds configured. Let's go %screate one%s!"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2136
msgid "To get started, please configure your %s."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2198
#: includes/settings/fields/class-conditional-logic.php:45
msgid "Process this feed if"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2219
#: includes/settings/fields/class-conditional-logic.php:50
msgid "Enable Condition"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2273
msgid "Invalid value"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2359
msgid "Process %s feed only when payment is received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2371
#: includes/addon/class-gf-feed-addon.php:2374
msgid "Post Payment Actions"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2374
msgid "Select which actions should only occur after payment has been received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2771
#: settings.php:1110
msgid "Checkbox"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2787
msgid "feeds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1151
msgid "Payment failed to be captured. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1193
msgid "Initial payment"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1200
msgid "%s has been captured successfully. Amount: %s. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1205
msgid "Failed to capture %s. Reason: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1218
msgid "Subscription failed to be created. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1548
msgid "options: "
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1778
msgid "This webhook has already been processed (Event Id: %s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1944
msgid "Payment is pending. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1975
msgid "Payment has been authorized. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2010
msgid "Payment has been completed. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2068
msgid "Payment has been refunded. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2122
msgid "Payment has failed. Amount: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2141
msgid "Authorization has been voided. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2173
msgid "Subscription has been created. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2229
msgid "Subscription has been paid. Amount: %s. Subscription Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2284
msgid "Subscription payment has failed. Amount: %s. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2341
msgid "Subscription has been cancelled. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2382
msgid "Subscription has expired. Subscriber Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2477
#: includes/addon/class-gf-payment-addon.php:2567
#: includes/addon/class-gf-payment-addon.php:2571
#: includes/class-confirmation.php:867
#: includes/fields/class-gf-field-name.php:58
#: includes/template-library/templates/templates.php:33
#: includes/template-library/templates/templates.php:1250
#: includes/template-library/templates/templates.php:1639
#: includes/template-library/templates/templates.php:2078
#: includes/template-library/templates/templates.php:2139
#: includes/template-library/templates/templates.php:2280
#: includes/template-library/templates/templates.php:2935
#: includes/template-library/templates/templates.php:2998
#: includes/template-library/templates/templates.php:3097
#: includes/template-library/templates/templates.php:3741
#: includes/template-library/templates/templates.php:3804
#: includes/template-library/templates/templates.php:3903
#: includes/template-library/templates/templates.php:5742
#: includes/template-library/templates/templates.php:7498
#: includes/template-library/templates/templates.php:7804
#: includes/template-library/templates/templates.php:8302
#: includes/template-library/templates/templates.php:9341
#: includes/template-library/templates/templates.php:9681
#: js.php:707
#: js.php:989
#: notification.php:235
#: notification.php:1307
msgid "Name"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2478
#: includes/addon/class-gf-payment-addon.php:2575
#: includes/addon/class-gf-payment-addon.php:2589
msgid "Transaction Type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2486
#: includes/addon/class-gf-payment-addon.php:2587
msgid "Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2489
#: includes/addon/class-gf-payment-addon.php:2584
msgid "Products and Services"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2492
msgid "Donations"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2497
msgid "Unsupported transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2504
#: includes/addon/class-gf-payment-addon.php:2852
#: includes/addon/class-gf-payment-addon.php:2860
msgid "Form Total"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2555
msgid "You must add a Credit Card field to your form before creating a feed. Let's go %sadd one%s!"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2571
msgid "Enter a feed name to uniquely identify this setup."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2580
msgid "Select a transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2589
msgid "Select a transaction type."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2594
msgid "Subscription Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2606
msgid "Select which field determines the recurring payment amount, or select 'Form Total' to use the total of all pricing fields as the recurring amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2610
#: includes/addon/class-gf-payment-addon.php:2612
msgid "Billing Cycle"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2612
msgid "Select your billing cycle.  This determines how often the recurring payment should occur."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2616
#: includes/addon/class-gf-payment-addon.php:2624
msgid "Recurring Times"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2620
msgid "infinite"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2624
msgid "Select how many times the recurring payment should be made.  The default is to bill the customer until the subscription is canceled."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2628
msgid "Setup Fee"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2634
#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Trial"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2637
msgid "Trial Period"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2637
msgid "Enable a trial period.  The user's recurring payment will not begin until after this trial period."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2642
msgid "Products &amp; Services Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2655
msgid "Select which field determines the payment amount, or select 'Form Total' to use the total of all pricing fields as the payment amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2660
msgid "Other Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2675
#: includes/addon/class-gf-payment-addon.php:2678
msgid "Billing Information"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2678
msgid "Map your Form Fields to the available listed fields."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2696
msgid "When conditions are enabled, form submissions will only be sent to the payment gateway when the conditions are met. When disabled, all form submissions will be sent to the payment gateway."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2817
msgid "Enter an amount"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2869
msgid "Sample Option"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2882
#: includes/fields/class-gf-field-address.php:37
#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:323
#: includes/template-library/templates/templates.php:4797
#: includes/template-library/templates/templates.php:5966
#: includes/template-library/templates/templates.php:7985
#: js.php:825
msgid "Address"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2883
#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:324
msgid "Address 2"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2884
#: includes/fields/class-gf-field-address.php:211
#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:325
#: includes/template-library/templates/templates.php:542
#: includes/template-library/templates/templates.php:2505
#: includes/template-library/templates/templates.php:2592
#: includes/template-library/templates/templates.php:3322
#: includes/template-library/templates/templates.php:3409
#: includes/template-library/templates/templates.php:4128
#: includes/template-library/templates/templates.php:4215
#: includes/template-library/templates/templates.php:4819
#: includes/template-library/templates/templates.php:5988
#: includes/template-library/templates/templates.php:7131
#: includes/template-library/templates/templates.php:8007
#: js.php:840
msgid "City"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2886
#: includes/settings/fields/class-field-select.php:151
#: includes/settings/fields/class-generic-map.php:327
msgid "Zip"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2887
#: includes/fields/class-gf-field-address.php:215
#: includes/settings/fields/class-field-select.php:152
#: includes/settings/fields/class-generic-map.php:328
#: includes/template-library/templates/templates.php:561
#: includes/template-library/templates/templates.php:2523
#: includes/template-library/templates/templates.php:2610
#: includes/template-library/templates/templates.php:3340
#: includes/template-library/templates/templates.php:3427
#: includes/template-library/templates/templates.php:4146
#: includes/template-library/templates/templates.php:4233
#: includes/template-library/templates/templates.php:4837
#: includes/template-library/templates/templates.php:6006
#: includes/template-library/templates/templates.php:7146
#: includes/template-library/templates/templates.php:8025
#: js.php:855
msgid "Country"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2905
msgid "day(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2906
msgid "week(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2907
msgid "month(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2908
msgid "year(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2917
msgid "Select a product field"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2933
msgctxt "toolbar label"
msgid "Sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2934
msgctxt "metabox title"
msgid "Filter"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2960
msgid "Today"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2961
msgid "Yesterday"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2962
msgid "Last 30 Days"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2979
msgid "orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2980
msgid "subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2988
msgid "There aren't any transactions that match your criteria."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3107
#: includes/addon/class-gf-payment-addon.php:3116
msgid "Revenue"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3112
msgid "Orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3113
msgid "Subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3114
msgid "Recurring Payments"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3115
msgid "Refunds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3138
#: includes/addon/class-gf-payment-addon.php:3139
msgid "Week"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3156
#: includes/addon/class-gf-payment-addon.php:3157
#: includes/fields/class-gf-field-creditcard.php:332
#: includes/fields/class-gf-field-creditcard.php:453
#: includes/fields/class-gf-field-date.php:748
#: includes/fields/class-gf-field-date.php:993
#: includes/fields/class-gf-field-date.php:1147
#: js.php:334
#: js.php:1160
#: js.php:1165
#: js.php:1166
msgid "Month"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3174
#: includes/addon/class-gf-payment-addon.php:3177
#: includes/fields/class-gf-field-date.php:772
#: includes/fields/class-gf-field-date.php:996
#: includes/fields/class-gf-field-date.php:1152
#: js.php:336
#: js.php:1161
#: js.php:1167
#: js.php:1168
msgid "Day"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3316
msgid "Jan"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3317
msgid "Feb"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3318
msgid "Mar"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3319
msgid "Apr"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3320
msgctxt "Abbreviated month name"
msgid "May"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3321
msgid "Jun"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3322
msgid "Jul"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3323
msgid "Aug"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3324
msgid "Sep"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3325
msgid "Oct"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3326
msgid "Nov"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3327
msgid "Dec"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3446
msgid "Daily"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3447
msgid "Weekly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3448
msgid "Monthly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3454
msgid "Select how you would like the sales data to be displayed."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3464
msgctxt "regarding a payment method"
msgid "Any"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3475
#: includes/template-library/templates/templates.php:2767
#: includes/template-library/templates/templates.php:4392
#: includes/template-library/templates/templates.php:4426
msgid "Payment Method"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3596
msgid "Warning! This subscription will be canceled. This cannot be undone. 'OK' to cancel subscription, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3598
msgid "Canceled"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3599
msgid "The subscription could not be canceled. Please try again later."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3865
msgid "Cancel Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3996
msgid "sale"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3997
msgid "sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4022
msgid "There hasn't been any sales in the specified date range."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4045
msgid "1 item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/addon/class-gf-payment-addon.php:4062
msgid "Go to the first page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4069
msgid "Go to the previous page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4079
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4084
msgid "Go to the next page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:4093
msgid "Go to the last page"
msgstr ""

#: includes/addon/class-gf-results.php:21
msgid "Results Filters"
msgstr ""

#: includes/addon/class-gf-results.php:84
msgid "Error retrieving results. If the problem persists, please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:131
#: includes/addon/class-gf-results.php:161
msgid "View results generated by this form"
msgstr ""

#: includes/addon/class-gf-results.php:153
#: includes/addon/class-gf-results.php:159
msgid "Results"
msgstr ""

#: includes/addon/class-gf-results.php:255
msgid "Include results if"
msgstr ""

#: includes/addon/class-gf-results.php:262
msgid "Start date"
msgstr ""

#: includes/addon/class-gf-results.php:264
#: includes/addon/class-gf-results.php:270
#: includes/settings/fields/class-date-time.php:145
msgid "Open Date Picker"
msgstr ""

#: includes/addon/class-gf-results.php:268
msgid "End date"
msgstr ""

#: includes/addon/class-gf-results.php:289
msgid "Apply filters"
msgstr ""

#: includes/addon/class-gf-results.php:292
msgid "Clear"
msgstr ""

#: includes/addon/class-gf-results.php:318
msgid "This form does not have any fields that can be used for results"
msgstr ""

#: includes/addon/class-gf-results.php:329
#: includes/addon/class-gf-results.php:651
msgid "Total Score"
msgstr ""

#: includes/addon/class-gf-results.php:329
msgid "Scores are weighted calculations. Items ranked higher are given a greater score than items that are ranked lower. The total score for each item is the sum of the weighted scores."
msgstr ""

#: includes/addon/class-gf-results.php:330
#: includes/addon/class-gf-results.php:654
msgid "Aggregate Rank"
msgstr ""

#: includes/addon/class-gf-results.php:330
msgid "The aggregate rank is the overall rank for all entries based on the weighted scores for each item."
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "Date Range"
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "Date Range is optional, if no date range is specified it will be ignored."
msgstr ""

#: includes/addon/class-gf-results.php:332
msgid "Filters"
msgstr ""

#: includes/addon/class-gf-results.php:332
msgid "Narrow the results by adding filters. Note that some field types support more options than others."
msgstr ""

#: includes/addon/class-gf-results.php:333
msgid "Average Row Score"
msgstr ""

#: includes/addon/class-gf-results.php:333
msgid "The average (mean) score for each row: the sum of all the scores for each row divided by the total number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:334
msgid "Average Global Score"
msgstr ""

#: includes/addon/class-gf-results.php:334
msgid "The average (mean) score for the whole field. The sum of the total scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:335
#: includes/addon/class-gf-results.php:578
msgid "Average Score"
msgstr ""

#: includes/addon/class-gf-results.php:335
msgid "The average (mean) score: The sum of the scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:363
msgid "No results."
msgstr ""

#: includes/addon/class-gf-results.php:388
msgid "There was an error while processing the entries. Please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:402
msgid "Entries processed: %1$d of %2$d"
msgstr ""

#: includes/addon/class-gf-results.php:419
msgid "No results"
msgstr ""

#: includes/addon/class-gf-results.php:478
#: includes/addon/class-gf-results.php:485
#: includes/addon/class-gf-results.php:500
msgid "No entries for this field"
msgstr ""

#: includes/addon/class-gf-results.php:507
msgid "Choice"
msgstr ""

#: includes/addon/class-gf-results.php:507
#: includes/addon/class-gf-results.php:537
msgid "Frequency"
msgstr ""

#: includes/addon/class-gf-results.php:634
msgid "Average global score"
msgstr ""

#: includes/addon/class-gf-results.php:636
msgid "Average score"
msgstr ""

#: includes/addon/class-gf-results.php:648
msgid "Item"
msgstr ""

#: includes/addon/class-gf-results.php:684
msgid "Latest values:"
msgstr ""

#: includes/addon/class-gf-results.php:692
msgid "Show more"
msgstr ""

#: includes/ajax/class-gf-ajax-handler.php:304
msgid "Your session has expired. Please refresh the page and try again."
msgstr ""

#. Translators: This is used to announce the current step of a multipage form, 1. first step number, 2. total steps number, example: Step 1 of 5
#: includes/ajax/config/class-gf-ajax-config.php:33
msgid "Step %1$s of %2$s, %3$s"
msgstr ""

#: includes/ajax/config/class-gf-ajax-config.php:34
msgid "There was an unknown error processing your request. Please try again."
msgstr ""

#: includes/api.php:136
msgid "Form with id: %s not found"
msgstr ""

#: includes/api.php:216
#: includes/api.php:467
msgid "Invalid form object"
msgstr ""

#: includes/api.php:228
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:207
#: includes/webapi/webapi.php:1352
msgid "Missing form id"
msgstr ""

#: includes/api.php:248
#: includes/api.php:2242
#: includes/webapi/v2/includes/class-results-cache.php:477
#: includes/webapi/v2/includes/controllers/class-controller-form-field-filters.php:46
#: includes/webapi/v2/includes/controllers/class-controller-forms.php:152
msgid "Form not found"
msgstr ""

#: includes/api.php:258
msgid "Error updating form"
msgstr ""

#: includes/api.php:265
msgid "Error updating form confirmations"
msgstr ""

#: includes/api.php:273
msgid "Error updating form notifications"
msgstr ""

#: includes/api.php:284
msgid "Error updating title"
msgstr ""

#: includes/api.php:335
msgid "Property key incorrect"
msgstr ""

#: includes/api.php:414
msgid "Invalid form objects"
msgstr ""

#: includes/api.php:471
msgid "The form title is missing"
msgstr ""

#: includes/api.php:475
msgid "The form fields are missing"
msgstr ""

#: includes/api.php:526
msgid "There was a problem while inserting the form"
msgstr ""

#: includes/api.php:713
#: includes/api.php:724
msgid "Entry with id %s not found"
msgstr ""

#: includes/api.php:846
#: includes/legacy/forms_model_legacy.php:2036
msgid "Missing entry id"
msgstr ""

#: includes/api.php:852
#: includes/legacy/forms_model_legacy.php:2042
msgid "Entry not found"
msgstr ""

#: includes/api.php:867
#: includes/api.php:1242
#: includes/legacy/forms_model_legacy.php:2056
#: includes/legacy/forms_model_legacy.php:2278
msgid "The form for this entry does not exist"
msgstr ""

#: includes/api.php:1019
#: includes/legacy/forms_model_legacy.php:2123
msgid "There was a problem while updating the entry properties"
msgstr ""

#: includes/api.php:1129
#: includes/api.php:1181
#: includes/legacy/forms_model_legacy.php:2163
#: includes/legacy/forms_model_legacy.php:2199
msgid "There was a problem while updating the field values"
msgstr ""

#: includes/api.php:1161
#: includes/legacy/forms_model_legacy.php:2150
msgid "There was a problem while updating one of the input values for the entry"
msgstr ""

#: includes/api.php:1232
#: includes/legacy/forms_model_legacy.php:2268
msgid "The entry object must be an array"
msgstr ""

#: includes/api.php:1238
#: includes/legacy/forms_model_legacy.php:2274
msgid "The form id must be specified"
msgstr ""

#: includes/api.php:1287
#: includes/legacy/forms_model_legacy.php:2318
msgid "There was a problem while inserting the entry properties"
msgstr ""

#: includes/api.php:1353
msgid "Invalid entry id: %s"
msgstr ""

#: includes/api.php:1503
#: includes/api.php:1601
msgid "Note not found"
msgstr ""

#: includes/api.php:1528
msgid "Invalid entry"
msgstr ""

#: includes/api.php:1532
msgid "Invalid or empty note"
msgstr ""

#: includes/api.php:1552
msgid "Invalid note"
msgstr ""

#: includes/api.php:1583
msgid "Invalid note format"
msgstr ""

#: includes/api.php:1595
msgid "Missing note id"
msgstr ""

#: includes/api.php:1703
#: includes/api.php:1712
msgid "There was an error while processing the form:"
msgstr ""

#: includes/api.php:1782
msgid "You must be logged in to use this form."
msgstr ""

#: includes/api.php:1860
msgid "Field not found."
msgstr ""

#: includes/api.php:1865
msgid "Field does not support validation."
msgstr ""

#: includes/api.php:1889
msgid "Your form could not be found"
msgstr ""

#: includes/api.php:1893
msgid "Your form doesn't have any fields."
msgstr ""

#: includes/api.php:2044
msgid "Feed not found"
msgstr ""

#: includes/api.php:2159
msgid "There was an error while deleting feed id %s"
msgstr ""

#: includes/api.php:2253
msgid "There was an error while inserting a feed"
msgstr ""

#: includes/api.php:2284
msgid "The %s table does not exist."
msgstr ""

#: includes/async/class-gf-background-process.php:1313
msgid "Every Minute"
msgstr ""

#: includes/async/class-gf-background-process.php:1315
msgid "Every %d Minutes"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:99
msgid "Accent"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:101
msgid "AJAX"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:103
msgid "Background"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:104
msgid "Border"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:105
msgid "Border Radius"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:106
msgid "Button Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:108
msgid "Card"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:109
msgid "Circle"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:110
#: includes/form-editor/dialog-alert/config/class-gf-dialog-config-i18n.php:33
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:43
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:52
#: includes/template-library/config/class-gf-template-library-config.php:94
msgid "Close"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:111
msgid "Colors"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:112
msgid "Copy / Paste Not Available"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:113
msgid "Copy and paste functionality requires a secure connection. Reload this page using an HTTPS URL and try again."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:114
msgid "Copy Form Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:115
msgid "Custom Colors"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:116
msgid "Default Colors"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:117
msgid "Description Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:119
msgid "Field Values"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:120
msgid "Font Size"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:122
#: includes/embed-form/config/class-gf-embed-config-i18n.php:39
msgid "Form ID: %s"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:124
msgid "Form Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:125
msgid "Form Theme"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:126
msgid "Form style options are not available for forms that use %1$slegacy mode%2$s."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:128
#: settings.php:573
msgid "Gravity Forms 2.5 Theme"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:129
msgid "Image Choice Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:130
msgid "Inherit from default (%s)"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:131
msgid "Input Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:132
msgid "Add Block To Page"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:133
msgid "Click or drag the Gravity Forms Block into the page to insert the form you selected. %1$sLearn More.%2$s"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:134
msgid "In pixels."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:135
msgid "Invalid Form Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:136
msgid "Learn more about configuring your form to use Orbital."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:137
msgid "Label Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:140
msgid "No Card"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:141
#: includes/form-editor/dialog-alert/config/class-gf-dialog-config-i18n.php:34
msgid "OK"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:142
msgid "Orbital Theme"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:143
msgid "Paste Form Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:144
msgid "Paste Not Available"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:145
msgid "Please ensure the form styles you are trying to paste are in the correct format."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:147
msgid "Reset Defaults"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:148
msgid "Restore Defaults"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:149
msgid "Restore Default Styles"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:151
msgid "Select and display one of your forms."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:152
msgid "Show Form Description"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:153
msgid "Show Form Title"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:156
msgid "Style"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:157
msgid "Square"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:158
msgid "Tabindex"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:160
msgid "Theme Colors"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:161
msgid "The accent color is used for aspects such as checkmarks and dropdown choices."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:162
msgid "The background color is used for various form elements, such as buttons and progress bars."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:163
msgid "The selected form has been deleted or trashed. Please select a new form."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:164
msgid "This will restore your form styles back to their default values and cannot be undone. Are you sure you want to continue?"
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:165
msgid "You must have at least one form to use the block."
msgstr ""

#: includes/blocks/config/class-gf-blocks-config.php:166
msgid "Your browser does not have permission to paste from the clipboard. <p>Please navigate to <strong>about:config</strong> and change the preference <strong>dom.events.asyncClipboard.readText</strong> to <strong>true</strong>."
msgstr ""

#: includes/class-confirmation.php:214
msgid "Confirmation Name"
msgstr ""

#: includes/class-confirmation.php:225
msgid "Confirmation Type"
msgstr ""

#: includes/class-confirmation.php:239
#: includes/class-confirmation.php:1191
msgid "Redirect"
msgstr ""

#: includes/class-confirmation.php:247
#: notification.php:433
msgid "Message"
msgstr ""

#: includes/class-confirmation.php:263
msgid "Auto-Formatting"
msgstr ""

#: includes/class-confirmation.php:268
#: notification.php:446
msgid "Disable auto-formatting"
msgstr ""

#: includes/class-confirmation.php:301
msgid "Redirect URL"
msgstr ""

#: includes/class-confirmation.php:317
msgid "You must specify a valid Redirect URL."
msgstr ""

#: includes/class-confirmation.php:325
msgid "Pass Field Data via Query String"
msgstr ""

#: includes/class-confirmation.php:338
msgid "Sample: phone={Phone:1}&email={Email:2}"
msgstr ""

#: includes/class-confirmation.php:346
#: notification.php:472
msgid "Enable conditional logic"
msgstr ""

#: includes/class-confirmation.php:355
msgid "Save Confirmation"
msgstr ""

#: includes/class-confirmation.php:517
msgid "Your confirmation message appears to contain a merge tag as the value for an HTML attribute. Depending on the attribute and field type, this might be a security risk. %sFurther details%s"
msgstr ""

#: includes/class-confirmation.php:535
msgid "Confirmation Settings"
msgstr ""

#: includes/class-confirmation.php:619
msgid "Save &amp; Continue Link"
msgstr ""

#: includes/class-confirmation.php:622
msgid "Save &amp; Continue Token"
msgstr ""

#: includes/class-confirmation.php:628
msgid "Save &amp; Continue Email Input"
msgstr ""

#: includes/class-confirmation.php:1093
msgid "WARNING: You are about to delete this confirmation."
msgstr ""

#: includes/class-confirmation.php:1093
msgid "\\'Cancel\\' to stop, \\'OK\\' to delete."
msgstr ""

#. translators: %s: Confirmation name
#. translators: %s: Notification name
#: includes/class-confirmation.php:1103
#: notification.php:1551
msgid "%s (Edit)"
msgstr ""

#: includes/class-confirmation.php:1154
msgid "<em>This page does not exist.</em>"
msgstr ""

#: includes/class-gf-osdxp.php:73
#: includes/class-gf-osdxp.php:122
msgid "There was an error while validating your license key. Gravity Forms will continue to work, but automatic upgrades will not be available. Please contact support to resolve this issue."
msgstr ""

#: includes/class-gf-osdxp.php:75
msgid "Valid"
msgstr ""

#: includes/class-gf-osdxp.php:78
msgid "Invalid or Expired."
msgstr ""

#: includes/class-gf-osdxp.php:130
msgid "Valid Key : Your license key has been successfully validated."
msgstr ""

#: includes/class-gf-osdxp.php:138
msgid "Invalid Key - an Enterprise license is required."
msgstr ""

#: includes/class-gf-osdxp.php:140
msgid "Invalid or Expired Key - Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/class-gf-osdxp.php:183
msgid "License successfully removed."
msgstr ""

#. translators: 1: version number 2: open link tag 3: closing link tag.
#: includes/class-gf-upgrade.php:326
msgid "Gravity Forms is currently upgrading the database to version %1$s. For sites with a large number of entries this may take a long time. Check the %2$sSystem Status%3$s page for further details."
msgstr ""

#: includes/class-gf-upgrade.php:775
msgid "Queued for upgrade."
msgstr ""

#: includes/class-gf-upgrade.php:877
msgid "Migrating forms."
msgstr ""

#: includes/class-gf-upgrade.php:936
msgid "Forms migrated."
msgstr ""

#: includes/class-gf-upgrade.php:967
msgid "Entry details migrated."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1017
#: includes/class-gf-upgrade.php:1043
msgid "Error Migrating Entry Headers: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1056
msgid "Migrating leads. Step 1/3 Migrating entry headers. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1099
#: includes/class-gf-upgrade.php:1124
msgid "Error Migrating Entry Details: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1138
msgid "Migrating leads. Step 2/3 Migrating entry details. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1184
#: includes/class-gf-upgrade.php:1208
msgid "Error Migrating Entry Meta: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1224
msgid "Migrating leads. Step 3/3 Migrating entry meta. %d rows remaining."
msgstr ""

#: includes/class-gf-upgrade.php:1243
msgid "Migrating incomplete submissions."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1270
msgid "Error Migrating incomplete submissions: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1285
msgid "Migrating entry notes."
msgstr ""

#: includes/class-gf-upgrade.php:1787
msgid "There appears to be an issue with one of the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#: includes/class-gf-upgrade.php:1828
msgid "There appears to be an issue with the data in the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#. translators: %s: the add-on name
#: includes/class-gf-upgrade.php:2194
msgid "The %s is not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#. translators: %d: the number of outdated add-ons
#: includes/class-gf-upgrade.php:2197
msgid "There are %d add-ons installed that are not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#: includes/class-personal-data.php:80
msgid "General Settings"
msgstr ""

#: includes/class-personal-data.php:85
msgid "Prevent the storage of IP addresses during form submission"
msgstr ""

#: includes/class-personal-data.php:91
#: tooltips.php:169
msgid "Retention Policy"
msgstr ""

#: includes/class-personal-data.php:96
msgid "Retain entries indefinitely"
msgstr ""

#: includes/class-personal-data.php:100
msgid "Trash entries automatically"
msgstr ""

#: includes/class-personal-data.php:104
#: includes/class-personal-data.php:112
msgid "Warning: this will affect all entries that are older than the number of days specified."
msgstr ""

#: includes/class-personal-data.php:108
msgid "Delete entries permanently automatically"
msgstr ""

#: includes/class-personal-data.php:119
msgid "Number of days to retain entries before trashing/deleting:"
msgstr ""

#: includes/class-personal-data.php:136
msgid "Form entries must be retained for at least one day."
msgstr ""

#: includes/class-personal-data.php:145
msgid "Exporting and Erasing Data"
msgstr ""

#: includes/class-personal-data.php:150
msgid "Enable integration with the WordPress tools for exporting and erasing personal data."
msgstr ""

#: includes/class-personal-data.php:155
msgid "You must add an email address field to the form in order to enable this setting."
msgstr ""

#: includes/class-personal-data.php:161
msgid "Identification Field"
msgstr ""

#: includes/class-personal-data.php:257
msgid "Created By"
msgstr ""

#: includes/class-personal-data.php:304
msgid "Fields"
msgstr ""

#: includes/class-personal-data.php:305
msgid "Export"
msgstr ""

#: includes/class-personal-data.php:306
msgid "Erase"
msgstr ""

#: includes/class-personal-data.php:324
msgid "Select/Deselect All"
msgstr ""

#: includes/class-personal-data.php:430
msgid "Other Data"
msgstr ""

#: includes/class-personal-data.php:686
msgid "Browser details"
msgstr ""

#: includes/class-personal-data.php:986
msgid "Draft Forms (Save and Continue Later)"
msgstr ""

#. translators: deleted text
#: includes/class-personal-data.php:1295
msgid "[deleted]"
msgstr ""

#: includes/config/class-gf-config-collection.php:61
#: includes/config/class-gf-config.php:244
msgid "Unable to verify nonce. Please refresh the page and try again."
msgstr ""

#: includes/config/class-gf-config-collection.php:78
msgid "Unable to find config: %s"
msgstr ""

#: includes/config/class-gf-config.php:250
msgid "Invalid config."
msgstr ""

#: includes/config/class-gf-config.php:258
msgid "Config validation failed. Hash does not match"
msgstr ""

#: includes/config/items/class-gf-config-admin.php:47
#: includes/embed-form/config/class-gf-embed-config-i18n.php:45
#: includes/templates/edit-shortcode-form.tpl.php:18
msgid "Insert Form"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:28
msgid "Mo"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:29
msgid "Tu"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:30
msgid "We"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:31
msgid "Th"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:32
msgid "Fr"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:33
msgid "Sa"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:34
msgid "Su"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:54
msgid "Select date"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:26
msgid "This type of file is not allowed. Must be one of the following: "
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:27
#: includes/fields/class-gf-field-fileupload.php:452
msgid "Delete this file"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:28
msgid "in progress"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:29
msgid "File exceeds size limit"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:30
msgid "This type of file is not allowed."
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:31
msgid "Maximum number of files reached"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:32
msgid "There was a problem while saving the file on the server"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:33
msgid "Please wait for the uploading to complete"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:35
msgid "Cancel this upload"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:40
msgid "Editor Preferences"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:41
msgid "Close button"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:42
msgid "Change options related to the form editor."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:43
msgid "Compact View"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:44
msgid "Simplify the preview of form fields for a more streamlined editing experience."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:45
msgid "Show Field IDs"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:46
msgid "Show the ID of each field in Compact View."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:57
msgid "Dropdown menu button"
msgstr ""

#: includes/editor-button/dom/class-gf-editor-button.php:22
#: includes/editor-button/dom/class-gf-editor-button.php:23
msgid "Open editor preferences"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:38
msgid "Embed Form"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:40
msgid "Add to Existing Content"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:41
msgid "%1$sAdd to Existing Content:%2$s %3$s"
msgstr ""

#. Translators: singular post type name (e.g. 'post').
#: includes/embed-form/config/class-gf-embed-config-i18n.php:42
#: includes/settings/fields/class-post-select.php:139
msgid "Select a %s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:43
msgid "Select a post"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:44
msgid "Search all %ss"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:46
msgid "Create New"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:47
msgid "%1$sCreate New:%2$s %3$s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:48
msgid "Enter %s Name"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:49
msgid "Create"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:50
msgid "Unsaved Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:51
msgid "Oops! You have unsaved changes in the form, before you can continue with embedding it please save your changes."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:52
msgid "Save Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:55
msgid "Close this dialog and return to form editor."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:56
msgid "Not Using the Block Editor?"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:57
msgid "Copy and paste the shortcode within your page builder."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:58
msgid "Copy Shortcode"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:59
msgid "Copied"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:60
msgid "%1$sLearn more%2$s about the shortcode."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:61
msgid "Opens in a new tab"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config.php:180
msgid "Post"
msgstr ""

#: includes/embed-form/dom/class-gf-embed-button.php:21
msgid "Embed"
msgstr ""

#: includes/fields/class-gf-field-address.php:48
msgid "Allows users to enter a physical address."
msgstr ""

#: includes/fields/class-gf-field-address.php:183
msgid "Zip Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:205
#: includes/template-library/templates/templates.php:530
#: includes/template-library/templates/templates.php:2493
#: includes/template-library/templates/templates.php:2580
#: includes/template-library/templates/templates.php:3310
#: includes/template-library/templates/templates.php:3397
#: includes/template-library/templates/templates.php:4116
#: includes/template-library/templates/templates.php:4203
#: includes/template-library/templates/templates.php:4807
#: includes/template-library/templates/templates.php:5976
#: includes/template-library/templates/templates.php:7121
#: includes/template-library/templates/templates.php:7995
#: js.php:830
msgid "Street Address"
msgstr ""

#: includes/fields/class-gf-field-address.php:207
#: includes/template-library/templates/templates.php:536
#: includes/template-library/templates/templates.php:2499
#: includes/template-library/templates/templates.php:2586
#: includes/template-library/templates/templates.php:3316
#: includes/template-library/templates/templates.php:3403
#: includes/template-library/templates/templates.php:4122
#: includes/template-library/templates/templates.php:4209
#: includes/template-library/templates/templates.php:4813
#: includes/template-library/templates/templates.php:5982
#: includes/template-library/templates/templates.php:7126
#: includes/template-library/templates/templates.php:8001
#: js.php:835
msgid "Address Line 2"
msgstr ""

#: includes/fields/class-gf-field-address.php:466
#: includes/fields/class-gf-field-phone.php:339
msgid "International"
msgstr ""

#: includes/fields/class-gf-field-address.php:467
#: includes/template-library/templates/templates.php:2517
#: includes/template-library/templates/templates.php:2604
#: includes/template-library/templates/templates.php:3334
#: includes/template-library/templates/templates.php:3421
#: includes/template-library/templates/templates.php:4140
#: includes/template-library/templates/templates.php:4227
#: includes/template-library/templates/templates.php:4831
#: includes/template-library/templates/templates.php:6000
#: includes/template-library/templates/templates.php:7141
#: includes/template-library/templates/templates.php:8019
#: js.php:850
msgid "ZIP / Postal Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:468
msgid "State / Province / Region"
msgstr ""

#: includes/fields/class-gf-field-address.php:471
#: includes/fields/class-gf-field-address.php:853
msgid "United States"
msgstr ""

#: includes/fields/class-gf-field-address.php:472
msgid "ZIP Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:478
msgid "Canadian"
msgstr ""

#: includes/fields/class-gf-field-address.php:480
msgid "Province"
msgstr ""

#: includes/fields/class-gf-field-address.php:618
msgid "Afghanistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:619
msgid "Åland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:620
msgid "Albania"
msgstr ""

#: includes/fields/class-gf-field-address.php:621
msgid "Algeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:622
#: includes/fields/class-gf-field-address.php:919
#: includes/fields/class-gf-field-address.php:993
msgid "American Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:623
msgid "Andorra"
msgstr ""

#: includes/fields/class-gf-field-address.php:624
msgid "Angola"
msgstr ""

#: includes/fields/class-gf-field-address.php:625
msgid "Anguilla"
msgstr ""

#: includes/fields/class-gf-field-address.php:627
msgid "Antigua and Barbuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:628
msgid "Argentina"
msgstr ""

#: includes/fields/class-gf-field-address.php:629
msgid "Armenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:630
msgid "Aruba"
msgstr ""

#: includes/fields/class-gf-field-address.php:632
msgid "Austria"
msgstr ""

#: includes/fields/class-gf-field-address.php:633
msgid "Azerbaijan"
msgstr ""

#: includes/fields/class-gf-field-address.php:634
msgid "Bahamas"
msgstr ""

#: includes/fields/class-gf-field-address.php:635
msgid "Bahrain"
msgstr ""

#: includes/fields/class-gf-field-address.php:636
msgid "Bangladesh"
msgstr ""

#: includes/fields/class-gf-field-address.php:637
msgid "Barbados"
msgstr ""

#: includes/fields/class-gf-field-address.php:638
msgid "Belarus"
msgstr ""

#: includes/fields/class-gf-field-address.php:639
msgid "Belgium"
msgstr ""

#: includes/fields/class-gf-field-address.php:640
msgid "Belize"
msgstr ""

#: includes/fields/class-gf-field-address.php:641
msgid "Benin"
msgstr ""

#: includes/fields/class-gf-field-address.php:642
msgid "Bermuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:643
msgid "Bhutan"
msgstr ""

#: includes/fields/class-gf-field-address.php:644
msgid "Bolivia"
msgstr ""

#: includes/fields/class-gf-field-address.php:645
msgid "Bonaire, Sint Eustatius and Saba"
msgstr ""

#: includes/fields/class-gf-field-address.php:646
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/fields/class-gf-field-address.php:647
msgid "Botswana"
msgstr ""

#: includes/fields/class-gf-field-address.php:648
msgid "Bouvet Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:649
msgid "Brazil"
msgstr ""

#: includes/fields/class-gf-field-address.php:650
msgid "British Indian Ocean Territory"
msgstr ""

#: includes/fields/class-gf-field-address.php:651
msgid "Brunei Darussalam"
msgstr ""

#: includes/fields/class-gf-field-address.php:652
msgid "Bulgaria"
msgstr ""

#: includes/fields/class-gf-field-address.php:653
msgid "Burkina Faso"
msgstr ""

#: includes/fields/class-gf-field-address.php:654
msgid "Burundi"
msgstr ""

#: includes/fields/class-gf-field-address.php:655
msgid "Cabo Verde"
msgstr ""

#: includes/fields/class-gf-field-address.php:656
msgid "Cambodia"
msgstr ""

#: includes/fields/class-gf-field-address.php:657
msgid "Cameroon"
msgstr ""

#: includes/fields/class-gf-field-address.php:658
msgid "Canada"
msgstr ""

#: includes/fields/class-gf-field-address.php:659
msgid "Cayman Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:660
msgid "Central African Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:661
msgid "Chad"
msgstr ""

#: includes/fields/class-gf-field-address.php:662
msgid "Chile"
msgstr ""

#: includes/fields/class-gf-field-address.php:663
msgid "China"
msgstr ""

#: includes/fields/class-gf-field-address.php:664
msgid "Christmas Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:665
msgid "Cocos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:666
msgid "Colombia"
msgstr ""

#: includes/fields/class-gf-field-address.php:667
msgid "Comoros"
msgstr ""

#: includes/fields/class-gf-field-address.php:668
msgid "Congo, Democratic Republic of the"
msgstr ""

#: includes/fields/class-gf-field-address.php:669
msgid "Congo"
msgstr ""

#: includes/fields/class-gf-field-address.php:670
msgid "Cook Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:671
msgid "Costa Rica"
msgstr ""

#: includes/fields/class-gf-field-address.php:672
msgid "Côte d'Ivoire"
msgstr ""

#: includes/fields/class-gf-field-address.php:673
msgid "Croatia"
msgstr ""

#: includes/fields/class-gf-field-address.php:674
msgid "Cuba"
msgstr ""

#: includes/fields/class-gf-field-address.php:675
msgid "Curaçao"
msgstr ""

#: includes/fields/class-gf-field-address.php:676
msgid "Cyprus"
msgstr ""

#: includes/fields/class-gf-field-address.php:677
msgid "Czechia"
msgstr ""

#: includes/fields/class-gf-field-address.php:678
msgid "Denmark"
msgstr ""

#: includes/fields/class-gf-field-address.php:679
msgid "Djibouti"
msgstr ""

#: includes/fields/class-gf-field-address.php:680
msgid "Dominica"
msgstr ""

#: includes/fields/class-gf-field-address.php:681
msgid "Dominican Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:682
msgid "Ecuador"
msgstr ""

#: includes/fields/class-gf-field-address.php:683
msgid "Egypt"
msgstr ""

#: includes/fields/class-gf-field-address.php:684
msgid "El Salvador"
msgstr ""

#: includes/fields/class-gf-field-address.php:685
msgid "Equatorial Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:686
msgid "Eritrea"
msgstr ""

#: includes/fields/class-gf-field-address.php:687
msgid "Estonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:688
msgid "Eswatini"
msgstr ""

#: includes/fields/class-gf-field-address.php:689
msgid "Ethiopia"
msgstr ""

#: includes/fields/class-gf-field-address.php:690
msgid "Falkland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:691
msgid "Faroe Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:692
msgid "Fiji"
msgstr ""

#: includes/fields/class-gf-field-address.php:693
msgid "Finland"
msgstr ""

#: includes/fields/class-gf-field-address.php:694
msgid "France"
msgstr ""

#: includes/fields/class-gf-field-address.php:695
msgid "French Guiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:696
msgid "French Polynesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:697
msgid "French Southern Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:698
msgid "Gabon"
msgstr ""

#: includes/fields/class-gf-field-address.php:699
msgid "Gambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:700
msgctxt "Country"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:701
msgid "Germany"
msgstr ""

#: includes/fields/class-gf-field-address.php:702
msgid "Ghana"
msgstr ""

#: includes/fields/class-gf-field-address.php:703
msgid "Gibraltar"
msgstr ""

#: includes/fields/class-gf-field-address.php:704
msgid "Greece"
msgstr ""

#: includes/fields/class-gf-field-address.php:705
msgid "Greenland"
msgstr ""

#: includes/fields/class-gf-field-address.php:706
msgid "Grenada"
msgstr ""

#: includes/fields/class-gf-field-address.php:707
msgid "Guadeloupe"
msgstr ""

#: includes/fields/class-gf-field-address.php:708
#: includes/fields/class-gf-field-address.php:929
#: includes/fields/class-gf-field-address.php:1003
msgid "Guam"
msgstr ""

#: includes/fields/class-gf-field-address.php:709
msgid "Guatemala"
msgstr ""

#: includes/fields/class-gf-field-address.php:710
msgid "Guernsey"
msgstr ""

#: includes/fields/class-gf-field-address.php:711
msgid "Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:712
msgid "Guinea-Bissau"
msgstr ""

#: includes/fields/class-gf-field-address.php:713
msgid "Guyana"
msgstr ""

#: includes/fields/class-gf-field-address.php:714
msgid "Haiti"
msgstr ""

#: includes/fields/class-gf-field-address.php:715
msgid "Heard Island and McDonald Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:716
msgid "Holy See"
msgstr ""

#: includes/fields/class-gf-field-address.php:717
msgid "Honduras"
msgstr ""

#: includes/fields/class-gf-field-address.php:718
msgid "Hong Kong"
msgstr ""

#: includes/fields/class-gf-field-address.php:719
msgid "Hungary"
msgstr ""

#: includes/fields/class-gf-field-address.php:720
msgid "Iceland"
msgstr ""

#: includes/fields/class-gf-field-address.php:721
msgid "India"
msgstr ""

#: includes/fields/class-gf-field-address.php:722
msgid "Indonesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:723
msgid "Iran"
msgstr ""

#: includes/fields/class-gf-field-address.php:724
msgid "Iraq"
msgstr ""

#: includes/fields/class-gf-field-address.php:725
msgid "Ireland"
msgstr ""

#: includes/fields/class-gf-field-address.php:726
msgid "Isle of Man"
msgstr ""

#: includes/fields/class-gf-field-address.php:727
msgid "Israel"
msgstr ""

#: includes/fields/class-gf-field-address.php:728
msgid "Italy"
msgstr ""

#: includes/fields/class-gf-field-address.php:729
msgid "Jamaica"
msgstr ""

#: includes/fields/class-gf-field-address.php:730
msgid "Japan"
msgstr ""

#: includes/fields/class-gf-field-address.php:731
msgid "Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:732
msgid "Jordan"
msgstr ""

#: includes/fields/class-gf-field-address.php:733
msgid "Kazakhstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:734
msgid "Kenya"
msgstr ""

#: includes/fields/class-gf-field-address.php:735
msgid "Kiribati"
msgstr ""

#: includes/fields/class-gf-field-address.php:736
msgid "Korea, Democratic People's Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:737
msgid "Korea, Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:738
msgid "Kuwait"
msgstr ""

#: includes/fields/class-gf-field-address.php:739
msgid "Kyrgyzstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:740
msgid "Lao People's Democratic Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:741
msgid "Latvia"
msgstr ""

#: includes/fields/class-gf-field-address.php:742
msgid "Lebanon"
msgstr ""

#: includes/fields/class-gf-field-address.php:743
msgid "Lesotho"
msgstr ""

#: includes/fields/class-gf-field-address.php:744
msgid "Liberia"
msgstr ""

#: includes/fields/class-gf-field-address.php:745
msgid "Libya"
msgstr ""

#: includes/fields/class-gf-field-address.php:746
msgid "Liechtenstein"
msgstr ""

#: includes/fields/class-gf-field-address.php:747
msgid "Lithuania"
msgstr ""

#: includes/fields/class-gf-field-address.php:748
msgid "Luxembourg"
msgstr ""

#: includes/fields/class-gf-field-address.php:749
msgid "Macao"
msgstr ""

#: includes/fields/class-gf-field-address.php:750
msgid "Madagascar"
msgstr ""

#: includes/fields/class-gf-field-address.php:751
msgid "Malawi"
msgstr ""

#: includes/fields/class-gf-field-address.php:752
msgid "Malaysia"
msgstr ""

#: includes/fields/class-gf-field-address.php:753
msgid "Maldives"
msgstr ""

#: includes/fields/class-gf-field-address.php:754
msgid "Mali"
msgstr ""

#: includes/fields/class-gf-field-address.php:755
msgid "Malta"
msgstr ""

#: includes/fields/class-gf-field-address.php:756
msgid "Marshall Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:757
msgid "Martinique"
msgstr ""

#: includes/fields/class-gf-field-address.php:758
msgid "Mauritania"
msgstr ""

#: includes/fields/class-gf-field-address.php:759
msgid "Mauritius"
msgstr ""

#: includes/fields/class-gf-field-address.php:760
msgid "Mayotte"
msgstr ""

#: includes/fields/class-gf-field-address.php:761
msgid "Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:762
msgid "Micronesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:763
msgid "Moldova"
msgstr ""

#: includes/fields/class-gf-field-address.php:764
msgid "Monaco"
msgstr ""

#: includes/fields/class-gf-field-address.php:765
msgid "Mongolia"
msgstr ""

#: includes/fields/class-gf-field-address.php:766
msgid "Montenegro"
msgstr ""

#: includes/fields/class-gf-field-address.php:767
msgid "Montserrat"
msgstr ""

#: includes/fields/class-gf-field-address.php:768
msgid "Morocco"
msgstr ""

#: includes/fields/class-gf-field-address.php:769
msgid "Mozambique"
msgstr ""

#: includes/fields/class-gf-field-address.php:770
msgid "Myanmar"
msgstr ""

#: includes/fields/class-gf-field-address.php:771
msgid "Namibia"
msgstr ""

#: includes/fields/class-gf-field-address.php:772
msgid "Nauru"
msgstr ""

#: includes/fields/class-gf-field-address.php:773
msgid "Nepal"
msgstr ""

#: includes/fields/class-gf-field-address.php:774
msgid "Netherlands"
msgstr ""

#: includes/fields/class-gf-field-address.php:775
msgid "New Caledonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:776
msgid "New Zealand"
msgstr ""

#: includes/fields/class-gf-field-address.php:777
msgid "Nicaragua"
msgstr ""

#: includes/fields/class-gf-field-address.php:778
msgid "Niger"
msgstr ""

#: includes/fields/class-gf-field-address.php:779
msgid "Nigeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:780
msgid "Niue"
msgstr ""

#: includes/fields/class-gf-field-address.php:781
msgid "Norfolk Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:782
msgid "North Macedonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:783
#: includes/fields/class-gf-field-address.php:954
#: includes/fields/class-gf-field-address.php:1028
msgid "Northern Mariana Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:784
msgid "Norway"
msgstr ""

#: includes/fields/class-gf-field-address.php:785
msgid "Oman"
msgstr ""

#: includes/fields/class-gf-field-address.php:786
msgid "Pakistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:787
msgid "Palau"
msgstr ""

#: includes/fields/class-gf-field-address.php:788
msgid "Palestine, State of"
msgstr ""

#: includes/fields/class-gf-field-address.php:789
msgid "Panama"
msgstr ""

#: includes/fields/class-gf-field-address.php:790
msgid "Papua New Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:791
msgid "Paraguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:792
msgid "Peru"
msgstr ""

#: includes/fields/class-gf-field-address.php:793
msgid "Philippines"
msgstr ""

#: includes/fields/class-gf-field-address.php:794
msgid "Pitcairn"
msgstr ""

#: includes/fields/class-gf-field-address.php:795
msgid "Poland"
msgstr ""

#: includes/fields/class-gf-field-address.php:796
msgid "Portugal"
msgstr ""

#: includes/fields/class-gf-field-address.php:797
#: includes/fields/class-gf-field-address.php:959
#: includes/fields/class-gf-field-address.php:1033
msgid "Puerto Rico"
msgstr ""

#: includes/fields/class-gf-field-address.php:798
msgid "Qatar"
msgstr ""

#: includes/fields/class-gf-field-address.php:799
msgid "Réunion"
msgstr ""

#: includes/fields/class-gf-field-address.php:800
msgid "Romania"
msgstr ""

#: includes/fields/class-gf-field-address.php:801
msgid "Russian Federation"
msgstr ""

#: includes/fields/class-gf-field-address.php:802
msgid "Rwanda"
msgstr ""

#: includes/fields/class-gf-field-address.php:803
msgid "Saint Barthélemy"
msgstr ""

#: includes/fields/class-gf-field-address.php:804
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr ""

#: includes/fields/class-gf-field-address.php:805
msgid "Saint Kitts and Nevis"
msgstr ""

#: includes/fields/class-gf-field-address.php:806
msgid "Saint Lucia"
msgstr ""

#: includes/fields/class-gf-field-address.php:807
msgid "Saint Martin"
msgstr ""

#: includes/fields/class-gf-field-address.php:808
msgid "Saint Pierre and Miquelon"
msgstr ""

#: includes/fields/class-gf-field-address.php:809
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: includes/fields/class-gf-field-address.php:810
msgid "Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:811
msgid "San Marino"
msgstr ""

#: includes/fields/class-gf-field-address.php:812
msgid "Sao Tome and Principe"
msgstr ""

#: includes/fields/class-gf-field-address.php:813
msgid "Saudi Arabia"
msgstr ""

#: includes/fields/class-gf-field-address.php:814
msgid "Senegal"
msgstr ""

#: includes/fields/class-gf-field-address.php:815
msgid "Serbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:816
msgid "Seychelles"
msgstr ""

#: includes/fields/class-gf-field-address.php:817
msgid "Sierra Leone"
msgstr ""

#: includes/fields/class-gf-field-address.php:818
msgid "Singapore"
msgstr ""

#: includes/fields/class-gf-field-address.php:819
msgid "Sint Maarten"
msgstr ""

#: includes/fields/class-gf-field-address.php:820
msgid "Slovakia"
msgstr ""

#: includes/fields/class-gf-field-address.php:821
msgid "Slovenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:822
msgid "Solomon Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:823
msgid "Somalia"
msgstr ""

#: includes/fields/class-gf-field-address.php:824
msgid "South Africa"
msgstr ""

#: includes/fields/class-gf-field-address.php:825
msgctxt "Country"
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:826
msgid "South Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:827
msgid "Spain"
msgstr ""

#: includes/fields/class-gf-field-address.php:828
msgid "Sri Lanka"
msgstr ""

#: includes/fields/class-gf-field-address.php:829
msgid "Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:830
msgid "Suriname"
msgstr ""

#: includes/fields/class-gf-field-address.php:831
msgid "Svalbard and Jan Mayen"
msgstr ""

#: includes/fields/class-gf-field-address.php:832
msgid "Sweden"
msgstr ""

#: includes/fields/class-gf-field-address.php:833
msgid "Switzerland"
msgstr ""

#: includes/fields/class-gf-field-address.php:834
msgid "Syria Arab Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:835
msgid "Taiwan"
msgstr ""

#: includes/fields/class-gf-field-address.php:836
msgid "Tajikistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:837
msgid "Tanzania, the United Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:838
msgid "Thailand"
msgstr ""

#: includes/fields/class-gf-field-address.php:839
msgid "Timor-Leste"
msgstr ""

#: includes/fields/class-gf-field-address.php:840
msgid "Togo"
msgstr ""

#: includes/fields/class-gf-field-address.php:841
msgid "Tokelau"
msgstr ""

#: includes/fields/class-gf-field-address.php:842
msgid "Tonga"
msgstr ""

#: includes/fields/class-gf-field-address.php:843
msgid "Trinidad and Tobago"
msgstr ""

#: includes/fields/class-gf-field-address.php:844
msgid "Tunisia"
msgstr ""

#: includes/fields/class-gf-field-address.php:845
msgid "Türkiye"
msgstr ""

#: includes/fields/class-gf-field-address.php:846
msgid "Turkmenistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:847
msgid "Turks and Caicos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:848
msgid "Tuvalu"
msgstr ""

#: includes/fields/class-gf-field-address.php:849
msgid "Uganda"
msgstr ""

#: includes/fields/class-gf-field-address.php:850
msgid "Ukraine"
msgstr ""

#: includes/fields/class-gf-field-address.php:851
msgid "United Arab Emirates"
msgstr ""

#: includes/fields/class-gf-field-address.php:852
msgid "United Kingdom"
msgstr ""

#: includes/fields/class-gf-field-address.php:854
msgid "Uruguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:855
msgid "US Minor Outlying Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:856
msgid "Uzbekistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:857
msgid "Vanuatu"
msgstr ""

#: includes/fields/class-gf-field-address.php:858
msgid "Venezuela"
msgstr ""

#: includes/fields/class-gf-field-address.php:859
msgid "Viet Nam"
msgstr ""

#: includes/fields/class-gf-field-address.php:860
msgid "Virgin Islands, British"
msgstr ""

#: includes/fields/class-gf-field-address.php:861
msgid "Virgin Islands, U.S."
msgstr ""

#: includes/fields/class-gf-field-address.php:862
msgid "Wallis and Futuna"
msgstr ""

#: includes/fields/class-gf-field-address.php:863
msgid "Western Sahara"
msgstr ""

#: includes/fields/class-gf-field-address.php:864
msgid "Yemen"
msgstr ""

#: includes/fields/class-gf-field-address.php:865
msgid "Zambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:866
msgid "Zimbabwe"
msgstr ""

#: includes/fields/class-gf-field-address.php:917
#: includes/fields/class-gf-field-address.php:991
msgid "Alabama"
msgstr ""

#: includes/fields/class-gf-field-address.php:918
#: includes/fields/class-gf-field-address.php:992
msgid "Alaska"
msgstr ""

#: includes/fields/class-gf-field-address.php:920
#: includes/fields/class-gf-field-address.php:994
msgid "Arizona"
msgstr ""

#: includes/fields/class-gf-field-address.php:921
#: includes/fields/class-gf-field-address.php:995
msgid "Arkansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:922
#: includes/fields/class-gf-field-address.php:996
msgid "California"
msgstr ""

#: includes/fields/class-gf-field-address.php:923
#: includes/fields/class-gf-field-address.php:997
msgid "Colorado"
msgstr ""

#: includes/fields/class-gf-field-address.php:924
#: includes/fields/class-gf-field-address.php:998
msgid "Connecticut"
msgstr ""

#: includes/fields/class-gf-field-address.php:925
#: includes/fields/class-gf-field-address.php:999
msgid "Delaware"
msgstr ""

#: includes/fields/class-gf-field-address.php:926
#: includes/fields/class-gf-field-address.php:1000
msgid "District of Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:927
#: includes/fields/class-gf-field-address.php:1001
msgid "Florida"
msgstr ""

#: includes/fields/class-gf-field-address.php:928
#: includes/fields/class-gf-field-address.php:1002
msgctxt "US State"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:930
#: includes/fields/class-gf-field-address.php:1004
msgid "Hawaii"
msgstr ""

#: includes/fields/class-gf-field-address.php:931
#: includes/fields/class-gf-field-address.php:1005
msgid "Idaho"
msgstr ""

#: includes/fields/class-gf-field-address.php:932
#: includes/fields/class-gf-field-address.php:1006
msgid "Illinois"
msgstr ""

#: includes/fields/class-gf-field-address.php:933
#: includes/fields/class-gf-field-address.php:1007
msgid "Indiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:934
#: includes/fields/class-gf-field-address.php:1008
msgid "Iowa"
msgstr ""

#: includes/fields/class-gf-field-address.php:935
#: includes/fields/class-gf-field-address.php:1009
msgid "Kansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:936
#: includes/fields/class-gf-field-address.php:1010
msgid "Kentucky"
msgstr ""

#: includes/fields/class-gf-field-address.php:937
#: includes/fields/class-gf-field-address.php:1011
msgid "Louisiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:938
#: includes/fields/class-gf-field-address.php:1012
msgid "Maine"
msgstr ""

#: includes/fields/class-gf-field-address.php:939
#: includes/fields/class-gf-field-address.php:1013
msgid "Maryland"
msgstr ""

#: includes/fields/class-gf-field-address.php:940
#: includes/fields/class-gf-field-address.php:1014
msgid "Massachusetts"
msgstr ""

#: includes/fields/class-gf-field-address.php:941
#: includes/fields/class-gf-field-address.php:1015
msgid "Michigan"
msgstr ""

#: includes/fields/class-gf-field-address.php:942
#: includes/fields/class-gf-field-address.php:1016
msgid "Minnesota"
msgstr ""

#: includes/fields/class-gf-field-address.php:943
#: includes/fields/class-gf-field-address.php:1017
msgid "Mississippi"
msgstr ""

#: includes/fields/class-gf-field-address.php:944
#: includes/fields/class-gf-field-address.php:1018
msgid "Missouri"
msgstr ""

#: includes/fields/class-gf-field-address.php:945
#: includes/fields/class-gf-field-address.php:1019
msgid "Montana"
msgstr ""

#: includes/fields/class-gf-field-address.php:946
#: includes/fields/class-gf-field-address.php:1020
msgid "Nebraska"
msgstr ""

#: includes/fields/class-gf-field-address.php:947
#: includes/fields/class-gf-field-address.php:1021
msgid "Nevada"
msgstr ""

#: includes/fields/class-gf-field-address.php:948
#: includes/fields/class-gf-field-address.php:1022
msgid "New Hampshire"
msgstr ""

#: includes/fields/class-gf-field-address.php:949
#: includes/fields/class-gf-field-address.php:1023
msgid "New Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:950
#: includes/fields/class-gf-field-address.php:1024
msgid "New Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:951
#: includes/fields/class-gf-field-address.php:1025
msgid "New York"
msgstr ""

#: includes/fields/class-gf-field-address.php:952
#: includes/fields/class-gf-field-address.php:1026
msgid "North Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:953
#: includes/fields/class-gf-field-address.php:1027
msgid "North Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:955
#: includes/fields/class-gf-field-address.php:1029
msgid "Ohio"
msgstr ""

#: includes/fields/class-gf-field-address.php:956
#: includes/fields/class-gf-field-address.php:1030
msgid "Oklahoma"
msgstr ""

#: includes/fields/class-gf-field-address.php:957
#: includes/fields/class-gf-field-address.php:1031
msgid "Oregon"
msgstr ""

#: includes/fields/class-gf-field-address.php:958
#: includes/fields/class-gf-field-address.php:1032
msgid "Pennsylvania"
msgstr ""

#: includes/fields/class-gf-field-address.php:960
#: includes/fields/class-gf-field-address.php:1034
msgid "Rhode Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:961
#: includes/fields/class-gf-field-address.php:1035
msgid "South Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:962
#: includes/fields/class-gf-field-address.php:1036
msgid "South Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:963
#: includes/fields/class-gf-field-address.php:1037
msgid "Tennessee"
msgstr ""

#: includes/fields/class-gf-field-address.php:964
#: includes/fields/class-gf-field-address.php:1038
msgid "Texas"
msgstr ""

#: includes/fields/class-gf-field-address.php:965
#: includes/fields/class-gf-field-address.php:1039
msgid "Utah"
msgstr ""

#: includes/fields/class-gf-field-address.php:966
#: includes/fields/class-gf-field-address.php:1040
msgid "U.S. Virgin Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:967
#: includes/fields/class-gf-field-address.php:1041
msgid "Vermont"
msgstr ""

#: includes/fields/class-gf-field-address.php:968
#: includes/fields/class-gf-field-address.php:1042
msgid "Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:969
#: includes/fields/class-gf-field-address.php:1043
msgid "Washington"
msgstr ""

#: includes/fields/class-gf-field-address.php:970
#: includes/fields/class-gf-field-address.php:1044
msgid "West Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:971
#: includes/fields/class-gf-field-address.php:1045
msgid "Wisconsin"
msgstr ""

#: includes/fields/class-gf-field-address.php:972
#: includes/fields/class-gf-field-address.php:1046
msgid "Wyoming"
msgstr ""

#: includes/fields/class-gf-field-address.php:973
#: includes/fields/class-gf-field-address.php:1047
msgid "Armed Forces Americas"
msgstr ""

#: includes/fields/class-gf-field-address.php:974
#: includes/fields/class-gf-field-address.php:1048
msgid "Armed Forces Europe"
msgstr ""

#: includes/fields/class-gf-field-address.php:975
#: includes/fields/class-gf-field-address.php:1049
msgid "Armed Forces Pacific"
msgstr ""

#: includes/fields/class-gf-field-address.php:1060
msgid "Alberta"
msgstr ""

#: includes/fields/class-gf-field-address.php:1061
msgid "British Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1062
msgid "Manitoba"
msgstr ""

#: includes/fields/class-gf-field-address.php:1063
msgid "New Brunswick"
msgstr ""

#: includes/fields/class-gf-field-address.php:1064
msgid "Newfoundland and Labrador"
msgstr ""

#: includes/fields/class-gf-field-address.php:1065
msgid "Northwest Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:1066
msgid "Nova Scotia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1067
msgid "Nunavut"
msgstr ""

#: includes/fields/class-gf-field-address.php:1068
msgid "Ontario"
msgstr ""

#: includes/fields/class-gf-field-address.php:1069
msgid "Prince Edward Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:1070
msgid "Quebec"
msgstr ""

#: includes/fields/class-gf-field-address.php:1071
msgid "Saskatchewan"
msgstr ""

#: includes/fields/class-gf-field-address.php:1072
msgid "Yukon"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:35
#: includes/fields/class-gf-field-hiddenproduct.php:53
#: includes/fields/class-gf-field-number.php:132
#: includes/fields/class-gf-field-singleproduct.php:60
msgid "Please enter a valid quantity"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:149
#: includes/fields/class-gf-field-hiddenproduct.php:116
#: includes/fields/class-gf-field-singleproduct.php:217
msgid "Qty: "
msgstr ""

#: includes/fields/class-gf-field-calculation.php:149
#: includes/fields/class-gf-field-hiddenproduct.php:120
#: includes/fields/class-gf-field-singleproduct.php:221
msgid "Price: "
msgstr ""

#: includes/fields/class-gf-field-captcha.php:81
#: js.php:971
msgid "CAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:92
msgid "Adds a captcha field to your form to help protect your website from spam and bot abuse."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:146
msgid "The reCAPTCHA v2 field is not supported in Conversational Forms and will be removed, but will continue to work as expected in other contexts."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:148
msgid "This field is not supported in Conversational Forms"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:160
#: includes/fields/class-gf-field-captcha.php:477
msgid "Configuration Required"
msgstr ""

#. Translators: 1. Opening <a> tag with link to the Forms > Settings > reCAPTCHA page. 2. closing <a> tag.
#: includes/fields/class-gf-field-captcha.php:163
msgid "To use the reCAPTCHA field, please configure your %1$sreCAPTCHA settings.%2$s"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:168
msgid "This field requires additional configuration"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:216
#: includes/fields/class-gf-field-captcha.php:252
msgid "The CAPTCHA wasn't entered correctly. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:293
msgid "The reCAPTCHA was invalid. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:480
msgid "To use the reCAPTCHA field, please configure your"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:482
msgid "reCAPTCHA settings"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:492
msgid "An example of reCAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:49
msgid "Allows users to select one or many checkboxes."
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:272
msgid "Select exactly %s choice."
msgid_plural "Select exactly %s choices."
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-gf-field-checkbox.php:299
msgid "Select up to %s choice."
msgid_plural "Select up to %s choices."
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-gf-field-checkbox.php:323
msgid "Select at least %s choice."
msgid_plural "Select at least %s choices."
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-gf-field-checkbox.php:345
msgid "Select between %s and %s choices."
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:1060
#: includes/fields/class-gf-field-radio.php:202
#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice-checkbox-markup.php:142
#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice-radio-markup.php:86
msgid "%d of %d items shown. Edit choices to view all."
msgstr ""

#: includes/fields/class-gf-field-consent.php:90
#: includes/template-library/templates/templates.php:1492
#: includes/template-library/templates/templates.php:5534
#: includes/template-library/templates/templates.php:10206
#: js.php:1075
#: js.php:1076
msgid "Consent"
msgstr ""

#: includes/fields/class-gf-field-consent.php:101
msgid "Offers a “yes/no” consent checkbox and a detailed description of what is being consented to."
msgstr ""

#: includes/fields/class-gf-field-consent.php:480
#: includes/fields/class-gf-field-consent.php:551
#: includes/template-library/templates/templates.php:1512
#: includes/template-library/templates/templates.php:5554
#: includes/template-library/templates/templates.php:10226
#: js.php:1086
msgid "Checked"
msgstr ""

#: includes/fields/class-gf-field-consent.php:480
msgid "Not Checked"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:13
#: includes/template-library/templates/templates.php:2775
#: includes/template-library/templates/templates.php:3586
#: js.php:863
msgid "Credit Card"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:24
msgid "Allows users to enter credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:112
msgid "Please enter your credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:118
msgid "Please enter your card's security code."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:121
msgid "Invalid credit card number."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:124
msgid "is not supported. Please enter one of the supported credit cards."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:199
msgid "This page is unsecured. Do not enter a real credit card number! Use this field only for testing purposes. "
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:288
msgid "Supported Credit Cards:"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:303
#: includes/fields/class-gf-field-creditcard.php:397
msgid "Only digits are allowed"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:304
#: includes/template-library/templates/templates.php:4401
#: js.php:866
msgid "Card Number"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:328
#: includes/template-library/templates/templates.php:4406
#: js.php:868
#: js.php:1228
msgid "Expiration Date"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:345
#: includes/fields/class-gf-field-creditcard.php:484
#: includes/fields/class-gf-field-date.php:803
#: includes/fields/class-gf-field-date.php:999
#: includes/fields/class-gf-field-date.php:1157
#: js.php:338
#: js.php:1162
#: js.php:1169
#: js.php:1170
msgid "Year"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:395
#: includes/template-library/templates/templates.php:4411
#: js.php:870
msgid "Security Code"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:417
#: includes/template-library/templates/templates.php:3605
#: includes/template-library/templates/templates.php:4421
#: js.php:872
#: js.php:1232
msgid "Cardholder Name"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:611
#: js.php:867
#: js.php:1227
msgid "Expiration Month"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:616
#: js.php:869
#: js.php:1229
msgid "Expiration Year"
msgstr ""

#: includes/fields/class-gf-field-date.php:24
msgid "Allows users to enter a date."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date in the format (%s)."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date."
msgstr ""

#: includes/fields/class-gf-field-date.php:873
msgid "mm/dd/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:875
msgid "MM slash DD slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:880
msgid "dd/mm/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:882
msgid "DD slash MM slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:887
msgid "dd-mm-yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:889
msgid "DD dash MM dash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:894
msgid "dd.mm.yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:896
msgid "DD dot MM dot YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:901
msgid "yyyy/mm/dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:903
msgid "YYYY slash MM slash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:910
msgid "YYYY dash MM dash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:915
msgid "yyyy.mm.dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:917
msgid "YYYY dot MM dot DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:944
msgctxt "Abbreviation: Month"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-date.php:947
msgctxt "Abbreviation: Day"
msgid "DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:950
msgctxt "Abbreviation: Year"
msgid "YYYY"
msgstr ""

#: includes/fields/class-gf-field-donation.php:50
#: includes/fields/class-gf-field-price.php:46
msgid "Please enter a valid amount."
msgstr ""

#: includes/fields/class-gf-field-email.php:24
msgid "Allows users to enter a valid email address."
msgstr ""

#: includes/fields/class-gf-field-email.php:118
msgid "The email address entered is invalid, please check the formatting (e.g. <EMAIL>)."
msgstr ""

#: includes/fields/class-gf-field-email.php:122
msgid "The email address entered is invalid."
msgstr ""

#: includes/fields/class-gf-field-email.php:127
msgid "Your emails do not match."
msgstr ""

#: includes/fields/class-gf-field-email.php:217
#: includes/template-library/templates/templates.php:164
#: includes/template-library/templates/templates.php:712
#: includes/template-library/templates/templates.php:1370
#: includes/template-library/templates/templates.php:1771
#: includes/template-library/templates/templates.php:4746
#: includes/template-library/templates/templates.php:5874
#: includes/template-library/templates/templates.php:6837
#: includes/template-library/templates/templates.php:7055
#: includes/template-library/templates/templates.php:7614
#: includes/template-library/templates/templates.php:7937
#: includes/template-library/templates/templates.php:9464
#: includes/template-library/templates/templates.php:9804
#: js.php:1200
#: notification.php:212
msgid "Enter Email"
msgstr ""

#: includes/fields/class-gf-field-email.php:219
#: includes/template-library/templates/templates.php:170
#: includes/template-library/templates/templates.php:720
#: includes/template-library/templates/templates.php:1376
#: includes/template-library/templates/templates.php:1777
#: includes/template-library/templates/templates.php:4753
#: includes/template-library/templates/templates.php:5880
#: includes/template-library/templates/templates.php:6843
#: includes/template-library/templates/templates.php:7060
#: includes/template-library/templates/templates.php:7620
#: includes/template-library/templates/templates.php:7943
#: includes/template-library/templates/templates.php:9470
#: includes/template-library/templates/templates.php:9811
#: js.php:1205
msgid "Confirm Email"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:120
msgid "Allows users to upload a file."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:172
#: includes/fields/class-gf-field-fileupload.php:184
#: includes/upload.php:118
msgid "File exceeds size limit. Maximum file size: %dMB"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:176
msgid "There was an error while uploading the file. Error code: %d"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:200
#: includes/fields/class-gf-field-fileupload.php:218
#: includes/upload.php:106
msgid "The uploaded file type is not allowed."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:224
#: includes/upload.php:124
msgid "The uploaded file type is not allowed. Must be one of the following: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:239
msgid "Maximum number of files (%d) exceeded."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:278
msgid "Accepted file types: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum file size
#: includes/fields/class-gf-field-fileupload.php:283
msgid "Max. file size: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum number of files
#: includes/fields/class-gf-field-fileupload.php:288
msgid "Max. files: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:326
#: includes/fields/class-gf-field-fileupload.php:349
msgid "Allowed Files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:355
msgid "Drop files here or"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:356
msgid "Select files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:415
msgid "Download file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:416
#: includes/fields/class-gf-field-fileupload.php:443
msgid "Delete file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:417
msgid "View file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:784
msgid "%d files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:799
#: includes/fields/class-gf-field-post-image.php:213
msgid "View the image"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:858
msgid "Click to view"
msgstr ""

#: includes/fields/class-gf-field-hidden.php:24
msgid "Stores information that should not be visible to the user but can be processed and saved with the user submission."
msgstr ""

#: includes/fields/class-gf-field-html.php:12
msgid "HTML"
msgstr ""

#: includes/fields/class-gf-field-html.php:23
msgid "Places a block of free form HTML anywhere in your form."
msgstr ""

#: includes/fields/class-gf-field-html.php:56
msgid "HTML Content"
msgstr ""

#: includes/fields/class-gf-field-html.php:57
msgid "This is a content placeholder. HTML content is not displayed in the form admin. Preview this form to view the content."
msgstr ""

#: includes/fields/class-gf-field-image-choice.php:28
msgid "Image Choice"
msgstr ""

#: includes/fields/class-gf-field-image-choice.php:39
msgid "Allow users to choose from a list of images."
msgstr ""

#: includes/fields/class-gf-field-list.php:47
msgid "Allows the user to add/remove additional rows of information per field."
msgstr ""

#: includes/fields/class-gf-field-list.php:224
msgid "Remove row {0}"
msgstr ""

#: includes/fields/class-gf-field-list.php:229
#: includes/fields/class-gf-field-list.php:410
msgid "Add another row"
msgstr ""

#: includes/fields/class-gf-field-list.php:229
#: includes/settings/config/class-gf-settings-config-i18n.php:34
#: includes/webapi/webapi.php:448
msgid "Add"
msgstr ""

#: includes/fields/class-gf-field-list.php:230
msgid "Remove"
msgstr ""

#: includes/fields/class-gf-field-list.php:410
msgid "Add a new row"
msgstr ""

#: includes/fields/class-gf-field-list.php:411
msgid "Remove this row"
msgstr ""

#: includes/fields/class-gf-field-list.php:468
msgid ", Row {0}"
msgstr ""

#: includes/fields/class-gf-field-multiple-choice.php:13
msgid "Multiple Choice"
msgstr ""

#: includes/fields/class-gf-field-multiple-choice.php:22
msgid "Allow users to choose from a list of options."
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:41
msgid "Allows users to select multiple options available in the multi select box."
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:153
msgid "Click to select..."
msgstr ""

#: includes/fields/class-gf-field-name.php:69
msgid "Allows users to enter their name in the format you have specified."
msgstr ""

#: includes/fields/class-gf-field-name.php:274
#: includes/template-library/templates/templates.php:37
#: includes/template-library/templates/templates.php:403
#: includes/template-library/templates/templates.php:1260
#: includes/template-library/templates/templates.php:1649
#: includes/template-library/templates/templates.php:2289
#: includes/template-library/templates/templates.php:3106
#: includes/template-library/templates/templates.php:3912
#: includes/template-library/templates/templates.php:4621
#: includes/template-library/templates/templates.php:5752
#: includes/template-library/templates/templates.php:6729
#: includes/template-library/templates/templates.php:6952
#: includes/template-library/templates/templates.php:7508
#: includes/template-library/templates/templates.php:7814
#: includes/template-library/templates/templates.php:8312
#: includes/template-library/templates/templates.php:9350
#: includes/template-library/templates/templates.php:9691
#: js.php:1110
msgid "Prefix"
msgstr ""

#: includes/fields/class-gf-field-name.php:275
#: includes/fields/class-gf-field-name.php:396
#: includes/template-library/templates/templates.php:89
#: includes/template-library/templates/templates.php:455
#: includes/template-library/templates/templates.php:1298
#: includes/template-library/templates/templates.php:1701
#: includes/template-library/templates/templates.php:2341
#: includes/template-library/templates/templates.php:3158
#: includes/template-library/templates/templates.php:3964
#: includes/template-library/templates/templates.php:4674
#: includes/template-library/templates/templates.php:5804
#: includes/template-library/templates/templates.php:6767
#: includes/template-library/templates/templates.php:6989
#: includes/template-library/templates/templates.php:7546
#: includes/template-library/templates/templates.php:7866
#: includes/template-library/templates/templates.php:8364
#: includes/template-library/templates/templates.php:9402
#: includes/template-library/templates/templates.php:9729
#: js.php:1118
msgid "First"
msgstr ""

#: includes/fields/class-gf-field-name.php:276
#: includes/template-library/templates/templates.php:96
#: includes/template-library/templates/templates.php:462
#: includes/template-library/templates/templates.php:1304
#: includes/template-library/templates/templates.php:1707
#: includes/template-library/templates/templates.php:2347
#: includes/template-library/templates/templates.php:3164
#: includes/template-library/templates/templates.php:3970
#: includes/template-library/templates/templates.php:4681
#: includes/template-library/templates/templates.php:5810
#: includes/template-library/templates/templates.php:6773
#: includes/template-library/templates/templates.php:6994
#: includes/template-library/templates/templates.php:7552
#: includes/template-library/templates/templates.php:7872
#: includes/template-library/templates/templates.php:8370
#: includes/template-library/templates/templates.php:9408
#: includes/template-library/templates/templates.php:9736
#: js.php:1130
msgid "Middle"
msgstr ""

#: includes/fields/class-gf-field-name.php:277
#: includes/fields/class-gf-field-name.php:397
#: includes/template-library/templates/templates.php:103
#: includes/template-library/templates/templates.php:469
#: includes/template-library/templates/templates.php:1311
#: includes/template-library/templates/templates.php:1714
#: includes/template-library/templates/templates.php:2354
#: includes/template-library/templates/templates.php:3171
#: includes/template-library/templates/templates.php:3977
#: includes/template-library/templates/templates.php:4688
#: includes/template-library/templates/templates.php:5817
#: includes/template-library/templates/templates.php:6780
#: includes/template-library/templates/templates.php:7000
#: includes/template-library/templates/templates.php:7559
#: includes/template-library/templates/templates.php:7879
#: includes/template-library/templates/templates.php:8377
#: includes/template-library/templates/templates.php:9415
#: includes/template-library/templates/templates.php:9743
#: js.php:1137
msgid "Last"
msgstr ""

#: includes/fields/class-gf-field-name.php:278
#: includes/template-library/templates/templates.php:110
#: includes/template-library/templates/templates.php:476
#: includes/template-library/templates/templates.php:1317
#: includes/template-library/templates/templates.php:1720
#: includes/template-library/templates/templates.php:2360
#: includes/template-library/templates/templates.php:3177
#: includes/template-library/templates/templates.php:3983
#: includes/template-library/templates/templates.php:4695
#: includes/template-library/templates/templates.php:5823
#: includes/template-library/templates/templates.php:6786
#: includes/template-library/templates/templates.php:7005
#: includes/template-library/templates/templates.php:7565
#: includes/template-library/templates/templates.php:7885
#: includes/template-library/templates/templates.php:8383
#: includes/template-library/templates/templates.php:9421
#: includes/template-library/templates/templates.php:9750
#: js.php:1142
msgid "Suffix"
msgstr ""

#: includes/fields/class-gf-field-number.php:24
msgid "Allows users to enter a number."
msgstr ""

#: includes/fields/class-gf-field-number.php:129
msgid "Please enter a valid quantity. Quantity cannot contain decimals."
msgstr ""

#: includes/fields/class-gf-field-number.php:199
msgid "Please enter a number from %1$s to %2$s."
msgstr ""

#: includes/fields/class-gf-field-number.php:201
msgid "Please enter a number greater than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:203
msgid "Please enter a number less than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:205
msgid "Please enter a valid number."
msgstr ""

#: includes/fields/class-gf-field-option.php:29
#: js.php:1019
msgid "Option"
msgstr ""

#: includes/fields/class-gf-field-option.php:40
msgid "Allows users to select options for products created by a product field."
msgstr ""

#: includes/fields/class-gf-field-page.php:23
msgid "Allows multi-page forms."
msgstr ""

#: includes/fields/class-gf-field-page.php:51
msgid "Page Break"
msgstr ""

#: includes/fields/class-gf-field-password.php:14
#: includes/template-library/templates/templates.php:9549
#: js.php:926
msgid "Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:25
msgid "Allows the user to enter a password and confirm it.  The password will be masked with blobs or asterisks."
msgstr ""

#: includes/fields/class-gf-field-password.php:92
msgid "Your passwords do not match."
msgstr ""

#: includes/fields/class-gf-field-password.php:104
msgid "Your password does not meet the required strength. %sHint: To make it stronger, use upper and lower case letters, numbers and symbols like ! \" ? $ %% ^ & )."
msgstr ""

#: includes/fields/class-gf-field-password.php:213
#: includes/template-library/templates/templates.php:9557
#: js.php:1216
msgid "Enter Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:216
#: includes/template-library/templates/templates.php:9562
#: js.php:1217
msgid "Confirm Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:226
#: includes/fields/class-gf-field-password.php:227
msgid "Show Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:226
#: includes/fields/class-gf-field-password.php:227
msgid "Hide Password"
msgstr ""

#: includes/fields/class-gf-field-phone.php:49
msgid "Allows users to enter a phone number."
msgstr ""

#: includes/fields/class-gf-field-phone.php:180
#: js.php:1662
msgid "Phone format:"
msgstr ""

#: includes/fields/class-gf-field-post-category.php:23
msgid "Allows the user to select a category for the post they are creating."
msgstr ""

#: includes/fields/class-gf-field-post-content.php:14
msgid "Body"
msgstr ""

#: includes/fields/class-gf-field-post-content.php:25
msgid "Allows users to submit the body content for a post."
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:12
msgid "Custom Field"
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:23
msgid "Allows users to submit data that is used as a custom field value for a post."
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:12
msgid "Excerpt"
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:23
msgid "Allows users to submit data that is then used as the excerpt of a post."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:32
#: js.php:963
msgid "Post Image"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:43
msgid "Allows users to upload an image that is added to the Media Library and Gallery for the post that is created."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:115
msgid "Accepted file types: %s."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:128
msgid "delete"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:132
#: js.php:931
msgid "File"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:241
msgid "View the image (opens in a new tab)"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:12
msgid "Tags"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:23
msgid "Allows users to submit the tags for a post."
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:89
#: includes/fields/class-gf-field-text.php:126
msgid "Separate tags with commas"
msgstr ""

#: includes/fields/class-gf-field-post-title.php:24
msgid "Allows users to submit the title for a post."
msgstr ""

#: includes/fields/class-gf-field-product.php:13
#: includes/orders/summaries/class-gf-order-summary.php:62
msgid "Product"
msgstr ""

#: includes/fields/class-gf-field-product.php:24
msgid "Allows the creation of products in the form."
msgstr ""

#: includes/fields/class-gf-field-quantity.php:29
#: includes/fields/class-gf-field-singleproduct.php:133
#: includes/fields/class-gf-field.php:2882
#: includes/template-library/templates/templates.php:2088
#: includes/template-library/templates/templates.php:2149
#: includes/template-library/templates/templates.php:2945
#: includes/template-library/templates/templates.php:3008
#: includes/template-library/templates/templates.php:3751
#: includes/template-library/templates/templates.php:3814
#: js.php:989
#: js.php:1060
msgid "Quantity"
msgstr ""

#: includes/fields/class-gf-field-quantity.php:40
msgid "Allows a quantity to be specified for product field."
msgstr ""

#: includes/fields/class-gf-field-radio.php:34
#: includes/fields/class-gf-field-select.php:33
msgid "Allows users to select one option from a list."
msgstr ""

#: includes/fields/class-gf-field-radio.php:304
#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice-radio-markup.php:150
msgid "Other Choice, please specify"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:26
msgid "Repeater"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:391
msgid "Are you sure you want to remove this item?"
msgstr ""

#: includes/fields/class-gf-field-section.php:12
msgid "Section"
msgstr ""

#: includes/fields/class-gf-field-section.php:23
msgid "Adds a content separator to your form to help organize groups of fields. This is a visual element and does not collect any data."
msgstr ""

#: includes/fields/class-gf-field-shipping.php:26
#: includes/template-library/templates/templates.php:2659
#: includes/template-library/templates/templates.php:3476
#: includes/template-library/templates/templates.php:4282
#: js.php:1001
msgid "Shipping"
msgstr ""

#: includes/fields/class-gf-field-shipping.php:37
msgid "Allows a shipping fee to be added to the form total."
msgstr ""

#: includes/fields/class-gf-field-text.php:13
msgid "Single Line Text"
msgstr ""

#: includes/fields/class-gf-field-text.php:24
msgid "Allows users to submit a single line of text."
msgstr ""

#: includes/fields/class-gf-field-text.php:84
#: includes/fields/class-gf-field-textarea.php:223
msgid "The text entered exceeds the maximum number of characters."
msgstr ""

#: includes/fields/class-gf-field-textarea.php:24
msgid "Allows users to submit multiple lines of text."
msgstr ""

#: includes/fields/class-gf-field-time.php:54
msgid "Allows users to submit a time as hours and minutes."
msgstr ""

#: includes/fields/class-gf-field-time.php:190
msgid "Please enter a valid time."
msgstr ""

#: includes/fields/class-gf-field-time.php:286
#: includes/fields/class-gf-field-time.php:325
#: js.php:342
#: js.php:345
msgid "HH"
msgstr ""

#: includes/fields/class-gf-field-time.php:287
#: includes/fields/class-gf-field-time.php:334
msgctxt "Abbreviation: Minutes"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-time.php:306
msgid "AM"
msgstr ""

#: includes/fields/class-gf-field-time.php:307
msgid "PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:314
#: js.php:1185
msgid "AM/PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:327
msgid "Hours"
msgstr ""

#: includes/fields/class-gf-field-time.php:336
msgid "Minutes"
msgstr ""

#: includes/fields/class-gf-field-total.php:178
msgid "Submitted value (%s) does not match expected value (%s)."
msgstr ""

#: includes/fields/class-gf-field-website.php:24
msgid "Allows users to enter a website URL."
msgstr ""

#: includes/fields/class-gf-field-website.php:75
msgid "Please enter a valid Website URL (e.g. https://gravityforms.com)."
msgstr ""

#. Translators: comma-separated list of the labels of missing fields.
#: includes/fields/class-gf-field.php:964
msgid "Please complete the following fields: %s."
msgstr ""

#: includes/fields/class-gf-field.php:1199
msgid "Field value cannot be displayed. Please activate the add-on that includes the `%s` field type."
msgstr ""

#: includes/fields/class-gf-field.php:1201
msgid "Field value cannot be displayed. If you are the developer of the `%s` field type, please implement `%s::get_value_entry_detail()` to define how the value is displayed."
msgstr ""

#: includes/fields/class-gf-field.php:1703
msgid "duplicate this field"
msgstr ""

#: includes/fields/class-gf-field.php:1730
msgid "delete this field"
msgstr ""

#: includes/fields/class-gf-field.php:1754
msgid "jump to this field's settings"
msgstr ""

#: includes/fields/class-gf-field.php:1777
msgid "Move"
msgstr ""

#: includes/fields/class-gf-field.php:1786
msgid "ID: %s"
msgstr ""

#: includes/fields/class-gf-field.php:1803
msgid "This field has an issue"
msgstr ""

#: includes/fields/class-gf-field.php:1844
msgid "This field is hidden when viewing the form"
msgstr ""

#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice.php:62
msgid "Image for choice number"
msgstr ""

#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice.php:82
msgid "Choice number"
msgstr ""

#: includes/fields/field-decorator-choice/class-gf-field-decorator-choice.php:84
msgid "does not have an image"
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:32
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) to the left of the choice."
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:34
msgid "Expand the Choices window"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:34
msgid "Form Updated"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:35
msgid "View Form"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:36
msgid "An error occurred while saving the form."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:37
msgid "Request failed due to a network error. Please check your internet connection."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:38
msgid "Form was updated successfully."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:41
msgid "Saved"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:46
msgid "Save Error."
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:45
msgid "Honeypot Spam Filter"
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:45
msgid "Failed Honeypot Validation."
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:198
msgid "This field is for validation purposes and should be left unchanged."
msgstr ""

#: includes/legacy/forms_model_legacy.php:2338
msgid "There was a problem while inserting one of the input values for the entry"
msgstr ""

#: includes/legacy/forms_model_legacy.php:2347
msgid "There was a problem while inserting the field values"
msgstr ""

#: includes/license/class-gf-license-api-response.php:43
#: includes/setup-wizard/endpoints/class-gf-setup-wizard-endpoint-validate-license.php:45
msgid "The license is invalid."
msgstr ""

#: includes/license/class-gf-license-api-response.php:147
msgid "Sites Exceeded"
msgstr ""

#: includes/license/class-gf-license-api-response.php:152
msgid "Invalid"
msgstr ""

#: includes/license/class-gf-license-api-response.php:154
msgid "Expired"
msgstr ""

#: includes/license/class-gf-license-api-response.php:222
msgid "Manage"
msgstr ""

#: includes/license/class-gf-license-api-response.php:229
#: includes/template-library/config/class-gf-template-library-config.php:113
msgid "Upgrade"
msgstr ""

#: includes/license/class-gf-license-api-response.php:299
#: tests/unit-tests/license/test-license-api-response.php:495
#: tests/unit-tests/license/test-license-api-response.php:511
msgid "Expired On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:306
#: tests/unit-tests/license/test-license-api-response.php:523
msgid "Renews On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:309
#: tests/unit-tests/license/test-license-api-response.php:533
#: tests/unit-tests/license/test-license-api-response.php:542
#: tests/unit-tests/license/test-license-api-response.php:551
msgid "Expires On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:321
#: tests/unit-tests/license/test-license-api-response.php:420
msgid "Does not expire"
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:46
msgid "The license key entered is incorrect; please visit the %1$sGravity Forms website%2$s to verify your license. "
msgstr ""

#: includes/license/class-gf-license-statuses.php:53
msgid "Your license key has been successfully validated. "
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:56
msgid "The license key entered has been revoked; please check its status in your %1$sGravity Forms account.%2$s "
msgstr ""

#: includes/license/class-gf-license-statuses.php:60
msgid "The license key has already been activated on its maximum number of sites; please upgrade your license. "
msgstr ""

#: includes/license/class-gf-license-statuses.php:61
msgid "The license key does not support multisite installations. Please use a different license. "
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:64
msgid "The license key has expired; please visit your %1$sGravity Forms account%2$s to manage your license. "
msgstr ""

#. translators: %1$s admin link tag markup, %2$s closing markup, %3$s Gravity Forms link tag markup, %4$s closing markup
#: includes/license/class-gf-license-statuses.php:70
msgid "%1$sRegister%2$s your copy of Gravity Forms to receive access to automatic upgrades and support. Need a license key? %3$sPurchase one now%4$s. "
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:78
msgid "There was an error while validating your license key; please try again later. If the problem persists, please %1$scontact support%2$s."
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:84
msgid "Your IP address has been temporarily blocked for exceeding our API rate limits. Please try again in a few minutes. If the issue persists, %1$scontact support%2$s."
msgstr ""

#: includes/locking/class-gf-locking.php:199
msgid "This page is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/class-gf-locking.php:200
#: includes/locking/locking.php:21
msgid "Accept"
msgstr ""

#: includes/locking/class-gf-locking.php:202
msgid "%s is currently editing"
msgstr ""

#: includes/locking/class-gf-locking.php:203
msgid "%s has taken over and is currently editing."
msgstr ""

#: includes/locking/class-gf-locking.php:204
msgid "%s has requested permission to take over control."
msgstr ""

#: includes/locking/class-gf-locking.php:205
#: includes/locking/class-gf-locking.php:285
msgid "You now have control"
msgstr ""

#: includes/locking/class-gf-locking.php:207
msgid "No response"
msgstr ""

#: includes/locking/class-gf-locking.php:208
msgid "Request again"
msgstr ""

#: includes/locking/class-gf-locking.php:210
msgid "Your request was rejected"
msgstr ""

#: includes/locking/class-gf-locking.php:290
msgid "Your request has been sent to %s."
msgstr ""

#: includes/locking/class-gf-locking.php:490
msgid "Take Over"
msgstr ""

#: includes/locking/class-gf-locking.php:491
msgid "Request Control"
msgstr ""

#: includes/locking/class-gf-locking.php:508
msgid "Reject Request"
msgstr ""

#: includes/locking/locking.php:20
msgid "This form is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:22
msgid "%s is currently editing this form"
msgstr ""

#: includes/locking/locking.php:23
msgid "%s has taken over and is currently editing this form."
msgstr ""

#: includes/locking/locking.php:24
msgid "%s has requested permission to take over control of this form."
msgstr ""

#: includes/locking/locking.php:61
msgid "This entry is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:62
msgid "%s is currently editing this entry"
msgstr ""

#: includes/locking/locking.php:63
msgid "%s has taken over and is currently editing this entry."
msgstr ""

#: includes/locking/locking.php:64
msgid "%s has requested permission to take over control of this entry."
msgstr ""

#: includes/locking/locking.php:107
msgid "These form settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:110
#: includes/locking/locking.php:152
msgid "%s has requested permission to take over control of these settings."
msgstr ""

#: includes/locking/locking.php:149
msgid "These settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/logging/logging.php:219
msgid "Log file could not be deleted."
msgstr ""

#: includes/logging/logging.php:222
msgid "Invalid log file."
msgstr ""

#: includes/logging/logging.php:228
msgid "Log file was successfully deleted."
msgstr ""

#: includes/logging/logging.php:290
msgid "Logging assists in tracking down issues by logging debug and error messages in Gravity Forms Core and Gravity Forms Add-Ons. Important information may be included in the logging messages, including API usernames, passwords and credit card numbers. Logging is intended only to be used temporarily while trying to track down issues. Once the issue is identified and resolved, it should be disabled."
msgstr ""

#: includes/logging/logging.php:307
msgid "Plugin Logging Settings"
msgstr ""

#: includes/logging/logging.php:440
msgid "view log"
msgstr ""

#: includes/logging/logging.php:441
msgid "delete log"
msgstr ""

#: includes/logging/logging.php:454
msgid "Enable logging and log all messages"
msgstr ""

#: includes/logging/logging.php:473
msgid "Enable logging"
msgstr ""

#: includes/logging/logging.php:509
msgid "and log all messages"
msgstr ""

#: includes/logging/logging.php:527
msgid "and log only error messages"
msgstr ""

#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:28
msgid "Search Merge Tags"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:136
msgid "Trial Discount"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Free Trial"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:61
msgid "Order"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:63
msgid "Qty"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:64
msgid "Unit Price"
msgstr ""

#: includes/orders/summaries/views/view-order-summary.php:48
#: includes/orders/summaries/views/view-pricing-fields-html.php:44
#: includes/orders/summaries/views/view-pricing-fields-text.php:23
msgid "Sub Total"
msgstr ""

#: includes/save-form/class-gf-form-crud-handler.php:404
msgid "New submission from"
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:190
msgid "Please enter a unique form title, this title is used for an existing form."
msgstr ""

#. Translators: 1. Opening link tag, 2. Closing link tag.
#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:194
msgid "There was an error while saving your form. Please %1$scontact our support team%2$s."
msgstr ""

#: includes/settings/class-settings.php:832
msgid "Toggle %s Section"
msgstr ""

#: includes/settings/class-settings.php:988
msgid "Save Settings"
msgstr ""

#: includes/settings/class-settings.php:991
#: settings.php:1176
msgid "Settings updated."
msgstr ""

#: includes/settings/config/class-gf-settings-config-admin.php:144
#: includes/settings/fields/class-user-select.php:117
msgid "Logged In User"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:28
msgid "Loading"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:38
msgid "Remove Custom Value"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:46
msgid "Hex"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:51
msgid "swatch"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:56
#: js.php:1589
msgid "Click to upload"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:57
#: js.php:1590
msgid "or drag and drop"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:58
msgid "max."
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:59
#: js.php:1592
msgid "or"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:60
#: js.php:1593
msgid "Replace"
msgstr ""

#: includes/settings/fields/class-checkbox.php:330
#: includes/settings/fields/class-checkbox.php:378
#: includes/settings/fields/class-radio.php:204
#: includes/settings/fields/class-select-custom.php:220
#: includes/settings/fields/class-select.php:275
#: includes/settings/fields/class-select.php:303
msgid "Invalid selection."
msgstr ""

#: includes/settings/fields/class-date-time.php:180
msgid "Date must not include HTML tags."
msgstr ""

#: includes/settings/fields/class-date-time.php:186
msgid "You must select a valid hour."
msgstr ""

#: includes/settings/fields/class-date-time.php:192
msgid "You must select a valid minute."
msgstr ""

#: includes/settings/fields/class-date-time.php:198
msgid "You must select either am or pm."
msgstr ""

#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:321
msgid "First Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:321
msgid "Name (First)"
msgstr ""

#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:322
msgid "Last Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:322
msgid "Name (Last)"
msgstr ""

#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:323
msgid "Address (Street Address)"
msgstr ""

#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:324
msgid "Address (Address Line 2)"
msgstr ""

#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:325
msgid "Address (City)"
msgstr ""

#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:326
msgid "Address (State / Province)"
msgstr ""

#: includes/settings/fields/class-field-select.php:151
#: includes/settings/fields/class-generic-map.php:327
msgid "Address (Zip / Postal Code)"
msgstr ""

#: includes/settings/fields/class-field-select.php:152
#: includes/settings/fields/class-generic-map.php:328
msgid "Address (Country)"
msgstr ""

#: includes/settings/fields/class-generic-map.php:182
msgid "Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:185
msgid "Custom Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:191
msgid "Custom Value"
msgstr ""

#: includes/settings/fields/class-generic-map.php:237
msgid "No mapping fields are available."
msgstr ""

#: includes/settings/fields/class-generic-map.php:407
msgid "Select a Value"
msgstr ""

#: includes/settings/fields/class-generic-map.php:447
msgid "Full Address"
msgstr ""

#: includes/settings/fields/class-generic-map.php:457
msgid "Full Name"
msgstr ""

#: includes/settings/fields/class-generic-map.php:521
msgid "Form Fields"
msgstr ""

#: includes/settings/fields/class-generic-map.php:531
msgid "Entry Properties"
msgstr ""

#: includes/settings/fields/class-generic-map.php:572
msgid "Entry Meta"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:52
msgid "To use notification routing, your form must have a field supported by conditional logic."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:132
#: includes/settings/fields/class-notification-routing.php:238
msgid "Add Another Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:142
#: includes/settings/fields/class-notification-routing.php:149
#: includes/settings/fields/class-notification-routing.php:243
msgid "Remove This Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:161
#: includes/settings/fields/class-notification-routing.php:224
msgid "Send to"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:163
#: includes/settings/fields/class-notification-routing.php:225
msgid "if"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:180
msgid "Please enter a valid email address for all highlighted routing rules above."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:379
#: includes/settings/fields/class-notification-routing.php:586
msgid "Enter value"
msgstr ""

#: includes/settings/fields/class-post-select.php:51
msgid "The requested post type %s does not exist."
msgstr ""

#. Translators: plural post type name (e.g. 'post's).
#: includes/settings/fields/class-post-select.php:158
msgid "Search all %s"
msgstr ""

#: includes/settings/fields/class-select-custom.php:90
msgid "Add Custom"
msgstr ""

#: includes/settings/fields/class-text.php:117
#: includes/settings/fields/class-textarea.php:213
msgid "The text you have entered is not valid. For security reasons, some characters are not allowed. "
msgstr ""

#: includes/settings/fields/class-text.php:120
#: includes/settings/fields/class-textarea.php:216
msgid "Fix it"
msgstr ""

#: includes/settings/fields/class-user-select.php:90
msgid "Search users"
msgstr ""

#: includes/settings/fields/class-user-select.php:113
msgid "Select a user"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:53
msgid "Invalid License Key"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:54
msgid "Back To Dashboard"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:55
msgid "Toggle Fullscreen"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:58
#: includes/wizard/class-gf-installation-wizard.php:97
msgid "Welcome to Gravity Forms"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:59
msgid "Thank you for choosing Gravity Forms. We know you’re going to love our form builder and all it has to offer!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:60
msgid "Create surveys and quizzes"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:61
msgid "Accept online payments"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:62
msgid "Build custom business solutions"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:63
msgid "Enter License Key"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:64
msgid "Paste your license key here"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:65
msgid "Enter your license key below to enable Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:66
msgid "Activate License"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:67
msgid "License Key Validated"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:68
msgid "Checking License"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:69
msgid "Get 20% Off Gravity Forms!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:70
msgid "To continue installation enter your email below and get 20% off any new license."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:71
msgid "Email address"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:72
msgid "Get the Discount"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:73
msgid "I agree to the handling and storage of my data and to receive marketing communications from Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:76
msgid "Let's get you set up!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:77
msgid "Configure Gravity Forms to work in the way that you want."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:78
msgid "Hide license information"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:79
msgid "If you're installing Gravity Forms for a client, enable this setting to hide the license information."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:80
msgid "Enable automatic updates"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:81
msgid "Recommended"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:82
msgid "Feature Disabled"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:83
msgid "We recommend you enable this feature to ensure Gravity Forms runs smoothly."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:87
msgid "Personalize your Gravity Forms experience"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:88
msgid "Tell us about your site and how you’d like to use Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:89
msgid "How would you best describe your website?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:90
msgid "What types of forms do you want to create?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:91
msgid "Do you want to integrate your forms with any of these services?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:96
msgid "Help Make Gravity Forms Better!"
msgstr ""

#. translators: placeholders are markup to create a link.
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:99
#: settings.php:702
msgid "We love improving the form building experience for everyone in our community. By enabling data collection, you can help us learn more about how our customers use Gravity Forms. %1$sLearn more...%2$s"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:107
msgid "Ready to Create Your First Form?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:108
msgid "Watch the video below to help you get started with Gravity Forms, or jump straight in and begin your form building journey!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:93
#: includes/template-library/templates/templates.php:8874
#: includes/template-library/templates/templates.php:8912
msgid "Contact Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:104
msgid "Conversational Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:115
msgid "Survey"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:126
#: includes/template-library/templates/templates.php:8859
#: includes/template-library/templates/templates.php:8894
msgid "Payment Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:137
msgid "Subscription Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:148
#: includes/template-library/templates/templates.php:1620
#: includes/template-library/templates/templates.php:1626
#: includes/template-library/templates/templates.php:8869
#: includes/template-library/templates/templates.php:8906
msgid "Donation Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:159
msgid "Customer Service Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:170
msgid "Registration Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:181
msgid "Custom Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:207
msgid "Select a Website Type"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:214
msgid "Blog"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:218
msgid "Personal Business/Services"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:222
msgid "Small/Medium Business"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:226
msgid "Enterprise"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:230
msgid "eCommerce"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:238
msgid "Nonprofit"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:252
msgid "Email Marketing Platform"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:263
msgid "CRM"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:274
msgid "Payment Processor"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:285
msgid "Anti Spam Services"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:296
msgid "Accounting Software"
msgstr ""

#: includes/setup-wizard/endpoints/class-gf-setup-wizard-endpoint-save-prefs.php:135
msgid "Preferences updated."
msgstr ""

#. translators: About page title. 1: Version number.
#: includes/splash-page/class-gf-splash-page.php:113
msgid "About %1$s &lsaquo; System Status &lsaquo; Gravity Forms &#8212; WordPress"
msgstr ""

#: includes/splash-page/class-gf-splash-page.php:167
msgid "About %s"
msgstr ""

#: includes/splash-page/gf_splash.php:5
msgid "New Fields Added in Gravity Forms 2.9!"
msgstr ""

#: includes/splash-page/gf_splash.php:6
msgid "The new Image Choice and Multiple Choice fields give you more flexibility and control when creating forms."
msgstr ""

#: includes/splash-page/gf_splash.php:7
#: includes/splash-page/gf_splash.php:160
msgid "Get started with a new form"
msgstr ""

#: includes/splash-page/gf_splash.php:8
#: includes/splash-page/gf_splash.php:161
msgid "Get Started"
msgstr ""

#: includes/splash-page/gf_splash.php:16
msgid "Read reviews of Gravity Forms on G2"
msgstr ""

#: includes/splash-page/gf_splash.php:23
msgid "G2 logo"
msgstr ""

#: includes/splash-page/gf_splash.php:33
msgid "4.7 Stars"
msgstr ""

#: includes/splash-page/gf_splash.php:39
msgid "Read reviews of Gravity Forms on Trustpilot"
msgstr ""

#: includes/splash-page/gf_splash.php:46
msgid "Trustpilot logo"
msgstr ""

#: includes/splash-page/gf_splash.php:52
msgid "Trustpilot rating"
msgstr ""

#: includes/splash-page/gf_splash.php:56
msgid "4.4 Stars"
msgstr ""

#: includes/splash-page/gf_splash.php:67
msgid "Image Choice Field"
msgstr ""

#: includes/splash-page/gf_splash.php:68
msgid "A picture is worth a thousand words! The new Image Choice field lets you add stylish images straight from the media library to your choices. Easily create beautiful forms with eye-catching images that speak to your users."
msgstr ""

#: includes/splash-page/gf_splash.php:69
msgid "Read more about the Image Choice field"
msgstr ""

#: includes/splash-page/gf_splash.php:70
#: includes/splash-page/gf_splash.php:94
#: includes/splash-page/gf_splash.php:125
#: includes/splash-page/gf_splash.php:137
msgid "Read More"
msgstr ""

#: includes/splash-page/gf_splash.php:71
msgid "About the Image Choice field"
msgstr ""

#: includes/splash-page/gf_splash.php:76
msgid "Screenshot of the Image Choice field in Gravity Forms 2.9"
msgstr ""

#: includes/splash-page/gf_splash.php:91
msgid "Multiple Choice Field"
msgstr ""

#: includes/splash-page/gf_splash.php:92
msgid "The Multiple Choice field is a new, flexible way to let users choose one or many options. Gather the information you need, while ensuring a high-end experience for those submitting the form."
msgstr ""

#: includes/splash-page/gf_splash.php:93
msgid "Read more about the Multiple Choice field"
msgstr ""

#: includes/splash-page/gf_splash.php:95
msgid "About the Multiple Choice field"
msgstr ""

#: includes/splash-page/gf_splash.php:100
msgid "Screenshot of the Multiple Choice field in Gravity Forms 2.9"
msgstr ""

#: includes/splash-page/gf_splash.php:118
msgid "Icon of color swatches"
msgstr ""

#: includes/splash-page/gf_splash.php:124
msgid "Editor Design Improvements"
msgstr ""

#: includes/splash-page/gf_splash.php:125
msgid "We’ve brought our beautiful Orbital form theme into the form editor! With 2.9 you’ll find a more consistent and visually-pleasing form editing experience, closely mirroring how your form will look on the front end."
msgstr ""

#: includes/splash-page/gf_splash.php:125
msgid "Read more about the Gravity Forms 2.9 editor design improvements"
msgstr ""

#: includes/splash-page/gf_splash.php:130
msgid "Icon of accessibility symbol"
msgstr ""

#: includes/splash-page/gf_splash.php:136
msgid "Editor Accessibility Improvements"
msgstr ""

#: includes/splash-page/gf_splash.php:137
msgid "As part of our continuing commitment to make form building available to everyone, we have improved the accessibility of the form editor. If you rely on keyboard navigation or screen readers, you’ll now have an easier time navigating the field settings."
msgstr ""

#: includes/splash-page/gf_splash.php:137
msgid "Read more about the Gravity Forms 2.9 editor accessibility improvements"
msgstr ""

#: includes/splash-page/gf_splash.php:155
msgid "Ready to get started?"
msgstr ""

#: includes/splash-page/gf_splash.php:158
msgid "We believe there's a better way to manage your data and forms. Are you ready to create a form? Let's go!"
msgstr ""

#: includes/system-status/class-gf-system-report.php:69
msgid "The following is a system report containing useful technical information for troubleshooting issues. If you need further help after viewing the report, click on the \"Copy System Report\" button below to copy the report and paste it in your message to support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:112
msgid "Setting"
msgstr ""

#: includes/system-status/class-gf-system-report.php:246
msgid "complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:250
msgid "Current status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:263
msgid "Upgrading Gravity Forms"
msgstr ""

#: includes/system-status/class-gf-system-report.php:265
msgid "Do not close or navigate away from this page until the upgrade is 100% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:350
msgid "Unexpected content in the response."
msgstr ""

#: includes/system-status/class-gf-system-report.php:352
msgid "Response code: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:363
msgid "Gravity Forms Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:377
msgid "Database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:382
msgid "Translations"
msgstr ""

#: includes/system-status/class-gf-system-report.php:387
msgid "Log Files"
msgstr ""

#: includes/system-status/class-gf-system-report.php:392
msgid "Scheduled (Cron) Events Log"
msgstr ""

#: includes/system-status/class-gf-system-report.php:399
msgid "WordPress Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:403
#: notification.php:982
#: notification.php:1597
msgid "WordPress"
msgstr ""

#: includes/system-status/class-gf-system-report.php:407
msgid "Home URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:412
msgid "Site URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:417
msgid "REST API Base URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:422
msgid "WordPress Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:431
msgid "The Gravity Forms support agreement requires WordPress %s or greater. This site must be upgraded in order to be eligible for support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:439
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms."
msgstr ""

#: includes/system-status/class-gf-system-report.php:446
msgid "WordPress Multisite"
msgstr ""

#: includes/system-status/class-gf-system-report.php:448
#: includes/system-status/class-gf-system-report.php:459
#: includes/system-status/class-gf-system-report.php:465
#: includes/system-status/class-gf-system-report.php:471
#: includes/system-status/class-gf-system-report.php:477
#: includes/system-status/class-gf-system-report.php:483
#: includes/system-status/class-gf-system-report.php:490
#: includes/system-status/class-gf-system-report.php:585
#: includes/system-status/class-gf-system-report.php:597
#: includes/system-status/class-gf-system-report.php:603
#: includes/system-status/class-gf-system-report.php:869
#: includes/system-status/class-gf-system-report.php:880
#: includes/system-status/class-gf-system-report.php:891
#: includes/system-status/class-gf-system-report.php:897
#: includes/template-library/templates/templates.php:8779
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:71
msgid "Yes"
msgstr ""

#: includes/system-status/class-gf-system-report.php:448
#: includes/system-status/class-gf-system-report.php:459
#: includes/system-status/class-gf-system-report.php:465
#: includes/system-status/class-gf-system-report.php:471
#: includes/system-status/class-gf-system-report.php:477
#: includes/system-status/class-gf-system-report.php:483
#: includes/system-status/class-gf-system-report.php:490
#: includes/system-status/class-gf-system-report.php:585
#: includes/system-status/class-gf-system-report.php:591
#: includes/system-status/class-gf-system-report.php:597
#: includes/system-status/class-gf-system-report.php:603
#: includes/system-status/class-gf-system-report.php:869
#: includes/system-status/class-gf-system-report.php:880
#: includes/system-status/class-gf-system-report.php:891
#: includes/system-status/class-gf-system-report.php:897
#: includes/template-library/templates/templates.php:8785
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:72
msgid "No"
msgstr ""

#: includes/system-status/class-gf-system-report.php:452
msgid "WordPress Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:457
msgid "WordPress Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:463
msgid "WordPress Debug Log"
msgstr ""

#: includes/system-status/class-gf-system-report.php:469
msgid "WordPress Script Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:475
msgid "WordPress Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:481
msgid "WordPress Alternate Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:487
msgid "Background tasks"
msgstr ""

#: includes/system-status/class-gf-system-report.php:498
msgid "Active Theme"
msgstr ""

#: includes/system-status/class-gf-system-report.php:503
msgid "Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:508
msgid "Network Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:515
msgid "Server Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:519
msgid "Web Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:523
msgid "Software"
msgstr ""

#: includes/system-status/class-gf-system-report.php:528
msgid "Port"
msgstr ""

#: includes/system-status/class-gf-system-report.php:533
msgid "Document Root"
msgstr ""

#: includes/system-status/class-gf-system-report.php:540
msgid "PHP"
msgstr ""

#: includes/system-status/class-gf-system-report.php:544
#: includes/system-status/class-gf-system-report.php:624
#: includes/system-status/class-gf-system-report.php:842
msgid "Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:550
msgid "Recommended: PHP %s or higher."
msgstr ""

#: includes/system-status/class-gf-system-report.php:553
msgid "Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:558
msgid "Maximum Execution Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:563
msgid "Maximum File Upload Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:568
msgid "Maximum File Uploads"
msgstr ""

#: includes/system-status/class-gf-system-report.php:573
msgid "Maximum Post Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:578
msgid "Maximum Input Variables"
msgstr ""

#: includes/system-status/class-gf-system-report.php:583
msgid "cURL Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:585
#: includes/system-status/class-gf-system-report.php:586
msgid "version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:589
msgid "OpenSSL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:595
msgid "Mcrypt Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:601
msgid "Mbstring Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:607
msgid "Loaded Extensions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:615
msgid "Database Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:619
msgid "Database Management System"
msgstr ""

#. translators: %s is the database type (MySQL, MariaDB or SQLite).
#: includes/system-status/class-gf-system-report.php:631
msgid "Gravity Forms requires %s or above."
msgstr ""

#: includes/system-status/class-gf-system-report.php:634
msgid "Database Character Set"
msgstr ""

#: includes/system-status/class-gf-system-report.php:639
msgid "Database Collation"
msgstr ""

#: includes/system-status/class-gf-system-report.php:646
msgid "Date and Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:650
msgid "WordPress (Local) Timezone"
msgstr ""

#: includes/system-status/class-gf-system-report.php:655
msgid "MySQL - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:660
msgid "MySQL - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:665
msgid "PHP - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:670
msgid "PHP - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:727
#: includes/system-status/class-gf-system-report.php:749
#: includes/system-status/class-gf-system-report.php:773
msgid "Passes"
msgstr ""

#: includes/system-status/class-gf-system-report.php:735
#: includes/system-status/class-gf-system-report.php:761
#: includes/system-status/class-gf-system-report.php:780
msgid "Fails"
msgstr ""

#: includes/system-status/class-gf-system-report.php:849
#: includes/system-status/class-gf-system-report.php:1237
msgid "New version %s available."
msgstr ""

#: includes/system-status/class-gf-system-report.php:854
msgid "Upload folder"
msgstr ""

#: includes/system-status/class-gf-system-report.php:859
msgid "Upload folder permissions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:861
msgid "Writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:861
msgid "Not writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:864
msgid "File uploads, entry exports, and logging will not function properly."
msgstr ""

#: includes/system-status/class-gf-system-report.php:867
#: tooltips.php:151
msgid "Output CSS"
msgstr ""

#: includes/system-status/class-gf-system-report.php:873
msgid "Default Theme"
msgstr ""

#: includes/system-status/class-gf-system-report.php:878
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:44
#: tooltips.php:153
msgid "No-Conflict Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:889
msgid "Background updates"
msgstr ""

#: includes/system-status/class-gf-system-report.php:895
msgid "REST API v2"
msgstr ""

#: includes/system-status/class-gf-system-report.php:901
msgid "Orbital Style Filter"
msgstr ""

#: includes/system-status/class-gf-system-report.php:936
#: includes/system-status/class-gf-system-report.php:1033
msgid "Database Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:980
msgid "Table does not exist"
msgstr ""

#: includes/system-status/class-gf-system-report.php:986
msgid "Table has incorrect auto-increment settings."
msgstr ""

#: includes/system-status/class-gf-system-report.php:992
msgid "Table has not been upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1008
msgid "WARNING! Re-running the upgrade process is only recommended if you are currently experiencing issues with your database. This process may take several minutes to complete. 'OK' to upgrade. 'Cancel' to abort."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1015
msgid "Current Status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1017
msgid "%s%% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1020
msgid "Automatic background migration is disabled but the database needs to be upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1021
msgid "Force the migration manually"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1023
msgid "The database is currently being upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1025
msgid "As this site doesn't support background tasks the upgrade process will take longer than usual and the status will change infrequently."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1027
msgid "Force the upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1049
msgid "Upgrade database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1054
msgid "Your database version is out of date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1066
#: includes/system-status/class-gf-system-report.php:1082
msgid "Re-run database upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1071
msgid "Database upgrade failed."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1071
msgid "There are issues with your database."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1087
msgid "Database upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1087
msgid "Your database is up-to-date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1087
msgid "Warning: downgrading Gravity Forms is not recommended."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1243
msgid "Your system does not meet the minimum requirements for this Add-On (%d errors)."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1263
#: includes/system-status/class-gf-system-report.php:1270
#: includes/system-status/class-gf-system-report.php:1283
#: includes/system-status/class-gf-system-report.php:1346
#: includes/system-status/class-gf-system-report.php:1354
#: includes/system-status/class-gf-system-report.php:1528
#: includes/system-status/class-gf-system-report.php:1529
#: includes/system-status/class-gf-system-report.php:1544
#: includes/system-status/class-gf-system-report.php:1545
msgid "by"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1542
msgid "Parent"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1679
msgid "Site Locale"
msgstr ""

#. translators: %d: The ID of the currently logged in user.
#: includes/system-status/class-gf-system-report.php:1688
msgid "User (ID: %d) Locale"
msgstr ""

#: includes/system-status/class-gf-system-status.php:62
msgid "System Report"
msgstr ""

#: includes/system-status/class-gf-system-status.php:70
#: includes/system-status/class-gf-update.php:47
msgid "Updates"
msgstr ""

#: includes/system-status/class-gf-update.php:30
msgid "You don't have permissions to view this page"
msgstr ""

#: includes/system-status/class-gf-update.php:51
msgid "Plugin"
msgstr ""

#: includes/system-status/class-gf-update.php:90
msgid "Visit plugin page"
msgstr ""

#: includes/system-status/class-gf-update.php:145
msgid "Your version of Gravity Forms is up to date."
msgstr ""

#: includes/system-status/class-gf-update.php:154
#: includes/system-status/class-gf-update.php:163
msgid "There is a new version of Gravity Forms available."
msgstr ""

#: includes/system-status/class-gf-update.php:155
msgid "You can update to the latest version automatically or download the update and install it manually."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:91
msgid "Enter the form title"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:93
msgid "Use Template"
msgstr ""

#. translators: title of template
#: includes/template-library/config/class-gf-template-library-config.php:96
msgid "Use Template %s"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:97
msgid "Creating Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:98
msgid "Please enter a valid form title."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:101
msgid "Import failed."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:102
msgid "Close."
msgstr ""

#. translators: title of template
#: includes/template-library/config/class-gf-template-library-config.php:104
msgid "Preview %s"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:106
msgid "Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:107
msgid "Create Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:108
msgid "New Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:109
msgid "A new blank form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:110
msgid "A form description goes here"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:111
msgid "Explore Form Templates"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:112
msgid "Quickly create an amazing form by using a pre-made template, or start from scratch to tailor your form to your specific needs."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:137
msgid "A new form"
msgstr ""

#: includes/template-library/templates/templates.php:5
msgid "A simple and basic contact form with only two fields"
msgstr ""

#: includes/template-library/templates/templates.php:6
#: includes/template-library/templates/templates.php:17
msgid "Simple Contact Form"
msgstr ""

#: includes/template-library/templates/templates.php:18
msgid "Please get in contact using the form below..."
msgstr ""

#: includes/template-library/templates/templates.php:42
#: includes/template-library/templates/templates.php:408
#: includes/template-library/templates/templates.php:1273
#: includes/template-library/templates/templates.php:1654
#: includes/template-library/templates/templates.php:2294
#: includes/template-library/templates/templates.php:3111
#: includes/template-library/templates/templates.php:3917
#: includes/template-library/templates/templates.php:4626
#: includes/template-library/templates/templates.php:5757
#: includes/template-library/templates/templates.php:6742
#: includes/template-library/templates/templates.php:6964
#: includes/template-library/templates/templates.php:7521
#: includes/template-library/templates/templates.php:7819
#: includes/template-library/templates/templates.php:8317
#: includes/template-library/templates/templates.php:9355
#: includes/template-library/templates/templates.php:9704
msgid "Mr."
msgstr ""

#: includes/template-library/templates/templates.php:48
#: includes/template-library/templates/templates.php:414
#: includes/template-library/templates/templates.php:1277
#: includes/template-library/templates/templates.php:1660
#: includes/template-library/templates/templates.php:2300
#: includes/template-library/templates/templates.php:3117
#: includes/template-library/templates/templates.php:3923
#: includes/template-library/templates/templates.php:4632
#: includes/template-library/templates/templates.php:5763
#: includes/template-library/templates/templates.php:6746
#: includes/template-library/templates/templates.php:6968
#: includes/template-library/templates/templates.php:7525
#: includes/template-library/templates/templates.php:7825
#: includes/template-library/templates/templates.php:8323
#: includes/template-library/templates/templates.php:9361
#: includes/template-library/templates/templates.php:9708
msgid "Mrs."
msgstr ""

#: includes/template-library/templates/templates.php:54
#: includes/template-library/templates/templates.php:420
#: includes/template-library/templates/templates.php:1269
#: includes/template-library/templates/templates.php:1666
#: includes/template-library/templates/templates.php:2306
#: includes/template-library/templates/templates.php:3123
#: includes/template-library/templates/templates.php:3929
#: includes/template-library/templates/templates.php:4638
#: includes/template-library/templates/templates.php:5769
#: includes/template-library/templates/templates.php:6738
#: includes/template-library/templates/templates.php:6960
#: includes/template-library/templates/templates.php:7517
#: includes/template-library/templates/templates.php:7831
#: includes/template-library/templates/templates.php:8329
#: includes/template-library/templates/templates.php:9367
#: includes/template-library/templates/templates.php:9700
msgid "Miss"
msgstr ""

#: includes/template-library/templates/templates.php:60
#: includes/template-library/templates/templates.php:426
#: includes/template-library/templates/templates.php:1281
#: includes/template-library/templates/templates.php:1672
#: includes/template-library/templates/templates.php:2312
#: includes/template-library/templates/templates.php:3129
#: includes/template-library/templates/templates.php:3935
#: includes/template-library/templates/templates.php:4644
#: includes/template-library/templates/templates.php:5775
#: includes/template-library/templates/templates.php:6750
#: includes/template-library/templates/templates.php:6972
#: includes/template-library/templates/templates.php:7529
#: includes/template-library/templates/templates.php:7837
#: includes/template-library/templates/templates.php:8335
#: includes/template-library/templates/templates.php:9373
#: includes/template-library/templates/templates.php:9712
msgid "Ms."
msgstr ""

#: includes/template-library/templates/templates.php:66
#: includes/template-library/templates/templates.php:432
#: includes/template-library/templates/templates.php:1265
#: includes/template-library/templates/templates.php:1678
#: includes/template-library/templates/templates.php:2318
#: includes/template-library/templates/templates.php:3135
#: includes/template-library/templates/templates.php:3941
#: includes/template-library/templates/templates.php:4650
#: includes/template-library/templates/templates.php:5781
#: includes/template-library/templates/templates.php:6734
#: includes/template-library/templates/templates.php:6956
#: includes/template-library/templates/templates.php:7513
#: includes/template-library/templates/templates.php:7843
#: includes/template-library/templates/templates.php:8341
#: includes/template-library/templates/templates.php:9379
#: includes/template-library/templates/templates.php:9696
msgid "Dr."
msgstr ""

#: includes/template-library/templates/templates.php:72
#: includes/template-library/templates/templates.php:438
#: includes/template-library/templates/templates.php:1285
#: includes/template-library/templates/templates.php:1684
#: includes/template-library/templates/templates.php:2324
#: includes/template-library/templates/templates.php:3141
#: includes/template-library/templates/templates.php:3947
#: includes/template-library/templates/templates.php:4656
#: includes/template-library/templates/templates.php:5787
#: includes/template-library/templates/templates.php:6754
#: includes/template-library/templates/templates.php:6976
#: includes/template-library/templates/templates.php:7533
#: includes/template-library/templates/templates.php:7849
#: includes/template-library/templates/templates.php:8347
#: includes/template-library/templates/templates.php:9385
#: includes/template-library/templates/templates.php:9716
msgid "Prof."
msgstr ""

#: includes/template-library/templates/templates.php:78
#: includes/template-library/templates/templates.php:444
#: includes/template-library/templates/templates.php:1289
#: includes/template-library/templates/templates.php:1690
#: includes/template-library/templates/templates.php:2330
#: includes/template-library/templates/templates.php:3147
#: includes/template-library/templates/templates.php:3953
#: includes/template-library/templates/templates.php:4662
#: includes/template-library/templates/templates.php:5793
#: includes/template-library/templates/templates.php:6758
#: includes/template-library/templates/templates.php:6980
#: includes/template-library/templates/templates.php:7537
#: includes/template-library/templates/templates.php:7855
#: includes/template-library/templates/templates.php:8353
#: includes/template-library/templates/templates.php:9391
#: includes/template-library/templates/templates.php:9720
msgid "Rev."
msgstr ""

#: includes/template-library/templates/templates.php:212
msgid "Comments"
msgstr ""

#: includes/template-library/templates/templates.php:237
#: includes/template-library/templates/templates.php:1072
msgid "Please let us know what's on your mind. Have a question for us? Ask away."
msgstr ""

#: includes/template-library/templates/templates.php:270
#: includes/template-library/templates/templates.php:1187
#: includes/template-library/templates/templates.php:1576
#: includes/template-library/templates/templates.php:2011
#: includes/template-library/templates/templates.php:2858
#: includes/template-library/templates/templates.php:3668
#: includes/template-library/templates/templates.php:4501
#: includes/template-library/templates/templates.php:5630
#: includes/template-library/templates/templates.php:6657
#: includes/template-library/templates/templates.php:7432
#: includes/template-library/templates/templates.php:7736
#: includes/template-library/templates/templates.php:8240
#: includes/template-library/templates/templates.php:9279
#: includes/template-library/templates/templates.php:9614
#: includes/template-library/templates/templates.php:10283
msgid "Save and Continue Later"
msgstr ""

#: includes/template-library/templates/templates.php:303
#: includes/template-library/templates/templates.php:1219
#: includes/template-library/templates/templates.php:6687
#: includes/template-library/templates/templates.php:8270
#: includes/template-library/templates/templates.php:9309
#: tests/unit-tests/save-form/test-form-crud-handler.php:176
#: tests/unit-tests/save-form/test-form-crud-handler.php:307
#: tests/unit-tests/save-form/test-form-crud-handler.php:635
#: tests/unit-tests/save-form/test-form-crud-handler.php:861
#: tests/unit-tests/save-form/test-form-crud-handler.php:904
#: tests/unit-tests/save-form/test-form-crud-handler.php:968
#: tests/unit-tests/save-form/test-form-crud-handler.php:1011
msgid "Thank you for contacting us! We will get in touch with you shortly."
msgstr ""

#: includes/template-library/templates/templates.php:316
msgid "Hi there {Name (First):1.3}. We received the following information from you and will respond to your inquiry as quickly as possible."
msgstr ""

#: includes/template-library/templates/templates.php:327
msgid "An advanced contact form."
msgstr ""

#: includes/template-library/templates/templates.php:328
#: includes/template-library/templates/templates.php:339
msgid "Advanced Contact Form"
msgstr ""

#: includes/template-library/templates/templates.php:355
msgid "About You"
msgstr ""

#: includes/template-library/templates/templates.php:399
#: includes/template-library/templates/templates.php:4612
#: includes/template-library/templates/templates.php:6719
msgid "Your Name"
msgstr ""

#: includes/template-library/templates/templates.php:524
msgid "Your Address"
msgstr ""

#: includes/template-library/templates/templates.php:548
#: includes/template-library/templates/templates.php:2511
#: includes/template-library/templates/templates.php:2598
#: includes/template-library/templates/templates.php:3328
#: includes/template-library/templates/templates.php:3415
#: includes/template-library/templates/templates.php:4134
#: includes/template-library/templates/templates.php:4221
#: includes/template-library/templates/templates.php:4825
#: includes/template-library/templates/templates.php:5994
#: includes/template-library/templates/templates.php:7136
#: includes/template-library/templates/templates.php:8013
#: js.php:845
msgid "State / Province"
msgstr ""

#: includes/template-library/templates/templates.php:555
msgid "Zip / Postal Code"
msgstr ""

#: includes/template-library/templates/templates.php:614
msgid "We would love to chat with you. How can we get in touch?"
msgstr ""

#: includes/template-library/templates/templates.php:615
msgid "How Can We Reach You?"
msgstr ""

#: includes/template-library/templates/templates.php:657
msgid "Preferred Method of Contact"
msgstr ""

#: includes/template-library/templates/templates.php:704
#: includes/template-library/templates/templates.php:4738
msgid "Your Email Address"
msgstr ""

#: includes/template-library/templates/templates.php:767
#: includes/template-library/templates/templates.php:4876
msgid "Your Phone"
msgstr ""

#: includes/template-library/templates/templates.php:820
msgid "Best Time to Call You"
msgstr ""

#: includes/template-library/templates/templates.php:824
msgid "Select A Time"
msgstr ""

#: includes/template-library/templates/templates.php:828
msgid "12:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:832
msgid "12:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:836
msgid "1:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:840
msgid "1:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:844
msgid "2:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:848
msgid "2:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:852
msgid "3:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:856
msgid "3:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:860
msgid "4:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:864
msgid "4:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:868
msgid "5:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:872
msgid "5:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:876
msgid "6:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:880
msgid "6:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:884
msgid "7:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:888
msgid "7:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:892
msgid "8:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:896
msgid "8:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:900
msgid "9:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:904
msgid "9:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:908
msgid "10:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:912
msgid "10:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:916
msgid "11:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:920
msgid "11:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:924
msgid "12:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:928
msgid "12:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:932
msgid "1:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:936
msgid "1:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:940
msgid "2:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:944
msgid "2:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:948
msgid "3:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:952
msgid "3:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:956
msgid "4:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:960
msgid "4:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:964
msgid "5:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:968
msgid "5:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:972
msgid "6:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:976
msgid "6:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:980
msgid "7:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:984
msgid "7:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:988
msgid "8:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:992
msgid "8:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:996
msgid "9:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1000
msgid "9:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1004
msgid "10:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1008
msgid "10:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1012
msgid "11:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1016
msgid "11:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1073
msgid "What's on your mind?"
msgstr ""

#: includes/template-library/templates/templates.php:1116
msgid "Your Comments/Questions"
msgstr ""

#: includes/template-library/templates/templates.php:1230
msgid "A form that allows your users to enter a contest"
msgstr ""

#: includes/template-library/templates/templates.php:1231
msgid "Contest Entry Form"
msgstr ""

#: includes/template-library/templates/templates.php:1237
msgid "Form Template Library: Contest Entry Form"
msgstr ""

#: includes/template-library/templates/templates.php:1238
msgid "Enter our competition today to be in with a chance of winning..."
msgstr ""

#: includes/template-library/templates/templates.php:1243
msgid "Enter!"
msgstr ""

#: includes/template-library/templates/templates.php:1420
msgid "The answer is..."
msgstr ""

#: includes/template-library/templates/templates.php:1429
msgid "Answer A"
msgstr ""

#: includes/template-library/templates/templates.php:1435
msgid "Answer B"
msgstr ""

#: includes/template-library/templates/templates.php:1441
msgid "Answer C"
msgstr ""

#: includes/template-library/templates/templates.php:1483
msgid "Competition Terms and Conditions"
msgstr ""

#: includes/template-library/templates/templates.php:1508
msgid "<strong>I agree to the competition terms and conditions.</strong>"
msgstr ""

#: includes/template-library/templates/templates.php:1519
#: includes/template-library/templates/templates.php:5561
msgid "Terms and conditions placeholder."
msgstr ""

#: includes/template-library/templates/templates.php:1606
msgid "Thank you for entering our competition! The winners will be contacted via email."
msgstr ""

#: includes/template-library/templates/templates.php:1619
msgid "A donation form for multiple purposes"
msgstr ""

#: includes/template-library/templates/templates.php:1627
msgid "Help us provide care and support for vulnerable adults."
msgstr ""

#: includes/template-library/templates/templates.php:1819
msgid "Donation Amount"
msgstr ""

#: includes/template-library/templates/templates.php:1829
msgid "Choose how much you would like to donate."
msgstr ""

#: includes/template-library/templates/templates.php:1846
msgid "10 USD"
msgstr ""

#: includes/template-library/templates/templates.php:1852
msgid "50 USD"
msgstr ""

#: includes/template-library/templates/templates.php:1858
msgid "250 USD"
msgstr ""

#: includes/template-library/templates/templates.php:1864
msgid "Other amount"
msgstr ""

#: includes/template-library/templates/templates.php:1891
msgid "Other Amount"
msgstr ""

#: includes/template-library/templates/templates.php:2038
msgid "Thank you for your contribution! We appreciate your support."
msgstr ""

#: includes/template-library/templates/templates.php:2049
#: includes/template-library/templates/templates.php:2057
msgid "A form that allows you to sell products and let your customers pay via different payment gateways"
msgstr ""

#: includes/template-library/templates/templates.php:2050
#: includes/template-library/templates/templates.php:2056
msgid "eCommerce Form"
msgstr ""

#: includes/template-library/templates/templates.php:2070
#: includes/template-library/templates/templates.php:2927
#: includes/template-library/templates/templates.php:3733
msgid "My Super Awesome Product"
msgstr ""

#: includes/template-library/templates/templates.php:2095
#: includes/template-library/templates/templates.php:2951
#: includes/template-library/templates/templates.php:3757
msgid "This is my super awesome product. It's the best, so everyone should buy it!"
msgstr ""

#: includes/template-library/templates/templates.php:2131
#: includes/template-library/templates/templates.php:2990
#: includes/template-library/templates/templates.php:3796
msgid "Another Amazing Product"
msgstr ""

#: includes/template-library/templates/templates.php:2156
#: includes/template-library/templates/templates.php:3014
#: includes/template-library/templates/templates.php:3820
msgid "If you loved the first product, you're really going to love this one. Don't miss out, order yours while they're still in stock."
msgstr ""

#: includes/template-library/templates/templates.php:2192
msgid "Subtotal"
msgstr ""

#: includes/template-library/templates/templates.php:2484
#: includes/template-library/templates/templates.php:3301
#: includes/template-library/templates/templates.php:4107
msgid "Billing Address"
msgstr ""

#: includes/template-library/templates/templates.php:2571
#: includes/template-library/templates/templates.php:3388
#: includes/template-library/templates/templates.php:4194
msgid "Shipping Address"
msgstr ""

#: includes/template-library/templates/templates.php:2683
#: includes/template-library/templates/templates.php:3499
#: includes/template-library/templates/templates.php:4305
msgid "Standard Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:2689
#: includes/template-library/templates/templates.php:3505
#: includes/template-library/templates/templates.php:4311
msgid "Express Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:2695
#: includes/template-library/templates/templates.php:3511
#: includes/template-library/templates/templates.php:4317
msgid "Overnight Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:2781
msgid "PayPal"
msgstr ""

#: includes/template-library/templates/templates.php:2892
#: includes/template-library/templates/templates.php:3699
#: includes/template-library/templates/templates.php:4536
msgid "Thank you for shopping with us! Your payment was successfully completed."
msgstr ""

#: includes/template-library/templates/templates.php:2903
#: includes/template-library/templates/templates.php:2910
msgid "Stripe Checkout Form"
msgstr ""

#: includes/template-library/templates/templates.php:2904
#: includes/template-library/templates/templates.php:2911
msgid "A form that allows you to sell products and let your customers pay via Stripe"
msgstr ""

#: includes/template-library/templates/templates.php:3595
msgid "Card Details"
msgstr ""

#: includes/template-library/templates/templates.php:3600
#: includes/template-library/templates/templates.php:4416
#: js.php:871
msgid "Card Type"
msgstr ""

#: includes/template-library/templates/templates.php:3709
#: includes/template-library/templates/templates.php:3716
msgid "PayPal Checkout Form"
msgstr ""

#: includes/template-library/templates/templates.php:3710
#: includes/template-library/templates/templates.php:3717
msgid "A form that allows you to sell products and let your customers pay via PayPal"
msgstr ""

#: includes/template-library/templates/templates.php:4547
msgid "Allow your users to apply for a job"
msgstr ""

#: includes/template-library/templates/templates.php:4548
#: includes/template-library/templates/templates.php:4555
msgid "Employment Application Form"
msgstr ""

#: includes/template-library/templates/templates.php:4561
msgid "Submit Application"
msgstr ""

#: includes/template-library/templates/templates.php:4569
msgid "Your Personal Information"
msgstr ""

#: includes/template-library/templates/templates.php:4921
msgid "Best Time To Call You"
msgstr ""

#: includes/template-library/templates/templates.php:4929
msgid "Mornings"
msgstr ""

#: includes/template-library/templates/templates.php:4935
msgid "Early Afternoon"
msgstr ""

#: includes/template-library/templates/templates.php:4941
msgid "Late Afternoon"
msgstr ""

#: includes/template-library/templates/templates.php:4947
msgid "Early Evening"
msgstr ""

#: includes/template-library/templates/templates.php:4954
msgid "When is the best time for us to reach you via telephone?"
msgstr ""

#: includes/template-library/templates/templates.php:5032
#: includes/template-library/templates/templates.php:5075
msgid "Position You're Applying For"
msgstr ""

#: includes/template-library/templates/templates.php:5101
msgid "IT/Technical"
msgstr ""

#: includes/template-library/templates/templates.php:5107
msgid "Clerical/Accounting"
msgstr ""

#: includes/template-library/templates/templates.php:5113
msgid "Facilities Maintenance"
msgstr ""

#: includes/template-library/templates/templates.php:5157
msgid "Hours You Are Available for Work"
msgstr ""

#: includes/template-library/templates/templates.php:5164
msgid "Please tell us what hours you are available for work each day of the week."
msgstr ""

#: includes/template-library/templates/templates.php:5234
msgid "Previous Employment"
msgstr ""

#: includes/template-library/templates/templates.php:5277
msgid "Your Previous Employers"
msgstr ""

#: includes/template-library/templates/templates.php:5284
msgid "Please list your previous employers, the dates you worked and the position you held"
msgstr ""

#: includes/template-library/templates/templates.php:5299
msgid "Employer"
msgstr ""

#: includes/template-library/templates/templates.php:5305
msgid "Dates"
msgstr ""

#: includes/template-library/templates/templates.php:5311
msgid "Position"
msgstr ""

#: includes/template-library/templates/templates.php:5391
msgid "More About You"
msgstr ""

#: includes/template-library/templates/templates.php:5434
msgid "Tell Us About Yourself"
msgstr ""

#: includes/template-library/templates/templates.php:5478
msgid "Upload Your Resume"
msgstr ""

#: includes/template-library/templates/templates.php:5485
msgid "Upload your resume in .pdf, .doc or .docx format"
msgstr ""

#: includes/template-library/templates/templates.php:5525
msgid "Terms and Conditions"
msgstr ""

#: includes/template-library/templates/templates.php:5550
msgid "<strong>I agree to the terms and conditions.</strong>"
msgstr ""

#: includes/template-library/templates/templates.php:5666
msgid "Thank you for submitting your application! We will get in touch with you shortly."
msgstr ""

#: includes/template-library/templates/templates.php:5678
msgid "Let your users book tickets for an event"
msgstr ""

#: includes/template-library/templates/templates.php:5679
#: includes/template-library/templates/templates.php:5685
#: includes/template-library/templates/templates.php:8884
#: includes/template-library/templates/templates.php:8924
msgid "Event Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:5686
msgid "Please complete this form to register for the event."
msgstr ""

#: includes/template-library/templates/templates.php:5699
msgid "Contact Details"
msgstr ""

#: includes/template-library/templates/templates.php:6098
msgid "Event Details"
msgstr ""

#: includes/template-library/templates/templates.php:6150
msgid "Male"
msgstr ""

#: includes/template-library/templates/templates.php:6156
msgid "Female"
msgstr ""

#: includes/template-library/templates/templates.php:6162
msgid "Non-binary"
msgstr ""

#: includes/template-library/templates/templates.php:6168
msgid "Agender"
msgstr ""

#: includes/template-library/templates/templates.php:6174
msgid "My gender isn't listed"
msgstr ""

#: includes/template-library/templates/templates.php:6231
msgid "16-24"
msgstr ""

#: includes/template-library/templates/templates.php:6261
msgid "65+"
msgstr ""

#: includes/template-library/templates/templates.php:6303
msgid "How did you hear about this event?"
msgstr ""

#: includes/template-library/templates/templates.php:6312
#: includes/template-library/templates/templates.php:10138
msgid "Social Media"
msgstr ""

#: includes/template-library/templates/templates.php:6318
msgid "Google"
msgstr ""

#: includes/template-library/templates/templates.php:6324
#: includes/template-library/templates/templates.php:10150
msgid "Word of Mouth"
msgstr ""

#: includes/template-library/templates/templates.php:6330
msgid "Refer a Friend"
msgstr ""

#: includes/template-library/templates/templates.php:6336
msgid "Past Participant"
msgstr ""

#: includes/template-library/templates/templates.php:6472
msgid "Ticket Type"
msgstr ""

#: includes/template-library/templates/templates.php:6499
msgid "Early Bird Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:6505
msgid "Premium Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:6511
msgid "VIP Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:6540
msgid "Number of tickets needed"
msgstr ""

#: includes/template-library/templates/templates.php:6698
msgid "Allow your users to purchase a gift certificate"
msgstr ""

#: includes/template-library/templates/templates.php:6699
msgid "Gift Certificate Form"
msgstr ""

#: includes/template-library/templates/templates.php:6705
msgid "Gift Certificate Order Form"
msgstr ""

#: includes/template-library/templates/templates.php:6706
msgid "Purchase a gift certificate today for your nearest and dearest..."
msgstr ""

#: includes/template-library/templates/templates.php:6711
msgid "Buy Now"
msgstr ""

#: includes/template-library/templates/templates.php:6828
msgid "Your Email"
msgstr ""

#: includes/template-library/templates/templates.php:6885
msgid "How would you like the gift certificate delivered?"
msgstr ""

#: includes/template-library/templates/templates.php:6900
msgid "Mail"
msgstr ""

#: includes/template-library/templates/templates.php:6942
msgid "Name of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7046
msgid "Email of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7111
msgid "Address of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7247
msgid "Add a message to your gift certificate..."
msgstr ""

#: includes/template-library/templates/templates.php:7291
msgid "Gift Certificate Amount"
msgstr ""

#: includes/template-library/templates/templates.php:7317
msgid "$30"
msgstr ""

#: includes/template-library/templates/templates.php:7323
msgid "$50"
msgstr ""

#: includes/template-library/templates/templates.php:7329
msgid "$100"
msgstr ""

#: includes/template-library/templates/templates.php:7462
msgid "Thank you for making a gift certificate purchase! You should receive an email from us shortly with more information."
msgstr ""

#: includes/template-library/templates/templates.php:7478
msgid "Let users sign up to your newsletter"
msgstr ""

#: includes/template-library/templates/templates.php:7479
msgid "Newsletter Signup Form"
msgstr ""

#: includes/template-library/templates/templates.php:7485
msgid "Form Template Library: Newsletter Signup Form"
msgstr ""

#: includes/template-library/templates/templates.php:7486
msgid "If you want to keep up to date with what's happening on the blog, sign up for our newsletter!"
msgstr ""

#: includes/template-library/templates/templates.php:7491
msgid "Keep me up to date!"
msgstr ""

#: includes/template-library/templates/templates.php:7661
msgid "Privacy"
msgstr ""

#: includes/template-library/templates/templates.php:7669
msgid "I agree with the storage and handling of my data by this website. - <a target=\"_blank\" href=\"#\" rel=\"noopener noreferrer\">Privacy Policy</a> <abbr class=\"wpgdprc-required\" title=\"You need to accept this checkbox.\">*</abbr>"
msgstr ""

#: includes/template-library/templates/templates.php:7670
msgid "I agree with the storage and handling of my data by this website."
msgstr ""

#: includes/template-library/templates/templates.php:7678
msgid "I agree with the storage and handling of my data by this website. - <a target=\"_blank\" href=\"#\" rel=\"noopener noreferrer\">Privacy  Policy</a> <abbr class=\"wpgdprc-required\" title=\"You need to accept this checkbox.\">*</abbr>"
msgstr ""

#: includes/template-library/templates/templates.php:7770
msgid "Thank you for signing up. Be on the lookout for our monthly newsletter!"
msgstr ""

#: includes/template-library/templates/templates.php:7783
msgid "Helps users ask for a quote for a certain service or product you are selling on your website"
msgstr ""

#: includes/template-library/templates/templates.php:7784
#: includes/template-library/templates/templates.php:7790
#: includes/template-library/templates/templates.php:8879
#: includes/template-library/templates/templates.php:8918
msgid "Request a Quote Form"
msgstr ""

#: includes/template-library/templates/templates.php:7791
msgid "Please fill out the information below and we will be in touch shortly with your personalized quote."
msgstr ""

#: includes/template-library/templates/templates.php:8073
msgid "Please select the service/s you require..."
msgstr ""

#: includes/template-library/templates/templates.php:8081
#: includes/template-library/templates/templates.php:8114
msgid "Landscape Gardening"
msgstr ""

#: includes/template-library/templates/templates.php:8087
#: includes/template-library/templates/templates.php:8119
msgid "Ground Maintenance"
msgstr ""

#: includes/template-library/templates/templates.php:8093
#: includes/template-library/templates/templates.php:8124
msgid "Tree Surgery Services"
msgstr ""

#: includes/template-library/templates/templates.php:8099
#: includes/template-library/templates/templates.php:8129
msgid "Fencing"
msgstr ""

#: includes/template-library/templates/templates.php:8105
#: includes/template-library/templates/templates.php:8134
msgid "Clearance"
msgstr ""

#: includes/template-library/templates/templates.php:8281
msgid "Get feedback about your product using a survey form"
msgstr ""

#: includes/template-library/templates/templates.php:8282
#: includes/template-library/templates/templates.php:8288
#: includes/template-library/templates/templates.php:8864
#: includes/template-library/templates/templates.php:8900
msgid "Survey Form"
msgstr ""

#: includes/template-library/templates/templates.php:8289
msgid "Tell us what you think about Acme Products..."
msgstr ""

#: includes/template-library/templates/templates.php:8469
msgid "Company Name"
msgstr ""

#: includes/template-library/templates/templates.php:8556
msgid "Rate Acme Products"
msgstr ""

#: includes/template-library/templates/templates.php:8569
msgid "Terrible"
msgstr ""

#: includes/template-library/templates/templates.php:8575
msgid "Not so great"
msgstr ""

#: includes/template-library/templates/templates.php:8587
msgid "Pretty good"
msgstr ""

#: includes/template-library/templates/templates.php:8593
msgid "Excellent"
msgstr ""

#: includes/template-library/templates/templates.php:8601
#: includes/template-library/templates/templates.php:8709
#: includes/template-library/templates/templates.php:8793
#: includes/template-library/templates/templates.php:8932
#: includes/template-library/templates/templates.php:9034
#: includes/template-library/templates/templates.php:9135
#: includes/template-library/templates/templates.php:9204
msgid "First row"
msgstr ""

#: includes/template-library/templates/templates.php:8605
#: includes/template-library/templates/templates.php:8713
#: includes/template-library/templates/templates.php:8797
#: includes/template-library/templates/templates.php:8936
#: includes/template-library/templates/templates.php:9038
#: includes/template-library/templates/templates.php:9139
#: includes/template-library/templates/templates.php:9208
msgid "Second row"
msgstr ""

#: includes/template-library/templates/templates.php:8609
#: includes/template-library/templates/templates.php:8717
#: includes/template-library/templates/templates.php:8801
#: includes/template-library/templates/templates.php:8940
#: includes/template-library/templates/templates.php:9042
#: includes/template-library/templates/templates.php:9143
#: includes/template-library/templates/templates.php:9212
msgid "Third row"
msgstr ""

#: includes/template-library/templates/templates.php:8613
#: includes/template-library/templates/templates.php:8721
#: includes/template-library/templates/templates.php:8805
#: includes/template-library/templates/templates.php:8944
#: includes/template-library/templates/templates.php:9046
#: includes/template-library/templates/templates.php:9147
#: includes/template-library/templates/templates.php:9216
msgid "Fourth row"
msgstr ""

#: includes/template-library/templates/templates.php:8617
#: includes/template-library/templates/templates.php:8725
#: includes/template-library/templates/templates.php:8809
#: includes/template-library/templates/templates.php:8948
#: includes/template-library/templates/templates.php:9050
#: includes/template-library/templates/templates.php:9151
#: includes/template-library/templates/templates.php:9220
msgid "Fifth row"
msgstr ""

#: includes/template-library/templates/templates.php:8658
msgid "How many sites do you have Acme Products installed on?"
msgstr ""

#: includes/template-library/templates/templates.php:8671
msgid "Just one"
msgstr ""

#: includes/template-library/templates/templates.php:8677
msgid "2-5"
msgstr ""

#: includes/template-library/templates/templates.php:8683
msgid "6-10"
msgstr ""

#: includes/template-library/templates/templates.php:8689
msgid "11-25"
msgstr ""

#: includes/template-library/templates/templates.php:8695
msgid "26-100"
msgstr ""

#: includes/template-library/templates/templates.php:8701
msgid "100+"
msgstr ""

#: includes/template-library/templates/templates.php:8766
msgid "Do you use Acme Products to sell products or services?"
msgstr ""

#: includes/template-library/templates/templates.php:8850
msgid "What types of forms have you created with Acme Products?"
msgstr ""

#: includes/template-library/templates/templates.php:8989
msgid "Rank these add-ons based on how useful they are to you."
msgstr ""

#: includes/template-library/templates/templates.php:9002
msgid "Stripe"
msgstr ""

#: includes/template-library/templates/templates.php:9008
msgid "MailChimp"
msgstr ""

#: includes/template-library/templates/templates.php:9014
msgid "Zapier"
msgstr ""

#: includes/template-library/templates/templates.php:9020
msgid "Surveys"
msgstr ""

#: includes/template-library/templates/templates.php:9026
msgid "Dropbox"
msgstr ""

#: includes/template-library/templates/templates.php:9090
msgid "Acme Products fulfils all my form needs"
msgstr ""

#: includes/template-library/templates/templates.php:9103
msgid "Strongly disagree"
msgstr ""

#: includes/template-library/templates/templates.php:9127
msgid "Strongly agree"
msgstr ""

#: includes/template-library/templates/templates.php:9190
msgid "How could Acme Products be improved?"
msgstr ""

#: includes/template-library/templates/templates.php:9320
msgid "Let your users register to your website easily"
msgstr ""

#: includes/template-library/templates/templates.php:9321
#: includes/template-library/templates/templates.php:9327
msgid "User Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:9328
msgid "Please complete the following form to register on our site. Thanks."
msgstr ""

#: includes/template-library/templates/templates.php:9513
msgid "Username"
msgstr ""

#: includes/template-library/templates/templates.php:9647
msgid "Thank you for registering! You should receive an email from us shortly containing your account information."
msgstr ""

#: includes/template-library/templates/templates.php:9660
msgid "Helps your users register to a webinar"
msgstr ""

#: includes/template-library/templates/templates.php:9661
#: includes/template-library/templates/templates.php:9667
msgid "Webinar Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:9668
msgid "Register for our latest webinar..."
msgstr ""

#: includes/template-library/templates/templates.php:9673
msgid "Register Today"
msgstr ""

#: includes/template-library/templates/templates.php:9857
msgid "Company Website"
msgstr ""

#: includes/template-library/templates/templates.php:9903
msgid "Position / Job Title"
msgstr ""

#: includes/template-library/templates/templates.php:9950
msgid "Industry Type"
msgstr ""

#: includes/template-library/templates/templates.php:9959
msgid "Advertising"
msgstr ""

#: includes/template-library/templates/templates.php:9965
msgid "Agriculture"
msgstr ""

#: includes/template-library/templates/templates.php:9971
msgid "Banking"
msgstr ""

#: includes/template-library/templates/templates.php:9977
msgid "Construction"
msgstr ""

#: includes/template-library/templates/templates.php:9983
msgid "Creatives"
msgstr ""

#: includes/template-library/templates/templates.php:9995
msgid "Entertainment"
msgstr ""

#: includes/template-library/templates/templates.php:10001
msgid "Fashion"
msgstr ""

#: includes/template-library/templates/templates.php:10007
msgid "Finance"
msgstr ""

#: includes/template-library/templates/templates.php:10013
msgid "Hospitality"
msgstr ""

#: includes/template-library/templates/templates.php:10031
msgid "Services"
msgstr ""

#: includes/template-library/templates/templates.php:10082
msgid "Do you have any questions you would like to ask our speakers?"
msgstr ""

#: includes/template-library/templates/templates.php:10129
msgid "How did you hear about this webinar?"
msgstr ""

#: includes/template-library/templates/templates.php:10144
msgid "Advertisement"
msgstr ""

#: includes/template-library/templates/templates.php:10197
msgid "Want to keep up-to-date with our latest news and announcements?"
msgstr ""

#: includes/template-library/templates/templates.php:10315
msgid "Thank you for registering for our webinar! Keep an eye out for an email from us containing more information."
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:11
#: widget.php:163
msgid "Advanced Options"
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:17
msgid "Update Form"
msgstr ""

#: includes/upload.php:29
#: includes/upload.php:58
msgid "Failed to upload file."
msgstr ""

#: includes/upload.php:135
#: includes/upload.php:266
#: includes/upload.php:276
msgid "Upload unsuccessful"
msgstr ""

#: includes/upload.php:199
msgid "Failed to open temp directory."
msgstr ""

#: includes/upload.php:225
#: includes/upload.php:249
msgid "Failed to open input stream."
msgstr ""

#: includes/upload.php:232
#: includes/upload.php:255
msgid "Failed to open output stream."
msgstr ""

#: includes/upload.php:235
msgid "Failed to move uploaded file."
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:29
#: includes/webapi/webapi.php:420
msgid "Permissions"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:30
#: includes/webapi/webapi.php:430
msgid "Last Access"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:80
#: includes/webapi/webapi.php:1098
msgid "Never Accessed"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:94
msgid "You don't have any API keys. Let's go %1$screate one%2$s!"
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:354
msgid "Consumer secret is invalid."
msgstr ""

#. translators: %s: amount of errors
#: includes/webapi/v2/class-gf-rest-authentication.php:474
msgid "Missing OAuth parameter %s"
msgid_plural "Missing OAuth parameters %s"
msgstr[0] ""
msgstr[1] ""

#: includes/webapi/v2/class-gf-rest-authentication.php:520
msgid "Consumer key is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:568
msgid "Invalid signature - failed to sort parameters."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:577
msgid "Invalid signature - signature method is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:587
msgid "Invalid signature - provided signature does not match."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:669
msgid "Invalid timestamp."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:679
msgid "Invalid nonce - nonce has already been used."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:749
msgid "The API key provided does not have read permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:757
msgid "The API key provided does not have write permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:764
msgid "Unknown request method."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:804
msgid "Gravity Forms API. Use a consumer key in the username field and a consumer secret in the password field."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:94
#: includes/webapi/v2/includes/controllers/class-controller-entries.php:181
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:57
msgid "Invalid entry id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:199
msgid "The entry has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:336
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:213
#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:231
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:337
msgid "Missing entry JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:86
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:122
msgid "Error retrieving notes."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notifications.php:61
msgid "Form not found."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:54
#: includes/webapi/webapi.php:1573
msgid "No property values were found in the request body"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:57
#: includes/webapi/webapi.php:1575
msgid "Property values should be sent as an array"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:74
#: includes/webapi/webapi.php:1508
msgid "Entry updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:113
msgid "Missing Key Value Pairs JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:55
msgid "Feed updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:70
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:173
msgid "Invalid JSON. Properties should be sent as key value pairs."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:101
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:257
msgid "Unique identifier for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:106
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:262
msgid "The Form ID for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:110
msgid "Indicates if the feed is active or inactive."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:114
msgid "The position of the feed on the feeds list page and when processed; for add-ons which support feed ordering."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:118
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:268
msgid "The JSON string containing the feed meta."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:122
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:273
msgid "The add-on the feed belongs to."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:108
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:138
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:223
msgid "Invalid feed id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:305
msgid "Unique identifier for the resource."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:310
msgid "The Form ID for the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:316
msgid "The date the entry was created, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:321
msgid "The date the entry was updated, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:326
msgid "Whether the entry is starred."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:331
msgid "Whether the entry has been read."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:336
msgid "The IP address of the entry creator."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:341
msgid "The URL where the form was embedded."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:346
msgid "The user agent string for the browser used to submit the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:351
msgid "The status of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:356
msgid "The date of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:361
msgid "The amount of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:366
msgid "The payment method for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:371
msgid "The transaction ID for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:376
msgid "Whether the transaction has been fulfilled, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:381
msgid "The user ID of the entry submitter."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:386
msgid "The type of the transaction, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:391
msgid "The status of the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:192
msgid "Missing feed JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:212
msgid "Missing add-on slug"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:217
msgid "Missing feed meta"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:145
msgid "The input values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:149
msgid "The field values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:238
msgid "Invalid form id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:257
msgid "The form has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:399
msgid "The Form object must be sent as a JSON string in the request body with the content-type header set to application/json."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-notes.php:78
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:225
msgid "Invalid note id."
msgstr ""

#: includes/webapi/webapi.php:422
msgid "Read"
msgstr ""

#: includes/webapi/webapi.php:423
msgid "Write"
msgstr ""

#: includes/webapi/webapi.php:424
msgid "Read/Write"
msgstr ""

#: includes/webapi/webapi.php:436
msgid "Consumer Key"
msgstr ""

#: includes/webapi/webapi.php:442
msgid "Consumer Secret"
msgstr ""

#: includes/webapi/webapi.php:458
msgid "Gravity Forms API Settings"
msgstr ""

#: includes/webapi/webapi.php:531
#: includes/webapi/webapi.php:552
msgid "The Gravity Forms API allows developers to interact with this install via a JSON REST API."
msgstr ""

#: includes/webapi/webapi.php:535
msgid "Requirements check"
msgstr ""

#: includes/webapi/webapi.php:556
msgid "Enable access to the API"
msgstr ""

#: includes/webapi/webapi.php:567
msgid "Authentication ( API version 2 )"
msgstr ""

#: includes/webapi/webapi.php:570
msgid "Create an API Key below to use the REST API version 2. Alternatively, you can use cookie authentication which is supported for logged in users. %1$sVisit our documentation pages%2$s for more information."
msgstr ""

#: includes/webapi/webapi.php:578
msgid "API Keys"
msgstr ""

#: includes/webapi/webapi.php:584
msgid "Authentication ( API version 1 )"
msgstr ""

#: includes/webapi/webapi.php:587
msgid "Configure your API Key below to use the REST API version 1. Alternatively, you can use cookie authentication which is supported for logged in users. %1$sVisit our documentation pages%2$s for more information."
msgstr ""

#: includes/webapi/webapi.php:595
msgid "Public API Key"
msgstr ""

#: includes/webapi/webapi.php:603
msgid "Private API Key"
msgstr ""

#: includes/webapi/webapi.php:611
msgid "QR Code"
msgstr ""

#: includes/webapi/webapi.php:617
msgid "Impersonate account"
msgstr ""

#: includes/webapi/webapi.php:694
msgid "Permalinks are not in the correct format."
msgstr ""

#: includes/webapi/webapi.php:699
msgid "Change the %sWordPress Permalink Settings%s from default to any of the other options to get started."
msgstr ""

#: includes/webapi/webapi.php:707
msgid "Show/hide QR Code"
msgstr ""

#: includes/webapi/webapi.php:1086
msgid "Unable to retrieve key."
msgstr ""

#: includes/webapi/webapi.php:1126
msgid "You must provide a description."
msgstr ""

#: includes/webapi/webapi.php:1133
msgid "API Key successfully updated."
msgstr ""

#: includes/webapi/webapi.php:1135
msgid "Make sure you have copied the consumer key and secret below. They will not be available once you leave this page."
msgstr ""

#: includes/webapi/webapi.php:1137
msgid "Unable to save API key."
msgstr ""

#: includes/webapi/webapi.php:1142
msgid "Unable to process request."
msgstr ""

#: includes/webapi/webapi.php:1290
msgid "Feeds deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1329
msgid "Feeds updated: %d"
msgstr ""

#: includes/webapi/webapi.php:1446
msgid "Forms deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1508
msgid "Entries updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1629
msgid "Forms updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1629
msgid "Form updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1666
msgid "Entries deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:2368
msgid "Not authorized"
msgstr ""

#: includes/webapi/webapi.php:2373
msgid "Permission denied"
msgstr ""

#: includes/webapi/webapi.php:2378
msgid "Forbidden"
msgstr ""

#: includes/webapi/webapi.php:2383
msgid "Bad request"
msgstr ""

#: includes/webapi/webapi.php:2388
msgid "Not found"
msgstr ""

#: includes/webapi/webapi.php:2393
msgid "Not implemented"
msgstr ""

#: includes/webapi/webapi.php:2398
msgid "Internal Error"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:18
msgid "Gravity Forms will download important bug fixes, security enhancements and plugin updates automatically. Updates are extremely important to the security of your WordPress site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:24
msgid "This feature is activated by default unless you opt to disable it below. We only recommend disabling background updates if you intend on managing updates manually."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:35
msgid "Updates will only be available if you have entered a valid License Key"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:46
msgid "Keep background updates enabled"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:52
msgid "Turn off background updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:58
msgid "Are you sure?"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:61
msgid "By disabling background updates your site may not get critical bug fixes and security enhancements. We only recommend doing this if you are experienced at managing a WordPress site and accept the risks involved in manually keeping your WordPress site updated."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:66
msgid "I understand and accept the risk of not enabling background updates."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:94
msgid "Background Updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:102
msgid "Please accept the terms."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:12
msgid "Congratulations! Click the 'Create A Form' button to get started."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:19
msgid "Installation Complete"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:23
msgid "Create A Form"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:21
msgid "Enter your Gravity Forms License Key below.  Your key unlocks access to automatic updates, the add-on installer, and support.  You can find your key on the My Account page on the %sGravity Forms%s site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:25
msgid "Enter Your License Key"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:39
msgid "If you don't enter a valid license key, you will not be able to update Gravity Forms when important bug fixes and security enhancements are released. This can be a serious security risk for your site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:44
msgid "I understand the risks of not providing a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:62
msgid "Please enter a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:68
msgid "Invalid or Expired Key : Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:75
msgid "Please accept the terms"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:47
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:58
msgid "On"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:48
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:59
msgid "Off"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:50
msgid "Set this to ON to prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:55
#: settings.php:602
msgid "Toolbar Menu"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:61
msgid "Set this to ON to display the Forms menu in the WordPress top toolbar. The Forms menu will display the latest ten forms recently opened in the form editor."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:68
#: settings.php:679
#: tooltips.php:158
msgid "Akismet Integration"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:74
#: settings.php:680
#: tooltips.php:158
msgid "Protect your form entries from spam using Akismet."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:84
msgid "Global Settings"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step.php:106
msgid "Back"
msgstr ""

#: js.php:29
msgid "Item has been deleted."
msgstr ""

#: js.php:62
msgid "Item has been saved."
msgstr ""

#: js.php:125
msgid "Completed"
msgstr ""

#: js.php:154
msgid "Select a category"
msgstr ""

#: js.php:172
msgid "Parameter Name:"
msgstr ""

#: js.php:177
msgid "Dynamic Population Parameter Names"
msgstr ""

#: js.php:177
msgid "Parameter Name"
msgstr ""

#: js.php:199
msgid "Default Value:"
msgstr ""

#: js.php:224
msgid "Placeholder:"
msgstr ""

#: js.php:252
msgid "Autocomplete Attribute:"
msgstr ""

#: js.php:254
#: js.php:258
msgid "List of valid attributes"
msgstr ""

#: js.php:259
msgid "Autocomplete Attributes"
msgstr ""

#: js.php:259
msgid "Attribute"
msgstr ""

#: js.php:287
msgid "Sub-Label:"
msgstr ""

#: js.php:296
msgid "Custom Sub-Label"
msgstr ""

#: js.php:330
#: js.php:333
#: js.php:347
msgid "MM"
msgstr ""

#: js.php:335
msgid "DD"
msgstr ""

#: js.php:337
msgid "YYYY"
msgstr ""

#: js.php:346
#: js.php:1183
msgid "Hour"
msgstr ""

#: js.php:348
#: js.php:1184
msgid "Minute"
msgstr ""

#: js.php:381
msgid "Same as previous"
msgstr ""

#: js.php:459
msgid "is no longer necessary."
msgstr ""

#: js.php:495
msgid "This field is not associated with a product. Please add a Product Field to the form."
msgstr ""

#: js.php:513
msgid "Deleted Field"
msgstr ""

#: js.php:544
msgid "Column 1"
msgstr ""

#: js.php:544
msgid "Column 2"
msgstr ""

#: js.php:544
msgid "Column 3"
msgstr ""

#: js.php:654
msgid "You are about to move this form to the trash. 'Cancel' to stop, 'OK' to move to trash."
msgstr ""

#: js.php:676
msgid "Section Break"
msgstr ""

#: js.php:692
msgid "HTML Block"
msgstr ""

#: js.php:718
#: js.php:732
#: js.php:747
#: js.php:778
#: js.php:806
#: js.php:1094
msgid "Untitled"
msgstr ""

#: js.php:721
#: js.php:737
#: js.php:738
#: js.php:750
#: js.php:781
#: js.php:817
#: js.php:818
msgid "First Choice"
msgstr ""

#: js.php:721
#: js.php:737
#: js.php:738
#: js.php:750
#: js.php:781
#: js.php:817
#: js.php:818
msgid "Second Choice"
msgstr ""

#: js.php:721
#: js.php:737
#: js.php:738
#: js.php:750
#: js.php:781
#: js.php:817
#: js.php:818
msgid "Third Choice"
msgstr ""

#: js.php:936
msgid "Hidden Field"
msgstr ""

#: js.php:940
msgid "Post Title"
msgstr ""

#: js.php:944
msgid "Post Body"
msgstr ""

#: js.php:948
msgid "Post Excerpt"
msgstr ""

#: js.php:953
msgid "Post Tags"
msgstr ""

#: js.php:960
msgid "Post Custom Field"
msgstr ""

#: js.php:979
msgid "Product Name"
msgstr ""

#: js.php:1025
msgid "First Option"
msgstr ""

#: js.php:1025
msgid "Second Option"
msgstr ""

#: js.php:1025
msgid "Third Option"
msgstr ""

#: js.php:1036
msgid "Donation"
msgstr ""

#: js.php:1080
msgid "I agree to the privacy policy."
msgstr ""

#: js.php:1081
msgid "Enter consent agreement text here.  The Consent Field will store this agreement text with the form entry in order to track what the user has consented to."
msgstr ""

#: js.php:1591
msgid "recommended size:"
msgstr ""

#: js.php:1603
#: js.php:1636
msgid "Add choice"
msgstr ""

#: js.php:1606
#: js.php:1639
msgid "Delete choice"
msgstr ""

#: js.php:1671
msgid "Select a field"
msgstr ""

#: js.php:1690
msgid "The Multi Select field type is hard to use for people who cannot use a mouse. Please select a different field type to improve the accessibility of your form."
msgstr ""

#: js.php:1691
msgid "Users can enter a date in the field without using the date picker. Display the date format so they know what is the specified format."
msgstr ""

#: js.php:1692
msgid "The datepicker is not accessible for users who rely on the keyboard or screen reader. Please select a different input type to improve the accessibility of your form."
msgstr ""

#: js.php:1693
msgid "The Enhanced User Interface is not accessible for screen reader users and people who cannot use a mouse."
msgstr ""

#: js.php:1694
msgid "Hiding the label can make it difficult for users to fill out your form. Please keep the label visible to improve the accessibility of your form."
msgstr ""

#: js.php:1695
msgid "Hiding the choice labels can make it difficult for users to fill out your form. Please keep the choice labels visible to improve the accessibility of your form."
msgstr ""

#: js.php:1696
msgid "The image button is not accessible for users who rely on a screen reader. Please use a text button to improve the accessibility of your form."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1700
msgid "To better comply with %1$sWCAG%2$s, we use the placeholder or description as a hidden label for screen readers."
msgstr ""

#: js.php:1716
msgid "This field has accessibility issues."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1753
msgid "An empty label violates %1$sWCAG%2$s. Please use descriptive text for your label. To hide the label, use the \"Field Label Visibility\" setting."
msgstr ""

#: js.php:1765
msgid "This field has errors."
msgstr ""

#: js.php:1783
msgid "The submit button can't be placed inline on multi-page forms."
msgstr ""

#: js.php:1784
msgid "If a valid image url is not present a text-only submit button will be used."
msgstr ""

#: notification.php:187
msgid "Warning! Using a third-party email in the From Email field may prevent your notification from being delivered. It is best to use an email with the same domain as your website. %sMore details in our documentation.%s"
msgstr ""

#: notification.php:220
msgid "Configure Routing"
msgstr ""

#: notification.php:241
msgid "Email Service"
msgstr ""

#: notification.php:249
#: notification.php:1312
msgid "Event"
msgstr ""

#: notification.php:258
msgid "Send To Email"
msgstr ""

#: notification.php:301
msgid "Send To Field"
msgstr ""

#: notification.php:305
msgid "Your form does not have an email field. Add an email field to your form and try again."
msgstr ""

#: notification.php:335
msgid "Please select an Email Address field."
msgstr ""

#: notification.php:355
#: tooltips.php:13
msgid "From Name"
msgstr ""

#: notification.php:362
msgid "From Email"
msgstr ""

#: notification.php:370
msgid "Please enter a valid email address or merge tag in the From Email field."
msgstr ""

#: notification.php:376
#: tooltips.php:14
msgid "Reply To"
msgstr ""

#: notification.php:382
msgid "Please enter a valid email address or merge tag in the Reply To field."
msgstr ""

#: notification.php:388
msgid "CC"
msgstr ""

#: notification.php:408
msgid "Please enter a valid email address or merge tag in the CC field."
msgstr ""

#: notification.php:414
msgid "BCC"
msgstr ""

#: notification.php:420
msgid "Please enter a valid email address or merge tag in the BCC field."
msgstr ""

#: notification.php:426
#: notification.php:1308
msgid "Subject"
msgstr ""

#: notification.php:440
msgid "Auto-formatting"
msgstr ""

#: notification.php:452
#: tooltips.php:17
msgid "Attachments"
msgstr ""

#: notification.php:458
msgid "Attach uploaded fields to notification"
msgstr ""

#: notification.php:478
msgid "Update Notification"
msgstr ""

#: notification.php:712
msgid "Save & Continue Link"
msgstr ""

#: notification.php:716
msgid "Save & Continue Token"
msgstr ""

#: notification.php:953
msgid "Notification deleted."
msgstr ""

#: notification.php:955
msgid "There was an issue deleting this notification."
msgstr ""

#: notification.php:961
msgid "Notification duplicated."
msgstr ""

#: notification.php:963
msgid "There was an issue duplicating this notification."
msgstr ""

#: notification.php:1009
msgid "Form is submitted"
msgstr ""

#: notification.php:1011
msgid "Form is saved"
msgstr ""

#: notification.php:1012
msgid "Save and continue email is requested"
msgstr ""

#: notification.php:1316
msgid "Service"
msgstr ""

#: notification.php:1541
msgid "WARNING: You are about to delete this notification."
msgstr ""

#: notification.php:1602
msgid "Undefined Service"
msgstr ""

#: notification.php:1633
msgid "This form doesn't have any notifications. Let's go %screate one%s."
msgstr ""

#: preview.php:22
msgid "You don't have adequate permission to preview forms."
msgstr ""

#. translators: Form preview page title. 1: form title, 2: site title.
#: preview.php:46
msgid "%1$s &lsaquo; Form Preview - Gravity Forms &lsaquo; %2$s &#8212; WordPress"
msgstr ""

#: preview.php:116
msgid "display grid"
msgstr ""

#: preview.php:117
msgid "show structure"
msgstr ""

#: preview.php:123
msgid "Form Preview"
msgstr ""

#: preview.php:127
msgid "Note: This is a simple form preview. This form may display differently when added to your page based on normal inheritance from parent theme styles."
msgstr ""

#: preview.php:127
msgid "dismiss"
msgstr ""

#: print-entry.php:27
msgid "You don't have adequate permission to view entries."
msgstr ""

#: print-entry.php:176
msgid "Form Id and Entry Id are required parameters."
msgstr ""

#: print-entry.php:185
msgid "Bulk Print"
msgstr ""

#. translators: Print preview page title. 1: entry title, 2: form title, 3: site title.
#: print-entry.php:188
msgid "%1$s &lsaquo; %2$s &lsaquo; Print Preview - Gravity Forms &lsaquo; %3$s &#8212; WordPress"
msgstr ""

#: print-entry.php:238
msgid "close window"
msgstr ""

#: print-entry.php:238
msgid "Print Preview"
msgstr ""

#: select_columns.php:48
msgid "Oops! We could not locate your form. Please try again."
msgstr ""

#: select_columns.php:211
msgid "Active Columns"
msgstr ""

#: select_columns.php:224
msgid "Inactive Columns"
msgstr ""

#: settings.php:173
msgid "You don't have adequate permission to uninstall Gravity Forms."
msgstr ""

#: settings.php:263
msgid "Gravity Forms has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:278
msgid "This operation deletes ALL Gravity Forms settings. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: settings.php:289
msgid "Warning! ALL Gravity Forms data, including form entries will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: settings.php:352
msgid "%s uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:431
msgid "A valid license key is required for access to automatic plugin upgrades and product support."
msgstr ""

#: settings.php:435
msgid "License key is managed by the administrator of this network"
msgstr ""

#: settings.php:440
msgid "Support License Key"
msgstr ""

#: settings.php:446
msgid "Paste Your License Key Here"
msgstr ""

#: settings.php:472
msgid "Your license key was not updated. "
msgstr ""

#: settings.php:509
msgid "Your License Details"
msgstr ""

#: settings.php:521
msgid "Default Currency"
msgstr ""

#: settings.php:526
msgid "Select the default currency for your forms. This is used for product fields, credit card fields and others."
msgstr ""

#: settings.php:541
msgid "Logging"
msgstr ""

#: settings.php:542
msgid "Enable if you would like logging within Gravity Forms. Logging allows you to easily debug the inner workings of Gravity Forms to solve any possible issues. "
msgstr ""

#: settings.php:548
msgid "Enable Logging"
msgstr ""

#: settings.php:565
msgid "Default Form Theme"
msgstr ""

#: settings.php:578
msgid "Orbital Theme (Recommended)"
msgstr ""

#: settings.php:585
msgid "This theme will be used by default everywhere forms are embedded on your site"
msgstr ""

#: settings.php:587
msgid "Learn more about form theme and style settings."
msgstr ""

#: settings.php:603
msgid "Enable to display the forms menu in the WordPress top toolbar. The forms menu will display the ten forms recently opened in the form editor."
msgstr ""

#: settings.php:609
msgid "Enable Toolbar Menu"
msgstr ""

#: settings.php:621
msgid "Dashboard Widget"
msgstr ""

#: settings.php:622
msgid "Turn on to enable the Gravity Forms dashboard widget. The dashboard widget displays a list of forms and the number of entries each form has."
msgstr ""

#: settings.php:628
msgid "Enable Dashboard Widget"
msgstr ""

#: settings.php:641
msgid "Automatic Background Updates"
msgstr ""

#: settings.php:642
msgid "Enable to allow Gravity Forms to download and install bug fixes and security updates automatically in the background. Requires a valid license key."
msgstr ""

#: settings.php:648
msgid "Enable Automatic Background Updates"
msgstr ""

#: settings.php:660
#: settings.php:667
msgid "No Conflict Mode"
msgstr ""

#: settings.php:661
msgid "Enable to prevent extraneous scripts and styles from being printed on a Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: settings.php:687
msgid "Enable Akismet Integration"
msgstr ""

#: settings.php:700
msgid "Data Collection"
msgstr ""

#: settings.php:712
msgid "Enable Data Collection"
msgstr ""

#: settings.php:734
msgid "Output Default CSS"
msgstr ""

#: settings.php:736
msgid "Enable this option to output the default form CSS. Disable it if you plan to create your own CSS in a child theme. Note: after Gravity Forms 2.8, this setting will no longer appear on the settings page. If you previously had it enabled, you will need to use the %sgform_disable_css%s filter to disable it."
msgstr ""

#: settings.php:746
msgid "Disable CSS"
msgstr ""

#: settings.php:795
msgid "Please enter a valid license key to see details."
msgstr ""

#: settings.php:810
msgid "License details are not available at this time."
msgstr ""

#: settings.php:814
msgid "Days Left"
msgstr ""

#: settings.php:821
#: settings.php:831
msgid "License Type"
msgstr ""

#: settings.php:822
#: settings.php:834
msgid "License Status"
msgstr ""

#: settings.php:823
#: settings.php:847
msgid "Purchase Date"
msgstr ""

#: settings.php:824
#: settings.php:850
msgid "License Activations"
msgstr ""

#: settings.php:1003
msgid "Settings: General"
msgstr ""

#: settings.php:1070
msgid "reCAPTCHA Settings"
msgstr ""

#: settings.php:1073
msgid "Gravity Forms integrates with reCAPTCHA, a free CAPTCHA service that uses an advanced risk analysis engine and adaptive challenges to keep automated software from engaging in abusive activities on your site. "
msgstr ""

#: settings.php:1074
msgid "Please note, only v2 keys are supported and checkbox keys are not compatible with invisible reCAPTCHA."
msgstr ""

#: settings.php:1075
msgid "These settings are required only if you decide to use the reCAPTCHA field."
msgstr ""

#: settings.php:1076
msgid "Get your reCAPTCHA Keys."
msgstr ""

#: settings.php:1083
msgid "Site Key"
msgstr ""

#: settings.php:1093
msgid "Secret Key"
msgstr ""

#: settings.php:1114
msgid "Invisible"
msgstr ""

#: settings.php:1121
msgid "Validate Keys"
msgstr ""

#: settings.php:1156
#: settings.php:1177
msgid "reCAPTCHA keys are invalid."
msgstr ""

#: settings.php:1227
msgid "Please complete the reCAPTCHA widget to validate your reCAPTCHA keys:"
msgstr ""

#: tests/playwright/mu-plugins/snippets/gform_settings_header_buttons.php:5
msgid "I'm a Button!"
msgstr ""

#: tests/unit-tests/save-form/test-form-crud-handler.php:152
#: tests/unit-tests/save-form/test-form-crud-handler.php:294
#: tests/unit-tests/save-form/test-form-crud-handler.php:622
#: tests/unit-tests/save-form/test-form-crud-handler.php:747
msgid "New submission from {form_title}"
msgstr ""

#: tooltips.php:9
msgid "Send To Email Address"
msgstr ""

#: tooltips.php:9
msgid "Enter the email address you would like the notification email sent to."
msgstr ""

#: tooltips.php:10
#: tooltips.php:37
msgid "Disable Auto-Formatting"
msgstr ""

#: tooltips.php:10
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create email notification content."
msgstr ""

#: tooltips.php:11
msgid "Routing"
msgstr ""

#: tooltips.php:11
msgid "Allows notification to be sent to different email addresses depending on values selected in the form."
msgstr ""

#: tooltips.php:12
msgid "From Email Address"
msgstr ""

#: tooltips.php:12
msgid "Enter an authorized email address you would like the notification email sent from. To avoid deliverability issues, always use your site domain in the from email."
msgstr ""

#: tooltips.php:13
msgid "Enter the name you would like the notification email sent from, or select the name from available name fields."
msgstr ""

#: tooltips.php:14
msgid "Enter the email address you would like to be used as the reply to address for the notification email."
msgstr ""

#: tooltips.php:15
msgid "Carbon Copy Addresses"
msgstr ""

#: tooltips.php:15
msgid "Enter a comma separated list of email addresses you would like to receive a CC of the notification email."
msgstr ""

#: tooltips.php:16
msgid "Blind Carbon Copy Addresses"
msgstr ""

#: tooltips.php:16
msgid "Enter a comma separated list of email addresses you would like to receive a BCC of the notification email."
msgstr ""

#: tooltips.php:17
msgid "When enabled, any files uploaded to File Upload fields will be attached to the notification email."
msgstr ""

#: tooltips.php:18
msgid "Limit Form Activity"
msgstr ""

#: tooltips.php:18
msgid "Limit the number of entries a form can generate and/or schedule a time period the form is active."
msgstr ""

#: tooltips.php:19
msgid "Limit Number of Entries"
msgstr ""

#: tooltips.php:19
msgid "Enter a number in the input box below to limit the number of entries allowed for this form. The form will become inactive when that number is reached."
msgstr ""

#: tooltips.php:20
msgid "Schedule a time period the form is active."
msgstr ""

#: tooltips.php:21
msgid "Enable Anti-spam honeypot"
msgstr ""

#: tooltips.php:21
msgid "Enables the honeypot spam protection technique, which is an alternative to the reCAPTCHA field."
msgstr ""

#: tooltips.php:22
msgid "Enable Animation"
msgstr ""

#: tooltips.php:22
msgid "Check this option to enable a sliding animation when displaying/hiding conditional logic fields."
msgstr ""

#: tooltips.php:23
msgid "Legacy Markup"
msgstr ""

#: tooltips.php:23
msgid "Check this option to enable Gravity Forms' legacy markup. This will hinder the accessibility of your form."
msgstr ""

#: tooltips.php:24
msgid "Enter the title of your form."
msgstr ""

#: tooltips.php:25
msgid "Enter a description for your form. This may be used for user instructions."
msgstr ""

#: tooltips.php:26
msgid "Form Label Placement"
msgstr ""

#: tooltips.php:26
msgid "Select the default label placement.  Labels can be top aligned above a field, left aligned to the left of a field, or right aligned to the right of a field. This is a global label placement setting."
msgstr ""

#: tooltips.php:27
msgid "Select the default description placement.  Descriptions can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:28
msgid "Select the default validation message placement.  Validation messages can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:29
msgid "Select the default sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:30
msgid "Required Indicator"
msgstr ""

#: tooltips.php:31
msgid "Form Button Text"
msgstr ""

#: tooltips.php:31
msgid "Enter the text you would like to appear on the form submit button."
msgstr ""

#: tooltips.php:32
msgid "Form Button Image"
msgstr ""

#: tooltips.php:32
msgid "Enter the path to an image you would like to use as the form submit button."
msgstr ""

#: tooltips.php:33
msgid "Form CSS Class Name"
msgstr ""

#: tooltips.php:33
msgid "Enter the CSS class name you would like to use in order to override the default styles for this form."
msgstr ""

#: tooltips.php:34
msgid "Enter the URL of a custom image to replace the default 'add item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:35
msgid "Enter the URL of a custom image to replace the default 'delete item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:36
msgid "Confirmation Message Text"
msgstr ""

#: tooltips.php:36
msgid "Enter the text you would like the user to see on the confirmation page of this form."
msgstr ""

#: tooltips.php:37
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create the confirmation content."
msgstr ""

#: tooltips.php:38
msgid "Redirect Form to Page"
msgstr ""

#: tooltips.php:38
msgid "Select the page you would like the user to be redirected to after they have submitted the form."
msgstr ""

#: tooltips.php:39
msgid "Redirect Form to URL"
msgstr ""

#: tooltips.php:39
msgid "Enter the URL of the webpage you would like the user to be redirected to after they have submitted the form."
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:41
msgid "Pass Data Via Query String"
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:41
msgid "To pass field data to the confirmation page, build a Query String using the 'Insert Merge Tag' drop down. %s..more info on querystrings &raquo;%s"
msgstr ""

#: tooltips.php:42
msgid "Enter the label of the form field.  This is the field title the user will see when filling out the form."
msgstr ""

#: tooltips.php:43
msgid "Enter the label for this HTML block. It will help you identify your HTML blocks in the form editor, but it will not be displayed on the form."
msgstr ""

#: tooltips.php:44
msgid "Disable Default Margins"
msgstr ""

#: tooltips.php:44
msgid "When enabled, margins are added to properly align the HTML content with other form fields."
msgstr ""

#: tooltips.php:45
msgid "reCAPTCHA Theme"
msgstr ""

#: tooltips.php:45
msgid "Select the visual theme for the reCAPTCHA field from the available options to better match your site design."
msgstr ""

#: tooltips.php:46
msgid "CAPTCHA Type"
msgstr ""

#: tooltips.php:46
msgid "Select the type of CAPTCHA you would like to use."
msgstr ""

#: tooltips.php:47
msgid "CAPTCHA Badge Position"
msgstr ""

#: tooltips.php:47
msgid "Select the position of the badge containing the links to Google's privacy policy and terms."
msgstr ""

#: tooltips.php:48
msgid "Select the custom field name from available existing custom fields, or enter a new custom field name."
msgstr ""

#: tooltips.php:49
msgid "Field type"
msgstr ""

#: tooltips.php:49
msgid "Select the type of field from the available form fields."
msgstr ""

#: tooltips.php:50
msgid "Enter the maximum number of characters that this field is allowed to have."
msgstr ""

#: tooltips.php:51
msgid "Enter the maximum number of rows that users are allowed to add."
msgstr ""

#: tooltips.php:52
msgid "Select the type of inputs you would like to use for the date field. Date Picker will let users select a date from a calendar. Date Field will let users free type the date."
msgstr ""

#: tooltips.php:53
msgid "Select the type of address you would like to use."
msgstr ""

#: tooltips.php:54
msgid "Default State"
msgstr ""

#: tooltips.php:54
msgid "Select the state you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:55
msgid "Default Province"
msgstr ""

#: tooltips.php:55
msgid "Select the province you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:56
msgid "Select the country you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:57
msgid "Hide Country"
msgstr ""

#: tooltips.php:57
msgid "For addresses that only apply to one country, you can choose to not display the country drop down. Entries will still be recorded with the selected country."
msgstr ""

#: tooltips.php:58
msgid "Hide Address Line 2"
msgstr ""

#: tooltips.php:58
msgid "Check this box to prevent the extra address input (Address Line 2) from being displayed in the form."
msgstr ""

#: tooltips.php:59
msgid "Hide State Field"
msgstr ""

#: tooltips.php:59
msgid "Check this box to prevent the State field from being displayed in the form."
msgstr ""

#: tooltips.php:60
msgid "Hide Province Field"
msgstr ""

#: tooltips.php:60
msgid "Check this box to prevent Province field from being displayed in the form."
msgstr ""

#: tooltips.php:61
msgid "Hide State/Province/Region"
msgstr ""

#: tooltips.php:61
msgid "Check this box to prevent the State/Province/Region from being displayed in the form."
msgstr ""

#: tooltips.php:62
msgid "Field Name Format"
msgstr ""

#: tooltips.php:62
msgid "Select the format you would like to use for the Name field.  There are 3 options, Normal which includes First and Last Name, Extended which adds Prefix and Suffix, or Simple which is a single input field."
msgstr ""

#: tooltips.php:63
msgid "Select the format of numbers that are allowed in this field. You have the option to use a comma or a dot as the decimal separator."
msgstr ""

#: tooltips.php:64
msgid "Check this box to prevent this field from being displayed in a non-secure page (i.e. not https://). It will redirect the page to the same URL, but starting with https:// instead. This option requires a properly configured SSL certificate."
msgstr ""

#: tooltips.php:65
msgid "Field Date Format"
msgstr ""

#: tooltips.php:65
msgid "Select the format you would like to use for the date input."
msgstr ""

#: tooltips.php:66
msgid "Select the format you would like to use for the time field.  Available options are 12 hour (i.e. 8:30 pm) and 24 hour (i.e. 20:30)."
msgstr ""

#: tooltips.php:67
msgid "Allowed File Extensions"
msgstr ""

#: tooltips.php:67
msgid "Enter the allowed file extensions for file uploads.  This will limit the type of files a user may upload."
msgstr ""

#: tooltips.php:68
msgid "Select this option to enable multiple files to be uploaded for this field."
msgstr ""

#: tooltips.php:69
msgid "Specify the maximum number of files that can be uploaded using this field. Leave blank for unlimited. Note that the actual number of files permitted may be limited by this server's specifications and configuration."
msgstr ""

#: tooltips.php:70
msgid "Specify the maximum file size in megabytes allowed for each of the files."
msgstr ""

#: tooltips.php:71
msgid "Phone Number Format"
msgstr ""

#: tooltips.php:71
msgid "Select the format you would like to use for the phone input.  Available options are domestic US/CANADA style phone number and international long format phone number."
msgstr ""

#: tooltips.php:72
msgid "Field Description"
msgstr ""

#: tooltips.php:72
msgid "Enter the description for the form field.  This will be displayed to the user and provide some direction on how the field should be filled out or selected."
msgstr ""

#: tooltips.php:73
msgid "Required Field"
msgstr ""

#: tooltips.php:73
msgid "Select this option to make the form field required.  A required field will prevent the form from being submitted if it is not filled out or selected."
msgstr ""

#: tooltips.php:74
msgid "Select this option to limit user input to unique values only.  This will require that a value entered in a field does not currently exist in the entry database for that field."
msgstr ""

#: tooltips.php:75
msgid "Hide Field Label"
msgstr ""

#: tooltips.php:75
msgid "Select this option to hide the field label in the form."
msgstr ""

#: tooltips.php:76
msgid "Number Range"
msgstr ""

#: tooltips.php:76
msgid "Enter the minimum and maximum values for this form field.  This will require that the value entered by the user must fall within this range."
msgstr ""

#: tooltips.php:77
msgid "Enabling calculations will allow the value of this field to be dynamically calculated based on a mathematical formula."
msgstr ""

#: tooltips.php:78
msgid "Specify a mathematical formula. The result of this formula will be dynamically populated as the value for this field."
msgstr ""

#: tooltips.php:79
msgid "Specify how many decimal places the number should be rounded to."
msgstr ""

#: tooltips.php:80
msgid "Admin Label"
msgstr ""

#: tooltips.php:80
msgid "Enter the admin label of the form field.  Entering a value in this field will override the Field Label when displayed in the Gravity Forms administration tool."
msgstr ""

#: tooltips.php:81
msgid "Enter values in this setting to override the Sub-Label for each field."
msgstr ""

#: tooltips.php:82
msgid "Label Visibility"
msgstr ""

#: tooltips.php:82
msgid "Select the label visibility for this field.  Labels can either inherit the form setting or be hidden."
msgstr ""

#: tooltips.php:83
msgid "Select the description placement.  Descriptions can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:84
msgid "Select the sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:85
msgid "Select a form field size from the available options. This will set the width of the field. Please note: if using a paragraph field, the size applies only to the height of the field."
msgstr ""

#: tooltips.php:86
msgid "Select the fields you'd like to use in this Name field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:87
msgid "Name Prefix Choices"
msgstr ""

#: tooltips.php:87
msgid "Add Choices to this field. You can mark a choice as selected by default by using the radio buttons on the left."
msgstr ""

#: tooltips.php:88
msgid "Select the fields you'd like to use in this Address Field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:89
#: tooltips.php:90
msgid "If you would like to pre-populate the value of a field, enter it here."
msgstr ""

#: tooltips.php:91
msgid "The Placeholder will not be submitted along with the form. Use the Placeholder to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:92
msgid "Placeholders will not be submitted along with the form. Use Placeholders to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:93
msgid "Use Values Submitted in a Different Field"
msgstr ""

#: tooltips.php:93
msgid "Activate this option to allow users to skip this field and submit the values entered in the associated field. For example, this is useful for shipping and billing address fields."
msgstr ""

#: tooltips.php:94
msgid "Enter the label to be displayed next to the check box. For example, &quot;same as shipping address&quot;."
msgstr ""

#: tooltips.php:95
msgid "Select the field to be used as the source for the values for this field."
msgstr ""

#: tooltips.php:96
msgid "Activated by Default"
msgstr ""

#: tooltips.php:96
msgid "Select this setting to display the option as activated by default when the form first loads."
msgstr ""

#: tooltips.php:97
msgid "Autocomplete Attribute"
msgstr ""

#: tooltips.php:97
msgid "Select this setting to let browsers help a user fill in a field with autocomplete.  You can enter a single autocomplete attribute or multiple attributes separated with a space.  Only use valid attributes: invalid attributes will not be saved.  Learn more about autocomplete in the %s accessibility documentation %s."
msgstr ""

#: tooltips.php:98
msgid "Validation Message"
msgstr ""

#: tooltips.php:98
msgid "If you would like to override the default error validation for a field, enter it here.  This message will be displayed if there is an error with this field when the user submits the form."
msgstr ""

#: tooltips.php:99
msgid "reCAPTCHA Language"
msgstr ""

#: tooltips.php:99
msgid "Select the language you would like to use for the reCAPTCHA display from the available options."
msgstr ""

#: tooltips.php:100
msgid "Enter the CSS class name you would like to use in order to override the default styles for this field."
msgstr ""

#: tooltips.php:102
msgid "Field Choices"
msgstr ""

#: tooltips.php:102
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) using a radio or checkbox located to the left of the choice."
msgstr ""

#: tooltips.php:103
msgid "Enable Choice Values"
msgstr ""

#: tooltips.php:103
msgid "Check this option to specify a value for each choice. Choice values are not displayed to the user viewing the form, but are accessible to administrators when viewing the entry."
msgstr ""

#: tooltips.php:104
msgid "Create rules to dynamically display or hide this field based on values from another field."
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:106
msgid "Enable Enhanced UI"
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:106
msgid "By selecting this option, the %s jQuery script will be applied to this field, enabling search capabilities to Drop Down fields and a more user-friendly interface for Multi Select fields."
msgstr ""

#: tooltips.php:107
msgid "Checkbox Text"
msgstr ""

#: tooltips.php:107
msgid "Text of the consent checkbox."
msgstr ""

#: tooltips.php:108
msgid "\"Select All\" Choice"
msgstr ""

#: tooltips.php:108
msgid "Check this option to add a \"Select All\" checkbox before the checkbox choices to allow users to check all the checkboxes with one click."
msgstr ""

#: tooltips.php:109
msgid "\"Other\" Choice"
msgstr ""

#: tooltips.php:109
msgid "Check this option to add a text input as the final choice of your radio button field. This allows the user to specify a value that is not a predefined choice."
msgstr ""

#: tooltips.php:110
msgid "Multiple Selection"
msgstr ""

#: tooltips.php:110
msgid "Enable this option to allow the user to select multiple choices. Disabling this option after entries with multiple choices have been submitted will hide the values for those entries."
msgstr ""

#: tooltips.php:111
msgid "Check this option to require a user to be logged in to view this form."
msgstr ""

#: tooltips.php:112
msgid "Enter a message to be displayed to users who are not logged in (shortcodes and HTML are supported)."
msgstr ""

#: tooltips.php:113
msgid "Page Conditional Logic"
msgstr ""

#: tooltips.php:113
msgid "Create rules to dynamically display or hide this page based on values from another field."
msgstr ""

#: tooltips.php:114
msgid "Select which type of visual progress indicator you would like to display.  Progress Bar, Steps or None."
msgstr ""

#: tooltips.php:115
msgid "Select which progress bar style you would like to use.  Select custom to choose your own text and background color."
msgstr ""

#: tooltips.php:116
msgid "Name each of the pages on your form.  Page names are displayed with the selected progress indicator."
msgstr ""

#: tooltips.php:117
msgid "Next Button Text"
msgstr ""

#: tooltips.php:117
msgid "Enter the text you would like to appear on the page next button."
msgstr ""

#: tooltips.php:118
msgid "Next Button Image"
msgstr ""

#: tooltips.php:118
msgid "Enter the path to an image you would like to use as the page next button."
msgstr ""

#: tooltips.php:119
msgid "Previous Button Text"
msgstr ""

#: tooltips.php:119
msgid "Enter the text you would like to appear on the page previous button."
msgstr ""

#: tooltips.php:120
msgid "Previous Button Image"
msgstr ""

#: tooltips.php:120
msgid "Enter the path to an image you would like to use as the page previous button."
msgstr ""

#: tooltips.php:121
msgid "Next Button Conditional Logic"
msgstr ""

#: tooltips.php:121
msgid "Create rules to dynamically display or hide the page's Next Button based on values from another field."
msgstr ""

#: tooltips.php:122
msgid "Create rules to dynamically display or hide the submit button based on values from another field."
msgstr ""

#: tooltips.php:123
msgid "Select which categories are displayed. You can choose to display all of them or select individual ones."
msgstr ""

#: tooltips.php:124
msgid "Select the post status that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:125
msgid "Post Author"
msgstr ""

#: tooltips.php:125
msgid "Select the author that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:126
msgid "Select the post format that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:127
msgid "Post Content Template"
msgstr ""

#: tooltips.php:127
msgid "Check this option to format and insert merge tags into the Post Content."
msgstr ""

#: tooltips.php:128
msgid "Post Title Template"
msgstr ""

#: tooltips.php:128
msgid "Check this option to format and insert merge tags into the Post Title."
msgstr ""

#: tooltips.php:129
msgid "Select the category that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:130
msgid "Use Current User as Author"
msgstr ""

#: tooltips.php:130
msgid "Selecting this option will set the post author to the WordPress user that submitted the form."
msgstr ""

#: tooltips.php:131
msgid "Image Meta"
msgstr ""

#: tooltips.php:131
msgid "Select one or more image metadata field to be displayed along with the image upload field. They enable users to enter additional information about the uploaded image."
msgstr ""

#: tooltips.php:132
msgid "Check this option to set this image as the post's Featured Image."
msgstr ""

#: tooltips.php:133
msgid "Incoming Field Data"
msgstr ""

#: tooltips.php:133
msgid "Check this option to enable data to be passed to the form and pre-populate this field dynamically. Data can be passed via Query Strings, Shortcode and/or Hooks."
msgstr ""

#: tooltips.php:134
msgid "Enter the content (Text or HTML) to be displayed on the form."
msgstr ""

#: tooltips.php:135
msgid "Base Price"
msgstr ""

#: tooltips.php:135
msgid "Enter the base price for this product."
msgstr ""

#: tooltips.php:136
msgid "Disable Quantity"
msgstr ""

#: tooltips.php:136
msgid "Disables the quantity field.  A quantity of 1 will be assumed or you can add a Quantity field to your form from the Pricing Fields."
msgstr ""

#: tooltips.php:137
msgid "Product Field"
msgstr ""

#: tooltips.php:137
msgid "Select which Product this field is tied to."
msgstr ""

#: tooltips.php:138
msgid "Input masks provide a visual guide allowing users to more easily enter data in a specific format such as dates and phone numbers."
msgstr ""

#: tooltips.php:139
msgid "Standard Fields provide basic form functionality."
msgstr ""

#: tooltips.php:140
msgid "Advanced Fields are for specific uses.  They enable advanced formatting of regularly used fields such as Name, Email, Address, etc."
msgstr ""

#: tooltips.php:141
msgid "Post Fields allow you to add fields to your form that create Post Drafts in WordPress from the submitted data."
msgstr ""

#: tooltips.php:142
msgid "Pricing fields allow you to add fields to your form that calculate pricing for selling goods and services."
msgstr ""

#: tooltips.php:143
msgid "Export Selected Form"
msgstr ""

#: tooltips.php:143
msgid "Select the form you would like to export entry data from. You may only export data from one form at a time."
msgstr ""

#: tooltips.php:144
msgid "Export Selected Forms"
msgstr ""

#: tooltips.php:144
msgid "Select the forms you would like to export."
msgstr ""

#: tooltips.php:145
msgid "Filter the entries by adding conditions."
msgstr ""

#: tooltips.php:146
msgid "Export Selected Fields"
msgstr ""

#: tooltips.php:146
msgid "Select the fields you would like to include in the export."
msgstr ""

#: tooltips.php:147
msgid "Export Date Range"
msgstr ""

#: tooltips.php:147
msgid "Select a date range. Setting a range will limit the export to entries submitted during that date range. If no range is set, all entries will be exported."
msgstr ""

#: tooltips.php:148
msgid "Click the file selection button to upload a Gravity Forms export file from your computer. Please make sure your file has the .json extension, and that it was generated by the Gravity Forms Export tool."
msgstr ""

#: tooltips.php:149
msgid "If a form uses the Image Choice field, or has Conversational Mode enabled, the importer will import the images and save them in your media library."
msgstr ""

#: tooltips.php:150
msgid "Settings License Key"
msgstr ""

#: tooltips.php:150
msgid "Your Gravity Forms support license key is used to verify your support package, enable automatic updates and receive support."
msgstr ""

#: tooltips.php:151
msgid "Select yes or no to enable or disable CSS output.  Setting this to no will disable the standard Gravity Forms CSS from being included in your theme."
msgstr ""

#: tooltips.php:152
msgid "Output HTML5"
msgstr ""

#: tooltips.php:152
msgid "Select yes or no to enable or disable HTML5 output. Setting this to no will disable the standard Gravity Forms HTML5 form field output."
msgstr ""

#: tooltips.php:153
msgid "Select On or Off to enable or disable no-conflict mode. Setting this to On will prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: tooltips.php:154
msgid "reCAPTCHA Site Key"
msgstr ""

#: tooltips.php:154
msgid "Enter your reCAPTCHA Site Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:155
msgid "reCAPTCHA Secret Key"
msgstr ""

#: tooltips.php:155
msgid "Enter your reCAPTCHA Secret Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:156
msgid "reCAPTCHA Type"
msgstr ""

#: tooltips.php:156
msgid "Select the type of reCAPTCHA you would like to use."
msgstr ""

#: tooltips.php:157
msgid "Please select the currency for your location.  Currency is used for pricing fields and price calculations."
msgstr ""

#: tooltips.php:159
msgid "Entries Conversion"
msgstr ""

#: tooltips.php:159
msgid "Conversion is the percentage of form views that generated an entry. If a form was viewed twice, and one entry was generated, the conversion will be 50%."
msgstr ""

#: tooltips.php:160
msgid "Tab Index Start Value"
msgstr ""

#: tooltips.php:160
#: widget.php:172
msgid "If you have other forms on the page (i.e. Comments Form), specify a higher tabindex start value so that your Gravity Form does not end up with the same tabindices as your other forms. To disable the tabindex, enter 0 (zero)."
msgstr ""

#: tooltips.php:161
msgid "Override Notifications"
msgstr ""

#: tooltips.php:161
msgid "Enter a comma separated list of email addresses you would like to receive the selected notification emails."
msgstr ""

#: tooltips.php:162
msgid "Progress Bar Confirmation Display"
msgstr ""

#: tooltips.php:162
msgid "Check this box if you would like the progress bar to display with the confirmation text."
msgstr ""

#: tooltips.php:163
msgid "Progress Bar Completion Text"
msgstr ""

#: tooltips.php:163
msgid "Enter text to display at the top of the progress bar."
msgstr ""

#: tooltips.php:164
msgid "Use Rich Text Editor"
msgstr ""

#: tooltips.php:164
msgid "Check this box if you would like to use the rich text editor for this field."
msgstr ""

#: tooltips.php:165
msgid "Enable Personal Data Tools"
msgstr ""

#: tooltips.php:165
msgid "Check this box if you would like to include data from this form when exporting or erasing personal data on this site."
msgstr ""

#: tooltips.php:166
msgid "Identification"
msgstr ""

#: tooltips.php:166
msgid "Select the field which will be used to identify the owner of the personal data."
msgstr ""

#: tooltips.php:167
msgid "Select the fields which will be included when exporting or erasing personal data."
msgstr ""

#: tooltips.php:168
msgid "Check this box if you would like to prevent the IP address from being stored during form submission."
msgstr ""

#: tooltips.php:169
msgid "Use these settings to keep entries only as long as they are needed. Trash or delete entries automatically older than the specified number of days. The minimum number of days allowed is one. This is to ensure that all entry processing is complete before deleting/trashing. The number of days setting is a minimum, not an exact period of time. The trashing/deleting occurs during the daily cron task so some entries may appear to remain up to a day longer than expected."
msgstr ""

#: tooltips.php:170
msgid "Password Visibility Toggle"
msgstr ""

#: tooltips.php:170
msgid "Check this box to add a toggle allowing the user to see the password they are entering in."
msgstr ""

#: tooltips.php:171
msgid "Enable to show a summary that lists validation errors on top of the form."
msgstr ""

#: widget.php:34
msgid "Gravity Forms Widget"
msgstr ""

#: widget.php:135
msgid "Contact Us"
msgstr ""

#: widget.php:169
msgid "Disable script output"
msgstr ""

#: widget.php:170
msgid "Tab Index Start"
msgstr ""

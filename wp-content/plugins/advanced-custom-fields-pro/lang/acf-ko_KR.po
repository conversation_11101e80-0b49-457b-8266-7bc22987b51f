# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-12T11:23:47+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-date_picker.php:235
#: includes/fields/class-acf-field-date_time_picker.php:222
msgid "Use the current date as the default value for this field."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:234
#: includes/fields/class-acf-field-date_time_picker.php:221
msgid "Default to the current date"
msgstr ""

#: includes/assets.php:363
msgid "Toggle panel"
msgstr ""

#. translators: %1$s - Plugin name, %2$s URL to documentation
#: includes/admin/admin.php:302
msgid ""
"%1$s We have detected that this website is configured to use v3 of the "
"Select2 jQuery library, which has been deprecated in favor of v4 and will be "
"removed in a future version of ACF. <a href=\"%2$s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr "생성한 옵션을 필드 정의의 '선택 사항' 설정에 다시 저장합니다."

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr "옵션 저장"

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""
"콘텐츠 편집자가 선택 입력을 입력하여 새 옵션을 생성할 수 있도록 허용합니다. "
"쉼표로 구분된 문자열로 여러 옵션을 만들 수 있습니다."

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr "옵션 만들기"

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr "ACF 필드 그룹 편집"

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr "모든 WP 엔진 요금제에서 4개월 무료 이용"

#: src/Site_Health/Site_Health.php:528
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr "블록 및 기타 위치가 있는 필드 그룹 수"

#: src/Site_Health/Site_Health.php:523
msgid "Number of Field Groups with Multiple Block Locations"
msgstr "여러 블록 위치가 있는 필드 그룹 수"

#: src/Site_Health/Site_Health.php:518
msgid "Number of Field Groups with a Single Block Location"
msgstr "단일 블록 위치가 있는 필드 그룹 수"

#: src/Site_Health/Site_Health.php:487
msgid "All Location Rules"
msgstr "모든 위치 규칙"

#: includes/validation.php:145
msgid "Learn more"
msgstr "더 알아보기"

#: includes/validation.php:134
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr "제공된 논스가 검증에 실패하여 ACF가 유효성 검사를 수행할 수 없습니다."

#: includes/validation.php:132
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"서버에서 논스를 수신하지 못했기 때문에 ACF가 유효성 검사를 수행할 수 없습니"
"다."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:348
msgid "are developed and maintained by"
msgstr "에 의해 개발 및 유지 관리됩니다."

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "출처 업데이트"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "기본적으로 관리자 사용자만 이 설정을 편집할 수 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "기본적으로 슈퍼 관리자 사용자만 이 설정을 편집할 수 있습니다."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "필드 닫기 및 추가"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"분류체계에서 메타박스의 콘텐츠를 처리하기 위해 호출할 PHP 함수 이름입니다. 보"
"안을 위해 이 콜백은 $_POST 또는 $_GET과 같은 슈퍼글로브에 액세스하지 않고 특"
"수한 컨텍스트에서 실행됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"편집 화면의 메타박스를 설정할 때 호출할 PHP 함수 이름입니다. 보안을 위해 이 "
"콜백은 $_POST 또는 $_GET과 같은 슈퍼글로브에 액세스하지 않고 특수한 컨텍스트"
"에서 실행됩니다."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:364
msgid "Allow Access to Value in Editor UI"
msgstr "에디터 UI에서 값에 대한 액세스 허용"

#: includes/fields/class-acf-field.php:346
msgid "Learn more."
msgstr "더 알아보기."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:345
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"콘텐츠 편집자가 블록 바인딩 또는 ACF 쇼트코드를 사용하여 편집기 UI에서 필드 "
"값에 액세스하고 표시할 수 있도록 허용합니다. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"요청된 ACF 필드 유형이 블록 바인딩 또는 ACF 쇼트코드의 출력을 지원하지 않습니"
"다."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr "요청된 ACF 필드는 바인딩 또는 ACF 쇼트코드로 출력할 수 없습니다."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"요청된 ACF 필드 유형이 바인딩 또는 ACF 쇼트코드로의 출력을 지원하지 않습니다."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[ACF 쇼트코드는 비공개 게시물의 필드를 표시할 수 없음]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[이 사이트에서는 ACF 쇼트코드가 비활성화되었습니다.]"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Businessman Icon"
msgstr "비즈니스맨 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Forums Icon"
msgstr "포럼 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:754
msgid "YouTube Icon"
msgstr "YouTube 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:753
msgid "Yes (alt) Icon"
msgstr "예 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:751
msgid "Xing Icon"
msgstr "싱 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:750
msgid "WordPress (alt) Icon"
msgstr "워드프레스(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "WhatsApp Icon"
msgstr "WhatsApp 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "Write Blog Icon"
msgstr "블로그 글쓰기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Widgets Menus Icon"
msgstr "위젯 메뉴 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "View Site Icon"
msgstr "사이트 보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Learn More Icon"
msgstr "더 알아보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "Add Page Icon"
msgstr "페이지 추가 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Video (alt3) Icon"
msgstr "비디오 (대체3) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "Video (alt2) Icon"
msgstr "비디오 (대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Video (alt) Icon"
msgstr "동영상 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Update (alt) Icon"
msgstr "업데이트 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Universal Access (alt) Icon"
msgstr "유니버설 액세스(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Twitter (alt) Icon"
msgstr "트위터(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Twitch Icon"
msgstr "Twitch 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Tide Icon"
msgstr "조수 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Tickets (alt) Icon"
msgstr "티켓(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Text Page Icon"
msgstr "텍스트 페이지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Table Row Delete Icon"
msgstr "표 행 삭제 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Table Row Before Icon"
msgstr "표 행 이전 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Table Row After Icon"
msgstr "표 행 후 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Table Col Delete Icon"
msgstr "표 열 삭제 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Table Col Before Icon"
msgstr "표 열 이전 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Table Col After Icon"
msgstr "표 열 뒤 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Superhero (alt) Icon"
msgstr "슈퍼히어로(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Superhero Icon"
msgstr "슈퍼히어로 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Spotify Icon"
msgstr "Spotify 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Shortcode Icon"
msgstr "쇼트코드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Shield (alt) Icon"
msgstr "방패(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Share (alt2) Icon"
msgstr "공유(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Share (alt) Icon"
msgstr "공유(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Saved Icon"
msgstr "저장 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "RSS Icon"
msgstr "RSS 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "REST API Icon"
msgstr "REST API 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Remove Icon"
msgstr "아이콘 제거"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Reddit Icon"
msgstr "Reddit 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Privacy Icon"
msgstr "개인 정보 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Printer Icon"
msgstr "프린터 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Podio Icon"
msgstr "Podio 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Plus (alt2) Icon"
msgstr "더하기(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Plus (alt) Icon"
msgstr "더하기(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Plugins Checked Icon"
msgstr "플러그인 확인 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Pinterest Icon"
msgstr "Pinterest 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Pets Icon"
msgstr "애완동물 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "PDF Icon"
msgstr "PDF 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Palm Tree Icon"
msgstr "야자수 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Open Folder Icon"
msgstr "폴더 열기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "No (alt) Icon"
msgstr "아니요(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Money (alt) Icon"
msgstr "돈 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Menu (alt3) Icon"
msgstr "메뉴(대체3) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Menu (alt2) Icon"
msgstr "메뉴(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Menu (alt) Icon"
msgstr "메뉴(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Spreadsheet Icon"
msgstr "스프레드시트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Interactive Icon"
msgstr "대화형 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Document Icon"
msgstr "문서 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Default Icon"
msgstr "기본 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Location (alt) Icon"
msgstr "위치(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "LinkedIn Icon"
msgstr "LinkedIn 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Instagram Icon"
msgstr "인스타그램 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Insert Before Icon"
msgstr "이전 삽입 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Insert After Icon"
msgstr "뒤에 삽입 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Insert Icon"
msgstr "아이콘 삽입"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Info Outline Icon"
msgstr "정보 개요 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Images (alt2) Icon"
msgstr "이미지(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Images (alt) Icon"
msgstr "이미지(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Rotate Right Icon"
msgstr "오른쪽 회전 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Rotate Left Icon"
msgstr "왼쪽 회전 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Rotate Icon"
msgstr "회전 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Flip Vertical Icon"
msgstr "세로로 뒤집기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Flip Horizontal Icon"
msgstr "가로로 뒤집기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Crop Icon"
msgstr "자르기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "ID (alt) Icon"
msgstr "ID (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "HTML Icon"
msgstr "HTML 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Hourglass Icon"
msgstr "모래시계 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Heading Icon"
msgstr "제목 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Google Icon"
msgstr "Google 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Games Icon"
msgstr "게임 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Fullscreen Exit (alt) Icon"
msgstr "전체 화면 종료(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Fullscreen (alt) Icon"
msgstr "전체 화면 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Status Icon"
msgstr "상태 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Image Icon"
msgstr "이미지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Gallery Icon"
msgstr "갤러리 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Chat Icon"
msgstr "채팅 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:585
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Audio Icon"
msgstr "오디오 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Aside Icon"
msgstr "옆으로 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Food Icon"
msgstr "음식 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Exit Icon"
msgstr "종료 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Excerpt View Icon"
msgstr "요약글 보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Embed Video Icon"
msgstr "동영상 퍼가기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Embed Post Icon"
msgstr "글 임베드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Embed Photo Icon"
msgstr "사진 임베드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Embed Generic Icon"
msgstr "일반 임베드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Embed Audio Icon"
msgstr "오디오 삽입 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Email (alt2) Icon"
msgstr "이메일(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Ellipsis Icon"
msgstr "줄임표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Unordered List Icon"
msgstr "정렬되지 않은 목록 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "RTL Icon"
msgstr "RTL 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Ordered List RTL Icon"
msgstr "정렬된 목록 RTL 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Ordered List Icon"
msgstr "주문 목록 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "LTR Icon"
msgstr "LTR 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Custom Character Icon"
msgstr "사용자 지정 캐릭터 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Edit Page Icon"
msgstr "페이지 편집 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Edit Large Icon"
msgstr "큰 편집 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Drumstick Icon"
msgstr "드럼 스틱 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Database View Icon"
msgstr "데이터베이스 보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Database Remove Icon"
msgstr "데이터베이스 제거 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Database Import Icon"
msgstr "데이터베이스 가져오기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Database Export Icon"
msgstr "데이터베이스 내보내기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Database Add Icon"
msgstr "데이터베이스 추가 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Database Icon"
msgstr "데이터베이스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Cover Image Icon"
msgstr "표지 이미지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Volume On Icon"
msgstr "볼륨 켜기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Volume Off Icon"
msgstr "볼륨 끄기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Skip Forward Icon"
msgstr "앞으로 건너뛰기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Skip Back Icon"
msgstr "뒤로 건너뛰기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Repeat Icon"
msgstr "반복 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Play Icon"
msgstr "재생 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Pause Icon"
msgstr "일시 중지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Forward Icon"
msgstr "앞으로 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Back Icon"
msgstr "뒤로 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Columns Icon"
msgstr "열 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Color Picker Icon"
msgstr "색상 선택기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Coffee Icon"
msgstr "커피 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Code Standards Icon"
msgstr "코드 표준 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Cloud Upload Icon"
msgstr "클라우드 업로드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Cloud Saved Icon"
msgstr "클라우드 저장 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Car Icon"
msgstr "자동차 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Camera (alt) Icon"
msgstr "카메라(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Calculator Icon"
msgstr "계산기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Button Icon"
msgstr "버튼 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Businessperson Icon"
msgstr "사업가 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Tracking Icon"
msgstr "추적 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Topics Icon"
msgstr "토픽 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Replies Icon"
msgstr "답글 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "PM Icon"
msgstr "PM 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Friends Icon"
msgstr "친구 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Community Icon"
msgstr "커뮤니티 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "BuddyPress Icon"
msgstr "버디프레스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "bbPress Icon"
msgstr "비비프레스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Activity Icon"
msgstr "활동 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Book (alt) Icon"
msgstr "책 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Block Default Icon"
msgstr "블록 기본 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Bell Icon"
msgstr "벨 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Beer Icon"
msgstr "맥주 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Bank Icon"
msgstr "은행 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Arrow Up (alt2) Icon"
msgstr "화살표 위(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Arrow Up (alt) Icon"
msgstr "위쪽 화살표(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Arrow Right (alt2) Icon"
msgstr "오른쪽 화살표(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Arrow Right (alt) Icon"
msgstr "오른쪽 화살표(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Arrow Left (alt2) Icon"
msgstr "왼쪽 화살표(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Left (alt) Icon"
msgstr "왼쪽 화살표(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Down (alt2) Icon"
msgstr "아래쪽 화살표(대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Down (alt) Icon"
msgstr "아래쪽 화살표(대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Amazon Icon"
msgstr "아마존 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Align Wide Icon"
msgstr "와이드 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Align Pull Right Icon"
msgstr "오른쪽으로 당겨 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Align Pull Left Icon"
msgstr "왼쪽으로 당겨 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Align Full Width Icon"
msgstr "전체 너비 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Airplane Icon"
msgstr "비행기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Site (alt3) Icon"
msgstr "사이트 (대체3) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Site (alt2) Icon"
msgstr "사이트 (대체2) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Site (alt) Icon"
msgstr "사이트 (대체) 아이콘"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr "몇 번의 클릭만으로 옵션 페이지를 만들려면 ACF PRO로 업그레이드하세요."

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "잘못된 요청 인수입니다."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "죄송합니다. 해당 권한이 없습니다."

#: src/Site_Health/Site_Health.php:726
msgid "Blocks Using Post Meta"
msgstr "포스트 메타를 사용한 블록"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO 로고"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO 로고"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:820
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"유형이 미디어_라이브러리로 설정된 경우 %s에 유효한 첨부 파일 ID가 필요합니다."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:804
msgid "%s is a required property of acf."
msgstr "%s는 acf의 필수 속성입니다."

#: includes/fields/class-acf-field-icon_picker.php:780
msgid "The value of icon to save."
msgstr "저장할 아이콘의 값입니다."

#: includes/fields/class-acf-field-icon_picker.php:774
msgid "The type of icon to save."
msgstr "저장할 아이콘 유형입니다."

#: includes/fields/class-acf-field-icon_picker.php:752
msgid "Yes Icon"
msgstr "예 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:749
msgid "WordPress Icon"
msgstr "워드프레스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "Warning Icon"
msgstr "경고 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Visibility Icon"
msgstr "가시성 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Vault Icon"
msgstr "볼트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Upload Icon"
msgstr "업로드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Update Icon"
msgstr "업데이트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Unlock Icon"
msgstr "잠금 해제 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Universal Access Icon"
msgstr "유니버설 액세스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Undo Icon"
msgstr "실행 취소 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Twitter Icon"
msgstr "트위터 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Trash Icon"
msgstr "휴지통 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Translation Icon"
msgstr "번역 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Tickets Icon"
msgstr "티켓 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Thumbs Up Icon"
msgstr "엄지척 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Thumbs Down Icon"
msgstr "엄지 아래로 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:640
#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Text Icon"
msgstr "텍스트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Testimonial Icon"
msgstr "추천 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tagcloud Icon"
msgstr "태그클라우드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tag Icon"
msgstr "태그 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Tablet Icon"
msgstr "태블릿 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Store Icon"
msgstr "스토어 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Sticky Icon"
msgstr "스티커 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Star Half Icon"
msgstr "별 반쪽 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Star Filled Icon"
msgstr "별이 채워진 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Star Empty Icon"
msgstr "별 비어 있음 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Sos Icon"
msgstr "구조 요청 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Sort Icon"
msgstr "정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Smiley Icon"
msgstr "스마일 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Smartphone Icon"
msgstr "스마트폰 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Slides Icon"
msgstr "슬라이드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Shield Icon"
msgstr "방패 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Share Icon"
msgstr "공유 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Search Icon"
msgstr "검색 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Screen Options Icon"
msgstr "화면 옵션 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Schedule Icon"
msgstr "일정 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Redo Icon"
msgstr "다시 실행 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Randomize Icon"
msgstr "무작위 추출 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Products Icon"
msgstr "제품 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Pressthis Icon"
msgstr "Pressthis 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Post Status Icon"
msgstr "글 상태 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Portfolio Icon"
msgstr "포트폴리오 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Plus Icon"
msgstr "플러스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Playlist Video Icon"
msgstr "재생목록 비디오 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Playlist Audio Icon"
msgstr "재생목록 오디오 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Phone Icon"
msgstr "전화 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Performance Icon"
msgstr "성능 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Paperclip Icon"
msgstr "종이 클립 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "No Icon"
msgstr "없음 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Networking Icon"
msgstr "네트워킹 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Nametag Icon"
msgstr "네임택 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Move Icon"
msgstr "이동 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Money Icon"
msgstr "돈 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Minus Icon"
msgstr "마이너스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Migrate Icon"
msgstr "마이그레이션 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Microphone Icon"
msgstr "마이크 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Megaphone Icon"
msgstr "메가폰 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Marker Icon"
msgstr "마커 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Lock Icon"
msgstr "잠금 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Location Icon"
msgstr "위치 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "List View Icon"
msgstr "목록 보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Lightbulb Icon"
msgstr "전구 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Left Right Icon"
msgstr "왼쪽 오른쪽 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Layout Icon"
msgstr "레이아웃 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Laptop Icon"
msgstr "노트북 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Info Icon"
msgstr "정보 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Index Card Icon"
msgstr "색인 카드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "ID Icon"
msgstr "ID 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Hidden Icon"
msgstr "숨김 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Heart Icon"
msgstr "하트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Hammer Icon"
msgstr "해머 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:477
#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Groups Icon"
msgstr "그룹 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Grid View Icon"
msgstr "그리드 보기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Forms Icon"
msgstr "양식 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Flag Icon"
msgstr "깃발 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:581
#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Filter Icon"
msgstr "필터 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Feedback Icon"
msgstr "피드백 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Facebook (alt) Icon"
msgstr "Facebook (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Facebook Icon"
msgstr "페이스북 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "External Icon"
msgstr "외부 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Email (alt) Icon"
msgstr "이메일 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Email Icon"
msgstr "이메일 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:565
#: includes/fields/class-acf-field-icon_picker.php:591
#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Video Icon"
msgstr "비디오 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Unlink Icon"
msgstr "링크 해제 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Underline Icon"
msgstr "밑줄 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Text Color Icon"
msgstr "텍스트색 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Table Icon"
msgstr "표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Strikethrough Icon"
msgstr "취소선 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Spellcheck Icon"
msgstr "맞춤법 검사 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Remove Formatting Icon"
msgstr "서식 제거 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:555
#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Quote Icon"
msgstr "인용 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Paste Word Icon"
msgstr "단어 붙여넣기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paste Text Icon"
msgstr "텍스트 붙여넣기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Paragraph Icon"
msgstr "단락 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Outdent Icon"
msgstr "내어쓰기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Kitchen Sink Icon"
msgstr "주방 싱크 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Justify Icon"
msgstr "맞춤 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Italic Icon"
msgstr "이탤릭체 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Insert More Icon"
msgstr "더 삽입 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Indent Icon"
msgstr "들여쓰기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Help Icon"
msgstr "도움말 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Expand Icon"
msgstr "펼치기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Contract Icon"
msgstr "계약 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:538
#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Code Icon"
msgstr "코드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Break Icon"
msgstr "나누기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Bold Icon"
msgstr "굵은 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Edit Icon"
msgstr "수정 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Download Icon"
msgstr "다운로드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Dismiss Icon"
msgstr "해지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Desktop Icon"
msgstr "데스크톱 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Dashboard Icon"
msgstr "대시보드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Cloud Icon"
msgstr "구름 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Clock Icon"
msgstr "시계 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Clipboard Icon"
msgstr "클립보드 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Chart Pie Icon"
msgstr "원형 차트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Chart Line Icon"
msgstr "선 차트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Chart Bar Icon"
msgstr "막대 차트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Chart Area Icon"
msgstr "영역 차트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Category Icon"
msgstr "카테고리 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Cart Icon"
msgstr "장바구니 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Carrot Icon"
msgstr "당근 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Camera Icon"
msgstr "카메라 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Calendar (alt) Icon"
msgstr "캘린더 (대체) 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Calendar Icon"
msgstr "캘린더 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Businesswoman Icon"
msgstr "비즈니스우먼 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Building Icon"
msgstr "건물 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Book Icon"
msgstr "책 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Backup Icon"
msgstr "백업 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Awards Icon"
msgstr "상 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Art Icon"
msgstr "아트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Arrow Up Icon"
msgstr "위쪽 화살표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Arrow Right Icon"
msgstr "오른쪽 화살표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Left Icon"
msgstr "왼쪽 화살표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Down Icon"
msgstr "아래쪽 화살표 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:449
#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Archive Icon"
msgstr "아카이브 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Analytics Icon"
msgstr "애널리틱스 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Align Right Icon"
msgstr "오른쪽 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Align None Icon"
msgstr "정렬 안 함 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:441
#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Align Left Icon"
msgstr "왼쪽 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:439
#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Align Center Icon"
msgstr "가운데 정렬 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Album Icon"
msgstr "앨범 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Users Icon"
msgstr "사용자 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Tools Icon"
msgstr "도구 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Site Icon"
msgstr "사이트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Settings Icon"
msgstr "설정 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Post Icon"
msgstr "글 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Plugins Icon"
msgstr "플러그인 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Page Icon"
msgstr "페이지 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Network Icon"
msgstr "네트워크 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Multisite Icon"
msgstr "다중 사이트 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Media Icon"
msgstr "미디어 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Links Icon"
msgstr "링크 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Home Icon"
msgstr "홈 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Customizer Icon"
msgstr "커스터마이저 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:419
#: includes/fields/class-acf-field-icon_picker.php:743
msgid "Comments Icon"
msgstr "댓글 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Collapse Icon"
msgstr "접기 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Appearance Icon"
msgstr "모양 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Generic Icon"
msgstr "일반 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:353
msgid "Icon picker requires a value."
msgstr "아이콘 선택기에는 값이 필요합니다."

#: includes/fields/class-acf-field-icon_picker.php:348
msgid "Icon picker requires an icon type."
msgstr "아이콘 선택기에는 아이콘 유형이 필요합니다."

#: includes/fields/class-acf-field-icon_picker.php:317
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"아래의 아이콘 선택기에서 검색어와 일치하는 사용 가능한 아이콘이 업데이트되었"
"습니다."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "No results found for that search term"
msgstr "해당 검색어에 대한 결과를 찾을 수 없습니다."

#: includes/fields/class-acf-field-icon_picker.php:298
msgid "Array"
msgstr "배열"

#: includes/fields/class-acf-field-icon_picker.php:297
msgid "String"
msgstr "문자열"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:285
msgid "Specify the return format for the icon. %s"
msgstr "아이콘의 반환 형식을 지정합니다. %s"

#: includes/fields/class-acf-field-icon_picker.php:270
msgid "Select where content editors can choose the icon from."
msgstr "콘텐츠 편집자가 아이콘을 선택할 수 있는 위치를 선택합니다."

#: includes/fields/class-acf-field-icon_picker.php:242
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "사용하려는 아이콘의 URL(또는 데이터 URI로서의 svg)"

#: includes/fields/class-acf-field-icon_picker.php:225
msgid "Browse Media Library"
msgstr "미디어 라이브러리 찾아보기"

#: includes/fields/class-acf-field-icon_picker.php:216
msgid "The currently selected image preview"
msgstr "현재 선택된 이미지 미리보기"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Click to change the icon in the Media Library"
msgstr "미디어 라이브러리에서 아이콘을 변경하려면 클릭합니다."

#: includes/fields/class-acf-field-icon_picker.php:95
msgid "Search icons..."
msgstr "검색 아이콘..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "미디어 라이브러리"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "대시 아이콘"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"아이콘을 선택할 수 있는 대화형 UI입니다. 대시 아이콘, 미디어 라이브러리 또는 "
"독립형 URL 입력 중에서 선택할 수 있습니다."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "아이콘 선택기"

#: src/Site_Health/Site_Health.php:787
msgid "JSON Load Paths"
msgstr "JSON 로드 경로"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Save Paths"
msgstr "JSON 경로 저장"

#: src/Site_Health/Site_Health.php:772
msgid "Registered ACF Forms"
msgstr "등록된 ACF 양식"

#: src/Site_Health/Site_Health.php:766
msgid "Shortcode Enabled"
msgstr "쇼트코드 사용"

#: src/Site_Health/Site_Health.php:758
msgid "Field Settings Tabs Enabled"
msgstr "필드 설정 탭 사용됨"

#: src/Site_Health/Site_Health.php:750
msgid "Field Type Modal Enabled"
msgstr "필드 유형 모달 사용됨"

#: src/Site_Health/Site_Health.php:742
msgid "Admin UI Enabled"
msgstr "관리자 UI 활성화됨"

#: src/Site_Health/Site_Health.php:733
msgid "Block Preloading Enabled"
msgstr "블록 사전 로드 활성화됨"

#: src/Site_Health/Site_Health.php:721
msgid "Blocks Per ACF Block Version"
msgstr "ACF 블록 버전당 블록 수"

#: src/Site_Health/Site_Health.php:716
msgid "Blocks Per API Version"
msgstr "API 버전별 블록 수"

#: src/Site_Health/Site_Health.php:689
msgid "Registered ACF Blocks"
msgstr "등록된 ACF 블록"

#: src/Site_Health/Site_Health.php:683
msgid "Light"
msgstr "Light"

#: src/Site_Health/Site_Health.php:683
msgid "Standard"
msgstr "표준"

#: src/Site_Health/Site_Health.php:682
msgid "REST API Format"
msgstr "REST API 형식"

#: src/Site_Health/Site_Health.php:674
msgid "Registered Options Pages (PHP)"
msgstr "등록된 옵션 페이지(PHP)"

#: src/Site_Health/Site_Health.php:660
msgid "Registered Options Pages (JSON)"
msgstr "등록된 옵션 페이지(JSON)"

#: src/Site_Health/Site_Health.php:655
msgid "Registered Options Pages (UI)"
msgstr "등록된 옵션 페이지(UI)"

#: src/Site_Health/Site_Health.php:625
msgid "Options Pages UI Enabled"
msgstr "옵션 페이지 UI 활성화됨"

#: src/Site_Health/Site_Health.php:617
msgid "Registered Taxonomies (JSON)"
msgstr "등록된 분류(JSON)"

#: src/Site_Health/Site_Health.php:605
msgid "Registered Taxonomies (UI)"
msgstr "등록된 분류(UI)"

#: src/Site_Health/Site_Health.php:593
msgid "Registered Post Types (JSON)"
msgstr "등록된 글 유형(JSON)"

#: src/Site_Health/Site_Health.php:581
msgid "Registered Post Types (UI)"
msgstr "등록된 글 유형(UI)"

#: src/Site_Health/Site_Health.php:568
msgid "Post Types and Taxonomies Enabled"
msgstr "글 유형 및 분류 사용됨"

#: src/Site_Health/Site_Health.php:561
msgid "Number of Third Party Fields by Field Type"
msgstr "필드 유형별 타사 필드 수"

#: src/Site_Health/Site_Health.php:556
msgid "Number of Fields by Field Type"
msgstr "필드 유형별 필드 수"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "GraphQL에 사용 가능한 필드 그룹"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "REST API에 필드 그룹 사용 가능"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "등록된 필드 그룹(JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "등록된 필드 그룹(PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "등록된 필드 그룹(UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "활성 플러그인"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "부모 테마"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "활성 테마"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "다중 사이트임"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "MySQL 버전"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "워드프레스 버전"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "구독 만료 날짜"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "라이선스 상태"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "라이선스 유형"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "라이선스 URL"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "라이선스 활성화됨"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "무료"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "플러그인 유형"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "플러그인 버전"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"이 섹션에는 지원팀에 제공하는 데 유용할 수 있는 ACF 구성에 대한 디버그 정보"
"가 포함되어 있습니다."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr "이 페이지의 ACF 블록은 저장하기 전에 주의가 필요합니다."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"이 데이터는 출력 중에 변경된 값을 감지할 때 기록됩니다. %1$s코드에서 값을 이"
"스케이프 처리한 후 로그를 지우고 %2$s를 해제합니다. 변경된 값이 다시 감지되"
"면 알림이 다시 나타납니다."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "더이상 안보기"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "콘텐츠 편집자를 위한 지침입니다. 데이터를 제출할 때 표시됩니다."

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has no term selected"
msgstr "학기가 선택되지 않았습니다."

#: includes/admin/post-types/admin-field-group.php:141
msgid "Has any term selected"
msgstr "학기가 선택되어 있음"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms do not contain"
msgstr "용어에 다음이 포함되지 않음"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Terms contain"
msgstr "용어에 다음이 포함됨"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is not equal to"
msgstr "용어가 다음과 같지 않습니다."

#: includes/admin/post-types/admin-field-group.php:137
msgid "Term is equal to"
msgstr "기간은 다음과 같습니다."

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has no user selected"
msgstr "선택한 사용자가 없음"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Has any user selected"
msgstr "사용자가 선택된 사용자 있음"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users do not contain"
msgstr "사용자가 다음을 포함하지 않음"

#: includes/admin/post-types/admin-field-group.php:133
msgid "Users contain"
msgstr "사용자가 다음을 포함합니다."

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is not equal to"
msgstr "사용자가 다음과 같지 않습니다."

#: includes/admin/post-types/admin-field-group.php:131
msgid "User is equal to"
msgstr "사용자가 다음과 같습니다."

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has no page selected"
msgstr "선택된 페이지가 없음"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Has any page selected"
msgstr "선택한 페이지가 있음"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages do not contain"
msgstr "페이지에 다음이 포함되지 않음"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Pages contain"
msgstr "페이지에 다음이 포함됨"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is not equal to"
msgstr "페이지가 다음과 같지 않습니다."

#: includes/admin/post-types/admin-field-group.php:125
msgid "Page is equal to"
msgstr "페이지가 다음과 같습니다."

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has no relationship selected"
msgstr "관계를 선택하지 않음"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has any relationship selected"
msgstr "관계를 선택했음"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has no post selected"
msgstr "선택된 게시글이 없음"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Has any post selected"
msgstr "게시글이 선택되어 있음"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts do not contain"
msgstr "글에 다음이 포함되지 않음"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Posts contain"
msgstr "글에 다음이 포함됨"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is not equal to"
msgstr "글은 다음과 같지 않습니다."

#: includes/admin/post-types/admin-field-group.php:117
msgid "Post is equal to"
msgstr "글은 다음과 같습니다."

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships do not contain"
msgstr "관계에 다음이 포함되지 않음"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationships contain"
msgstr "관계에 다음이 포함됩니다."

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is not equal to"
msgstr "관계가 다음과 같지 않습니다."

#: includes/admin/post-types/admin-field-group.php:113
msgid "Relationship is equal to"
msgstr "관계는 다음과 같습니다."

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF 필드"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO 기능"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "PRO 라이선스 갱신하여 잠금 해제"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "PRO 라이선스 갱신"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO 라이선스가 활성화되어 있지 않으면 PRO 필드를 편집할 수 없습니다."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"ACF 블록에 할당된 필드 그룹을 편집하려면 ACF PRO 라이선스를 활성화하세요."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "이 옵션 페이지를 편집하려면 ACF PRO 라이선스를 활성화하세요."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"이스케이프 처리된 HTML 값을 반환하는 것은 format_value도 참인 경우에만 가능합"
"니다. 보안을 위해 필드 값이 반환되지 않았습니다."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"이스케이프된 HTML 값을 반환하는 것은 형식_값도 참인 경우에만 가능합니다. 보안"
"을 위해 필드 값은 반환되지 않습니다."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF가 이제 <code>the_field</code> 또는 ACF 쇼트코드로 렌더링될 때 안전하"
"지 않은 HTML을 자동으로 이스케이프 처리합니다. 이 변경으로 인해 일부 필드의 "
"출력이 수정된 것을 감지했지만 이는 중요한 변경 사항은 아닐 수 있습니다. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "자세한 내용은 사이트 관리자 또는 개발자에게 문의하세요."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "자세히 알아보기"

#: includes/admin/admin.php:64
msgid "Hide&nbsp;details"
msgstr "세부 정보 숨기기"

#: includes/admin/admin.php:63 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "세부 정보 표시"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - %3$s를 통해 렌더링되었습니다."

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "ACF PRO 라이선스 갱신"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "라이선스 갱신"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "라이선스 관리"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "블록 에디터에서 '높음' 위치가 지원되지 않음"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "ACF PRO로 업그레이드"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF <a href=\"%s\" target=\"_blank\">옵션 페이지</a>는 필드를 통해 전역 설정"
"을 관리하기 위한 사용자 지정 관리자 페이지입니다. 여러 페이지와 하위 페이지"
"를 만들 수 있습니다."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "옵션 추가 페이지"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "제목의 플레이스홀더로 사용되는 편집기에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "제목 플레이스홀더"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4개월 무료"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr " (%s에서 복제됨)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "옵션 페이지 선택"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "중복 분류"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "분류 체계 만들기"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "중복 글 유형"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "글 유형 만들기"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "필드 그룹 연결"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "필드 추가"

#: includes/admin/post-types/admin-field-group.php:146
msgid "This Field"
msgstr "이 필드"

#: includes/admin/admin.php:385
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:383
msgid "Feedback"
msgstr "피드백"

#: includes/admin/admin.php:381
msgid "Support"
msgstr "지원"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:356
msgid "is developed and maintained by"
msgstr "에 의해 개발 및 유지 관리됩니다."

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "선택한 필드 그룹의 위치 규칙에 이 %s를 추가합니다."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"양방향 설정을 활성화하면 이 필드에 대해 선택한 각 값에 대해 대상 필드에서 값"
"을 업데이트하고 업데이트할 항목의 글 ID, 분류 ID 또는 사용자 ID를 추가하거나 "
"제거할 수 있습니다. 자세한 내용은 <a href=\"%s\" target=\"_blank\">문서</a>"
"를 참조하세요."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"업데이트 중인 항목에 대한 참조를 다시 저장할 필드를 선택합니다. 이 필드를 선"
"택할 수 있습니다. 대상 필드는 이 필드가 표시되는 위치와 호환되어야 합니다. 예"
"를 들어 이 필드가 분류에 표시되는 경우 대상 필드는 분류 유형이어야 합니다."

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "대상 필드"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "이 ID를 다시 참조하여 선택한 값의 필드를 업데이트합니다."

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "양방향"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s 필드"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "여러 개 선택"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "WP 엔진 로고"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"소문자, 밑줄 및 대시만 입력할 수 있으며 최대 32자까지 입력할 수 있습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "이 택소노미의 용어를 할당하기 위한 기능 이름입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "용어 할당 가능"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "이 택소노미의 용어를 삭제하기 위한 기능 이름입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "약관 삭제 가능"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "이 택소노미의 용어를 편집하기 위한 기능 이름입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "약관 편집 가능"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "이 택소노미의 용어를 관리하기 위한 기능 이름입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "약관 관리 가능"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"검색 결과 및 택소노미 아카이브 페이지에서 글을 제외할지 여부를 설정합니다."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP 엔진의 더 많은 도구"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "워드프레스로 제작하는 사용자를 위해 %s 팀에서 제작했습니다."

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "가격 및 업그레이드 보기"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:280
msgid "Learn More"
msgstr "더 알아보기"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"ACF 블록 및 옵션 페이지와 같은 기능과 리피터, 유연한 콘텐츠, 복제 및 갤러리"
"와 같은 정교한 필드 유형을 사용하여 워크플로우를 가속화하고 더 나은 웹사이트"
"를 개발할 수 있습니다."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "ACF 프로로 고급 기능을 잠금 해제하고 더 많은 것을 구축하세요."

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s 필드"

#: includes/admin/post-types/admin-taxonomies.php:263
msgid "No terms"
msgstr "용어 없음"

#: includes/admin/post-types/admin-taxonomies.php:236
msgid "No post types"
msgstr "게시물 유형 없음"

#: includes/admin/post-types/admin-post-types.php:260
msgid "No posts"
msgstr "게시물 없음"

#: includes/admin/post-types/admin-post-types.php:234
msgid "No taxonomies"
msgstr "택소노미 없음"

#: includes/admin/post-types/admin-post-types.php:179
#: includes/admin/post-types/admin-taxonomies.php:178
msgid "No field groups"
msgstr "필드 그룹 없음"

#: includes/admin/post-types/admin-field-groups.php:251
msgid "No fields"
msgstr "필드 없음"

#: includes/admin/post-types/admin-field-groups.php:124
#: includes/admin/post-types/admin-post-types.php:143
#: includes/admin/post-types/admin-taxonomies.php:142
msgid "No description"
msgstr "설명 없음"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "모든 게시물 상태"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"이 택소노미 키는 ACF 외부에 등록된 다른 택소노미에서 이미 사용 중이므로 사용"
"할 수 없습니다."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"이 택소노미 키는 이미 ACF의 다른 택소노미에서 사용 중이므로 사용할 수 없습니"
"다."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"택소노미 키에는 소문자 영숫자 문자, 밑줄 또는 대시만 포함되어야 합니다."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "택소노미 키는 32자 미만이어야 합니다."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "휴지통에 택소노미가 없습니다."

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "택소노미가 없습니다."

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "택소노미 검색"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "택소노미 보기"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "새로운 택소노미"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "택소노미 편집"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "새 택소노미 추가"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "휴지통에서 게시물 유형을 찾을 수 없습니다."

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "게시물 유형을 찾을 수 없습니다."

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "게시물 유형 검색"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "게시물 유형 보기"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "새 게시물 유형"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "게시물 유형 편집"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "새 게시물 유형 추가"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"이 게시물 유형 키는 ACF 외부에 등록된 다른 게시물 유형에서 이미 사용 중이므"
"로 사용할 수 없습니다."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"이 게시물 유형 키는 이미 ACF의 다른 게시물 유형에서 사용 중이므로 사용할 수 "
"없습니다."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"이 필드는 워드프레스 <a href=\"%s\" target=\"_blank\">예약어</a>가 아니어야 "
"합니다."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"게시물 유형 키에는 소문자 영숫자 문자, 밑줄 또는 대시만 포함해야 합니다."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "게시물 유형 키는 20자 미만이어야 합니다."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "ACF 블록에서는 이 필드를 사용하지 않는 것이 좋습니다."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"게시물 및 페이지에 표시되는 워드프레스 WYSIWYG 편집기를 표시하여 멀티미디어 "
"콘텐츠도 허용하는 풍부한 텍스트 편집 환경을 허용합니다."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG 편집기"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"데이터 개체 간의 관계를 만드는 데 사용할 수 있는 하나 이상의 사용자를 선택할 "
"수 있습니다."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "웹 주소를 저장하기 위해 특별히 설계된 텍스트 입력입니다."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"1 또는 0 값(켜기 또는 끄기, 참 또는 거짓 등)을 선택할 수 있는 토글입니다. 양"
"식화된 스위치 또는 확인란으로 표시할 수 있습니다."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"시간을 선택하기 위한 대화형 UI입니다. 시간 형식은 필드 설정을 사용하여 사용"
"자 정의할 수 있습니다."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "텍스트 단락을 저장하기 위한 기본 텍스트 영역 입력."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "단일 문자열 값을 저장하는 데 유용한 기본 텍스트 입력입니다."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"필드 설정에 지정된 기준 및 옵션에 따라 하나 이상의 택소노미 용어를 선택할 수 "
"있습니다."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"편집 화면에서 필드를 탭 섹션으로 그룹화할 수 있습니다. 필드를 정리하고 체계적"
"으로 유지하는 데 유용합니다."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "지정한 선택 항목이 있는 드롭다운 목록입니다."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"하나 이상의 게시물, 페이지 또는 사용자 정의 게시물 유형 항목을 선택하여 현재 "
"편집 중인 항목과의 관계를 만드는 이중 열 인터페이스입니다. 검색 및 필터링 옵"
"션이 포함되어 있습니다."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"범위 슬라이더 요소를 사용하여 지정된 범위 내에서 숫자 값을 선택하기 위한 입력"
"입니다."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"사용자가 지정한 값에서 단일 선택을 할 수 있도록 하는 라디오 버튼 입력 그룹입"
"니다."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"검색 옵션이 있는 하나 이상의 게시물, 페이지 또는 게시물 유형 항목을 선택할 "
"수 있는 대화형 및 사용자 정의 가능한 UI입니다. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "마스킹된 필드를 사용하여 암호를 제공하기 위한 입력입니다."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "게시물 상태로 필터링"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"검색 옵션과 함께 하나 이상의 게시물, 페이지, 사용자 정의 게시물 유형 항목 또"
"는 아카이브 URL을 선택하는 대화형 드롭다운."

#: includes/fields/class-acf-field-oembed.php:23
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"기본 워드프레스 oEmbed 기능을 사용하여 비디오, 이미지, 트윗, 오디오 및 기타 "
"콘텐츠를 삽입하기 위한 대화형 구성 요소입니다."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "숫자 값으로 제한된 입력입니다."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"다른 필드와 함께 편집자에게 메시지를 표시하는 데 사용됩니다. 필드에 대한 추"
"가 컨텍스트 또는 지침을 제공하는 데 유용합니다."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"워드프레스 기본 링크 선택기를 사용하여 제목 및 대상과 같은 링크 및 해당 속성"
"을 지정할 수 있습니다."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"기본 워드프레스 미디어 선택기를 사용하여 이미지를 업로드하거나 선택합니다."

#: includes/fields/class-acf-field-group.php:23
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"필드를 그룹으로 구성하여 데이터와 편집 화면을 더 잘 구성하는 방법을 제공합니"
"다."

#: includes/fields/class-acf-field-google-map.php:23
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Google 지도를 사용하여 위치를 선택하기 위한 대화형 UI입니다. 올바르게 표시하"
"려면 Google Maps API 키와 추가 구성이 필요합니다."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"기본 워드프레스 미디어 선택기를 사용하여 파일을 업로드하거나 선택합니다."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "이메일 주소를 저장하기 위해 특별히 설계된 텍스트 입력입니다."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"날짜와 시간을 선택하기 위한 대화형 UI입니다. 날짜 반환 형식은 필드 설정을 사"
"용하여 사용자 정의할 수 있습니다."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"날짜 선택을 위한 대화형 UI입니다. 날짜 반환 형식은 필드 설정을 사용하여 사용"
"자 정의할 수 있습니다."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "색상을 선택하거나 16진수 값을 지정하기 위한 대화형 UI입니다."

#: includes/fields/class-acf-field-checkbox.php:37
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"사용자가 지정한 하나 이상의 값을 선택할 수 있도록 하는 확인란 입력 그룹입니"
"다."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"지정한 값이 있는 버튼 그룹으로, 사용자는 제공된 값에서 하나의 옵션을 선택할 "
"수 있습니다."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"콘텐츠를 편집하는 동안 표시되는 접을 수 있는 패널로 사용자 정의 필드를 그룹화"
"하고 구성할 수 있습니다. 큰 데이터 세트를 깔끔하게 유지하는 데 유용합니다."

#: includes/fields.php:450
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"반복해서 반복할 수 있는 하위 필드 세트의 상위 역할을 하여 슬라이드, 팀 구성"
"원 및 클릭 유도 문안 타일과 같은 반복 콘텐츠에 대한 솔루션을 제공합니다."

#: includes/fields.php:440
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"이는 첨부 파일 모음을 관리하기 위한 대화형 인터페이스를 제공합니다. 대부분의 "
"설정은 이미지 필드 유형과 유사합니다. 추가 설정을 통해 갤러리에서 새 첨부 파"
"일이 추가되는 위치와 허용되는 첨부 파일의 최소/최대 수를 지정할 수 있습니다."

#: includes/fields.php:430
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"간단하고 구조화된 레이아웃 기반 편집기를 제공합니다. 유연한 콘텐츠 필드를 사"
"용하면 레이아웃과 하위 필드를 사용하여 사용 가능한 블록을 디자인함으로써 전"
"체 제어로 콘텐츠를 정의, 생성 및 관리할 수 있습니다."

#: includes/fields.php:420
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"이를 통해 기존 필드를 선택하고 표시할 수 있습니다. 데이터베이스의 어떤 필드"
"도 복제하지 않지만 런타임에 선택한 필드를 로드하고 표시합니다. 복제 필드는 선"
"택한 필드로 자신을 교체하거나 선택한 필드를 하위 필드 그룹으로 표시할 수 있습"
"니다."

#: includes/fields.php:417
msgctxt "noun"
msgid "Clone"
msgstr "복제"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:332
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "프로"

#: includes/fields.php:330 includes/fields.php:387
msgid "Advanced"
msgstr "고급"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON(최신)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "원본"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "게시물 ID가 잘못되었습니다."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "검토를 위해 잘못된 게시물 유형을 선택했습니다."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "더 보기"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "튜토리얼"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "필드 선택"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "다른 검색어를 입력하거나 %s 찾아보기"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "인기 분야"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:115
msgid "No search results for '%s'"
msgstr "'%s'에 대한 검색 결과가 없습니다."

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "검색 필드..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "필드 유형 선택"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "인기"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "택소노미 추가"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "게시물 유형 콘텐츠를 택소노미하기 위한 사용자 정의 택소노미 생성"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "첫 번째 택소노미 추가"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "계층적 택소노미는 범주와 같은 하위 항목을 가질 수 있습니다."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "프런트엔드와 관리자 알림판에서 택소노미를 볼 수 있습니다."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "이 택소노미로 택소노미할 수 있는 하나 이상의 게시물 유형입니다."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "장르"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "장르"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "장르"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr "`WP_REST_Terms_Controller` 대신 사용할 선택적 사용자 정의 컨트롤러."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "REST API에서 이 게시물 유형을 노출합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "쿼리 변수 이름 사용자 지정"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"{query_var}={term_slug}와 같이 예쁘지 않은 퍼머링크를 사용하여 용어에 액세스"
"할 수 있습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "계층적 택소노미에 대한 URL의 상위-하위 용어."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "URL에 사용된 슬러그 사용자 정의"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "이 택소노미에 대한 퍼머링크가 비활성화되었습니다."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"택소노미 키를 슬러그로 사용하여 URL을 다시 작성합니다. 귀하의 퍼머링크 구조는"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "택소노미 키"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "이 택소노미에 사용할 퍼머링크 유형을 선택하십시오."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "게시물 유형 목록 화면에 택소노미에 대한 열을 표시합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "관리 열 표시"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "빠른/일괄 편집 패널에 택소노미를 표시합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "빠른 편집"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Tag Cloud Widget 컨트롤에 택소노미를 나열합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "태그 클라우드"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"메타박스에서 저장된 택소노미 데이터를 정리하기 위해 호출할 PHP 함수 이름입니"
"다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "메타 박스 정리 콜백"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "메타박스 콜백 등록"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "메타박스 없음"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "커스텀 메타 박스"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"콘텐츠 에디터 화면의 메타박스를 제어합니다. 기본적으로 계층적 택소노미에는 범"
"주 메타 상자가 표시되고 비계층적 택소노미에는 태그 메타 상자가 표시됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "메타박스"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "카테고리 메타박스"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "태그 메타박스"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "태그에 대한 링크"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "블록 편집기에서 사용되는 탐색 링크 블록 변형에 대해 설명합니다."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "%s에 대한 링크"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "태그 링크"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr "블록 편집기에서 사용되는 탐색 링크 블록 변형에 대한 제목을 지정합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← 태그로 이동"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"용어를 업데이트한 후 기본 인덱스로 다시 연결하는 데 사용되는 텍스트를 할당합"
"니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "항목으로 돌아가기"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← %s로 이동"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "태그 목록"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "테이블의 숨겨진 제목에 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "태그 목록 탐색"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "테이블 페이지 매김 숨겨진 제목에 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "카테고리별로 필터링"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "게시물 목록 테이블의 필터 버튼에 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "항목별로 필터링"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "%s로 필터링"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"설명은 기본적으로 눈에 띄지 않습니다. 그러나 일부 테마에서는 이를 표시할 수 "
"있습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "태그 편집 화면의 설명 필드에 대해 설명합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "설명 필드 설명"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"계층 구조를 만들 상위 용어를 할당합니다. 예를 들어 재즈라는 용어는 Bebop과 "
"Big Band의 부모입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "태그 편집 화면의 상위 필드를 설명합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "상위 필드 설명"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"\"slug\"는 이름의 URL 친화적 버전입니다. 일반적으로 모두 소문자이며 문자, 숫"
"자 및 하이픈만 포함합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "태그 편집 화면의 Slug 필드에 대해 설명합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "슬러그 필드 설명"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "이름은 사이트에 표시되는 방식입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "태그 편집 화면의 이름 필드를 설명합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "이름 필드 설명"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "태그 없음"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"태그 또는 카테고리를 사용할 수 없을 때 게시물 및 미디어 목록 테이블에 표시되"
"는 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "용어 없음"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "%s 없음"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "태그를 찾을 수 없습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"사용 가능한 태그가 없는 경우 택소노미 메타 상자에서 '가장 많이 사용하는 항목"
"에서 선택' 텍스트를 클릭할 때 표시되는 텍스트를 지정하고, 택소노미 항목이 없"
"는 경우 용어 목록 테이블에 사용되는 텍스트를 지정합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "찾을 수 없음"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "자주 사용됨 탭의 제목 필드에 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "가장 많이 사용"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "가장 많이 사용되는 태그 중에서 선택"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"JavaScript가 비활성화된 경우 메타 상자에 사용되는 '가장 많이 사용되는 항목에"
"서 선택' 텍스트를 지정합니다. 비계층적 택소노미에만 사용됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "가장 많이 사용하는 것 중에서 선택"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "가장 많이 사용된 %s에서 선택"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "태그 추가 또는 제거"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"JavaScript가 비활성화된 경우 메타 상자에 사용되는 항목 추가 또는 제거 텍스트"
"를 할당합니다. 비계층적 택소노미에만 사용됨"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "항목 추가 또는 제거"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s 추가 또는 제거"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "쉼표로 태그 구분"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"택소노미 메타 상자에 사용되는 쉼표 텍스트로 별도의 항목을 할당합니다. 비계층"
"적 택소노미에만 사용됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "쉼표로 항목 구분"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "%s를 쉼표로 구분"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "인기 태그"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "인기 항목 텍스트를 할당합니다. 비계층적 택소노미에만 사용됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "인기 상품"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "인기 있는 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "검색 태그"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "검색 항목 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "상위 카테고리:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "부모 항목 텍스트를 할당하지만 끝에 콜론(:)이 추가됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "콜론이 있는 상위 항목"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "상위 카테고리"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "상위 항목 텍스트를 지정합니다. 계층적 택소노미에만 사용됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "상위 항목"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "부모 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "새 태그 이름"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "새 항목 이름 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "새 항목 이름"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "새 %s 이름"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "새 태그 추가"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "새 항목 추가 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "태그 업데이트"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "업데이트 항목 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "항목 업데이트"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "업데이트 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "태그 보기"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "편집하는 동안 용어를 보려면 관리 표시줄에서."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "태그 편집"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "용어를 편집할 때 편집기 화면 상단에 있습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "모든 태그"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "모든 항목 텍스트를 지정합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "메뉴 이름 텍스트를 할당합니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "메뉴 레이블"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "활성 택소노미가 활성화되고 워드프레스에 등록됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "택소노미에 대한 설명 요약입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "용어에 대한 설명 요약입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "용어 설명"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "한 단어, 공백 없음. 밑줄과 대시가 허용됩니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "용어 슬러그"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "기본 용어의 이름입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "용어 이름"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"삭제할 수 없는 택소노미에 대한 용어를 만듭니다. 기본적으로 게시물에 대해 선택"
"되지 않습니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "기본 용어"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"이 택소노미의 용어를 `wp_set_object_terms()`에 제공된 순서대로 정렬해야 하는"
"지 여부입니다."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "용어 정렬"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "게시물 유형 추가"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"사용자 정의 게시물 유형을 사용하여 표준 게시물 및 페이지 이상으로 워드프레스"
"의 기능을 확장합니다."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "첫 게시물 유형 추가"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "내가 무엇을 하는지 알고 모든 옵션을 보여주세요."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "고급 구성"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "계층적 게시물 유형은 페이지처럼 하위 항목을 가질 수 있습니다."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "계층형"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "프런트엔드와 관리 알림판에서 볼 수 있습니다."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "공개"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "동영상"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "소문자, 밑줄 및 대시만 가능하며 최대 20자입니다."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "동영상"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "단수 레이블"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "동영상"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "복수 레이블"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr "`WP_REST_Posts_Controller` 대신 사용할 선택적 사용자 정의 컨트롤러."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "컨트롤러 클래스"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "REST API URL의 네임스페이스 부분입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "네임스페이스 경로"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "게시물 유형 REST API URL의 기본 URL입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "기본 URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"REST API에서 이 게시물 유형을 노출합니다. 블록 편집기를 사용하는 데 필요합니"
"다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "REST API에 표시"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "쿼리 변수 이름을 사용자 지정합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "쿼리 변수"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "쿼리 변수 지원 없음"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "맞춤 쿼리 변수"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"항목은 예를 들어 non-pretty permalink를 사용하여 액세스할 수 있습니다. "
"{post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "쿼리 변수 지원"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr "항목 및 항목에 대한 URL은 쿼리 문자열을 사용하여 액세스할 수 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "공개적으로 쿼리 가능"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "아카이브 URL에 대한 사용자 지정 슬러그입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "아카이브 슬러그"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"테마의 아카이브 템플릿 파일로 사용자 정의할 수 있는 항목 아카이브가 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "아카이브"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "아카이브와 같은 항목 URL에 대한 페이지 매김 지원."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "쪽수 매기기"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "게시물 유형 항목에 대한 RSS 피드 URL입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "피드 URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"URL에 `WP_Rewrite::$front` 접두사를 추가하도록 퍼머링크 구조를 변경합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "프런트 URL 프리픽스"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "URL에 사용된 슬러그를 사용자 지정합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL 슬러그"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "이 게시물 유형에 대한 퍼머링크가 비활성화되었습니다."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"아래 입력에 정의된 사용자 지정 슬러그를 사용하여 URL을 다시 작성합니다. 귀하"
"의 퍼머링크 구조는"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "퍼머링크 없음(URL 재작성 방지)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "맞춤 퍼머링크"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "게시물 유형 키"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"게시물 유형 키를 슬러그로 사용하여 URL을 다시 작성하십시오. 귀하의 퍼머링크 "
"구조는"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "퍼머링크 재작성"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "해당 사용자가 삭제되면 해당 사용자가 항목을 삭제합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "사용자와 함께 삭제"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "게시물 유형을 '도구' > '내보내기'에서 내보낼 수 있도록 허용합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "내보내기 가능"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "기능에 사용할 복수형을 선택적으로 제공합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "복수 기능 이름"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"이 게시물 유형의 기능을 기반으로 하려면 다른 게시물 유형을 선택하십시오."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "단일 기능 이름"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"기본적으로 게시 유형의 기능은 '게시' 기능 이름을 상속합니다. edit_post, "
"delete_posts. 예를 들어 게시물 유형별 기능을 사용하도록 설정합니다. "
"edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "기능 이름 바꾸기"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "검색에서 제외"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"'모양' > '메뉴' 화면에서 항목을 메뉴에 추가할 수 있도록 허용합니다. '화면 옵"
"션'에서 켜야 합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "모양 메뉴 지원"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "관리 표시줄의 '새로 만들기' 메뉴에 항목으로 나타납니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "관리 표시줄에 표시"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "커스텀 메타 박스 콜백"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Menu Icon"
msgstr "메뉴 아이콘"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "관리 알림판의 사이드바 메뉴에 있는 위치입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "메뉴 위치"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"기본적으로 게시물 유형은 관리자 메뉴에서 새로운 최상위 항목을 가져옵니다. 여"
"기에 기존 최상위 항목이 제공되면 게시물 유형이 그 아래 하위 메뉴 항목으로 추"
"가됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "관리자 메뉴 부모"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "사이드바 메뉴의 관리 편집기 탐색."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "관리자 메뉴에 표시"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "어드민 알림판에서 항목을 수정하고 관리할 수 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "UI에 표시"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "게시물에 대한 링크입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "탐색 링크 블록 변형에 대한 설명입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "항목 링크 설명"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "%s에 대한 링크입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "링크 게시"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "탐색 링크 블록 변형의 제목입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "항목 링크"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s 링크"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "게시물이 업데이트되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "항목이 업데이트된 후 편집기 알림에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "항목 업데이트됨"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s 업데이트되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "게시물 예정."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "항목 예약 후 편집기 알림에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "예약된 항목"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s 예약됨."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "게시물이 초안으로 돌아갔습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "항목을 임시글로 되돌린 후 편집기 알림에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "초안으로 되돌린 항목"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s이(가) 초안으로 되돌아갔습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "게시물이 비공개로 게시되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "비공개 항목 게시 후 편집기 알림에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "비공개로 게시된 항목"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s은(는) 비공개로 게시되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "게시물이 게시되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "항목을 게시한 후 편집기 알림에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "게시된 항목"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s이(가) 게시되었습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "게시물 목록"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr "게시물 유형 목록 화면의 항목 목록에 대한 화면 판독기에서 사용됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "항목 목록"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s 목록"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "게시물 목록 탐색"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"게시물 유형 목록 화면에서 필터 목록 페이지 매김을 위해 스크린 리더에서 사용됩"
"니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "항목 목록 탐색"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s 목록 탐색"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "날짜별로 게시물 필터링"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"게시물 유형 목록 화면에서 날짜 제목으로 필터링하기 위해 화면 판독기에서 사용"
"됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "날짜별로 항목 필터링"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "날짜별로 %s 필터링"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "게시물 목록 필터링"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"게시물 유형 목록 화면의 필터 링크 제목에 대해 스크린 리더에서 사용됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "항목 목록 필터링"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "%s 목록 필터링"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "이 항목에 업로드된 모든 미디어를 표시하는 미디어 모달에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "이 항목에 업로드됨"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "이 %s에 업로드됨"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "게시물에 삽입"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "콘텐츠에 미디어를 추가할 때 버튼 레이블로 사용합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "미디어에 삽입 버튼"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "%s에 삽입"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "추천 이미지로 사용"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "이미지를 추천 이미지로 사용하도록 선택하기 위한 버튼 레이블로."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "추천 이미지 사용"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "추천 이미지 삭제"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "추천 이미지를 제거할 때 버튼 레이블로."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "추천 이미지 제거"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "추천 이미지 설정"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "추천 이미지를 설정할 때 버튼 레이블로."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "추천 이미지 설정"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "대표 이미지"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "추천 이미지 메타 상자의 제목에 사용되는 편집기에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "추천 이미지 메타 상자"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "게시물 속성"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "게시물 속성 메타 상자의 제목에 사용되는 편집기에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "속성 메타박스"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s 속성"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "게시물 아카이브"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"아카이브가 활성화된 CPT의 기존 메뉴에 항목을 추가할 때 표시되는 게시물 목록"
"에 이 레이블이 있는 '게시물 유형 아카이브' 항목을 추가합니다. '실시간 미리보"
"기' 모드에서 메뉴를 편집하고 사용자 정의 아카이브 슬러그가 제공되었을 때만 나"
"타납니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "아카이브 탐색 메뉴"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s 아카이브"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "휴지통에 게시물이 없습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "휴지통에 게시물이 없을 때 게시물 유형 목록 화면 상단에 표시됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "휴지통에 항목이 없습니다."

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "휴지통에서 %s을(를) 찾을 수 없습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "게시물이 없습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr "표시할 게시물이 없을 때 게시물 유형 목록 화면 상단에 표시됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "제품을 찾지 못했습니다"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "%s 없음"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "게시물 검색"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "항목 검색 시 항목 화면 상단"

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "항목 검색"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "%s 검색"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "상위 페이지:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "게시물 유형 목록 화면의 계층적 유형의 경우."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "상위 품목 접두어"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "부모 %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "새로운 게시물"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "새로운 물품"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "신규 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "새 게시물 추가"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "새 항목을 추가할 때 편집기 화면 상단에 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "새 항목 추가"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "새 %s 추가"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "게시물 보기"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"게시물 유형이 아카이브를 지원하고 홈 페이지가 해당 게시물 유형의 아카이브가 "
"아닌 경우 '모든 게시물' 보기의 관리 표시줄에 나타납니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "항목 보기"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "게시물 보기"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "항목을 편집할 때 관리 표시줄에서 항목을 봅니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "항목 보기"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s 보기"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "게시물 수정"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "항목을 편집할 때 편집기 화면 상단에 있습니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "항목 편집"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s 편집"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "모든 게시물"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "관리 알림판의 게시물 유형 하위 메뉴에서."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "모든 항목"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "모든 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "게시물 유형의 관리자 메뉴 이름입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "메뉴명"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "단수 및 복수 레이블을 사용하여 모든 레이블 다시 생성하기"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "재생성"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "활성 게시물 유형이 활성화되고 워드프레스에 등록됩니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "게시물 유형에 대한 설명 요약입니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "맞춤 추가"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "콘텐츠 편집기에서 다양한 기능을 활성화합니다."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "글 형식"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "편집기"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "트랙백"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "게시물 유형의 항목을 택소노미하려면 기존 택소노미를 선택하십시오."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "필드 찾아보기"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "가져올 항목 없음"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Custom Post Type UI 플러그인을 비활성화할 수 있습니다."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "사용자 정의 게시물 유형 UI에서 %d개의 항목을 가져왔습니다. -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "택소노미를 가져오지 못했습니다."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "게시물 유형을 가져오지 못했습니다."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "가져오기 위해 선택된 사용자 지정 게시물 유형 UI 플러그인이 없습니다."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "가져온 %s개 항목"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"이미 존재하는 키와 동일한 키를 사용하여 게시물 유형 또는 택소노미를 가져오면 "
"기존 게시물 유형 또는 택소노미에 대한 설정을 가져오기의 설정으로 덮어씁니다."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "사용자 정의 게시물 유형 UI에서 가져오기"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"다음 코드는 선택한 항목의 로컬 버전을 등록하는 데 사용할 수 있습니다. 필드 그"
"룹, 게시물 유형 또는 택소노미를 로컬에 저장하면 더 빠른 로드 시간, 버전 제어 "
"및 동적 필드/설정과 같은 많은 이점을 제공할 수 있습니다. 다음 코드를 복사하"
"여 테마의 functions.php 파일에 붙여넣거나 외부 파일에 포함시킨 다음 ACF 관리"
"자에서 항목을 비활성화하거나 삭제하십시오."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "내보내기 - PHP 생성"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "내보내다"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "택소노미 선택"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "게시물 유형 선택"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "내보낸 %s개 항목"

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "범주"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "꼬리표"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s 택소노미 생성됨"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s 택소노미 업데이트됨"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "택소노미 초안이 업데이트되었습니다."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "예정된 택소노미."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "택소노미가 제출되었습니다."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "택소노미가 저장되었습니다."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "택소노미가 삭제되었습니다."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "택소노미가 업데이트되었습니다."

#: includes/admin/post-types/admin-taxonomies.php:347
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"다른 플러그인 또는 테마에서 등록한 다른 택소노미에서 해당 키를 사용 중이므로 "
"이 택소노미를 등록할 수 없습니다."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:329
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "%s개의 택소노미가 동기화되었습니다."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:322
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "%s개의 택소노미가 중복되었습니다."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:315
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "%s개의 택소노미가 비활성화되었습니다."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:308
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "%s개의 택소노미가 활성화되었습니다."

#: includes/admin/post-types/admin-taxonomies.php:109
msgid "Terms"
msgstr "용어"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:323
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "%s 게시물 유형이 동기화되었습니다."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:316
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "%s 게시물 유형이 중복되었습니다."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:309
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "%s 게시물 유형이 비활성화되었습니다."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:302
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "%s 게시물 유형이 활성화되었습니다."

#: includes/admin/post-types/admin-post-types.php:84
#: includes/admin/post-types/admin-taxonomies.php:107
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "게시물 유형"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "고급 설정"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "기본 설정"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:341
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"이 게시물 유형은 다른 플러그인 또는 테마에서 등록한 다른 게시물 유형에서 해"
"당 키를 사용 중이므로 등록할 수 없습니다."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "페이지"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "기존 필드 그룹 연결"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s 게시물 유형이 생성됨"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "%s에 필드 추가"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s 게시물 유형 업데이트됨"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "게시물 유형 초안이 업데이트되었습니다."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "예정된 게시물 유형입니다."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "게시물 유형이 제출되었습니다."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "게시물 유형이 저장되었습니다."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "게시물 유형이 업데이트되었습니다."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "게시물 유형이 삭제되었습니다."

#: includes/admin/post-types/admin-field-group.php:145
msgid "Type to search..."
msgstr "검색하려면 입력하세요..."

#: includes/admin/post-types/admin-field-group.php:100
msgid "PRO Only"
msgstr "프로 전용"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field groups linked successfully."
msgstr "필드 그룹이 성공적으로 연결되었습니다."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:195
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Custom Post Type UI에 등록된 Post Type과 Taxonomies를 가져와서 ACF로 관리합니"
"다. <a href=\"%s\">시작하기</a>."

#: includes/admin/admin.php:47 includes/admin/admin.php:385
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "택소노미"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "게시물 유형"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "완료"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "필드 그룹"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "하나 이상의 필드 그룹 선택..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "연결할 필드 그룹을 선택하십시오."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "필드 그룹이 성공적으로 연결되었습니다."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:342
#: includes/admin/post-types/admin-taxonomies.php:348
msgctxt "post status"
msgid "Registration Failed"
msgstr "등록 실패"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"이 항목은 다른 플러그인 또는 테마에서 등록한 다른 항목에서 해당 키를 사용 중"
"이므로 등록할 수 없습니다."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "권한"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "가시성"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "레이블"

#: includes/admin/post-types/admin-field-group.php:278
msgid "Field Settings Tabs"
msgstr "필드 설정 탭"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[미리보기에 사용할 수 없는 ACF 쇼트코드 값]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:571
msgid "Close Modal"
msgstr "모달 닫기"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field moved to other group"
msgstr "필드가 다른 그룹으로 이동됨"

#: includes/assets.php:351
msgid "Close modal"
msgstr "모달 닫기"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "이 탭에서 새 탭 그룹을 시작합니다."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "새 탭 그룹"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Select2를 사용하여 스타일이 적용된 체크박스 사용"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "다른 선택 저장"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "다른 선택 허용"

#: includes/fields/class-acf-field-checkbox.php:433
msgid "Add Toggle All"
msgstr "모두 전환 추가"

#: includes/fields/class-acf-field-checkbox.php:392
msgid "Save Custom Values"
msgstr "사용자 지정 값 저장"

#: includes/fields/class-acf-field-checkbox.php:381
msgid "Allow Custom Values"
msgstr "사용자 지정 값 허용"

#: includes/fields/class-acf-field-checkbox.php:147
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"체크박스 맞춤 값은 비워둘 수 없습니다. 비어 있는 값을 모두 선택 취소합니다."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "업데이트"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields 로고"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "변경 사항 저장"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "필드 그룹 제목"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "제목 추가"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"ACF가 처음이신가요? <a href=\"%s\" target=\"_blank\">시작 가이드</a>를 살펴보"
"세요."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "필드 그룹 추가"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF는 <a href=\"%s\" target=\"_blank\">필드 그룹</a>을 사용하여 사용자 정의 "
"필드를 함께 그룹화한 다음 해당 필드를 편집 화면에 첨부합니다."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "첫 번째 필드 그룹 추가"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "옵션 페이지"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF 블록"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "갤러리 필드"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "유연한 콘텐츠 필드"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "리피터 필드"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "ACF 프로로 추가 기능 잠금 해제"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "필드 그룹 삭제"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "%1$s에서 %2$s에 생성됨"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "그룹 설정"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "위치 규칙"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"30개 이상의 필드 유형 중에서 선택하십시오. <a href=\"%s\" target=\"_blank\">"
"자세히 알아보세요</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"게시물, 페이지, 사용자 정의 게시물 유형 및 기타 워드프레스 콘텐츠에 대한 새로"
"운 사용자 정의 필드 생성을 시작하십시오."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "첫 번째 필드 추가"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "필드 추가"

#: includes/acf-field-group-functions.php:496 includes/fields.php:385
msgid "Presentation"
msgstr "프레젠테이션"

#: includes/fields.php:384
msgid "Validation"
msgstr "확인"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:383
msgid "General"
msgstr "일반"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "JSON 가져오기"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "JSON으로 내보내기"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:356
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "%s 필드 그룹이 비활성화되었습니다."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:349
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "%s 필드 그룹이 활성화되었습니다."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "비활성화"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "이 항목 비활성화"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "활성화"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "이 항목 활성화"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "필드 그룹을 휴지통으로 이동하시겠습니까?"

#: acf.php:561 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "비활성"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP 엔진"

#: acf.php:619
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields와 Advanced Custom Fields 프로는 동시에 활성화되어서는 "
"안 됩니다. Advanced Custom Fields 프로를 자동으로 비활성화했습니다."

#: acf.php:617
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields와 Advanced Custom Fields 프로는 동시에 활성화되어서는 "
"안 됩니다. Advanced Custom Fields를 자동으로 비활성화했습니다."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s에는 다음 역할 중 하나를 가진 사용자가 있어야 합니다. %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s에는 유효한 사용자 ID가 있어야 합니다."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "잘못된 요청."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s은(는) %2$s 중 하나가 아닙니다."

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s에는 다음 용어 중 하나가 있어야 합니다. %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s은(는) 다음 게시물 유형 중 하나여야 합니다: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s에는 유효한 게시물 ID가 있어야 합니다."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s에는 유효한 첨부 파일 ID가 필요합니다."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "REST API에 표시"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "투명성 활성화"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA 배열"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA 문자열"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "16진수 문자열"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "프로로 업그레이드"

#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "활성"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "‘%s’은(는) 유효한 이매일 주소가 아닙니다"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "색상 값"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "기본 색상 선택하기"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "선명한 색상"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "블록"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "옵션"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "사용자"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "메뉴 항목"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "위젯"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "첨부파일"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:108
#: includes/admin/post-types/admin-taxonomies.php:83
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "택소노미"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "게시물"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "최근 업데이트: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "죄송합니다. 이 게시물은 diff 비교에 사용할 수 없습니다."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "잘못된 필드 그룹 매개변수입니다."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "저장 대기 중"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "저장했어요"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "가져오기"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "변경사항 검토하기"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "위치: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "플러그인에 있음: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "테마에 있음: %s"

#: includes/admin/post-types/admin-field-groups.php:231
msgid "Various"
msgstr "다양한"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "변경사항 동기화하기"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "로딩 차이"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "지역 JSON 변경 검토하기"

#: includes/admin/admin.php:170
msgid "Visit website"
msgstr "웹 사이트 방문하기"

#: includes/admin/admin.php:169
msgid "View details"
msgstr "세부 정보 보기"

#: includes/admin/admin.php:168
msgid "Version %s"
msgstr "버전 %s"

#: includes/admin/admin.php:167
msgid "Information"
msgstr "정보"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">헬프 데스크</a>. 당사 헬프데스크의 지원 전문"
"가가 보다 심도 있는 기술 문제를 지원할 것입니다."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">토론</a>. ACF 세계의 '방법'을 파악하는 데 도"
"움을 줄 수 있는 커뮤니티 포럼에 활발하고 친근한 커뮤니티가 있습니다."

#: includes/admin/admin.php:150
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">문서</a>. 광범위한 문서에는 발생할 수 있는 "
"대부분의 상황에 대한 참조 및 가이드가 포함되어 있습니다."

#: includes/admin/admin.php:147
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"우리는 지원에 열광하며 ACF를 통해 웹 사이트를 최대한 활용하기를 바랍니다. 어"
"려움에 처한 경우 도움을 받을 수 있는 여러 곳이 있습니다."

#: includes/admin/admin.php:144 includes/admin/admin.php:146
msgid "Help & Support"
msgstr "도움말 및 지원"

#: includes/admin/admin.php:135
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "도움이 필요한 경우 도움말 및 지원 탭을 사용하여 연락하십시오."

#: includes/admin/admin.php:132
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"첫 번째 필드 그룹을 만들기 전에 먼저 <a href=\"%s\" target=\"_blank\">시작하"
"기</a> 가이드를 읽고 플러그인의 철학과 모범 사례를 숙지하는 것이 좋습니다."

#: includes/admin/admin.php:130
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields 플러그인은 추가 필드로 워드프레스 편집 화면을 사용자 "
"정의할 수 있는 시각적 양식 빌더와 모든 테마 템플릿 파일에 사용자 정의 필드 값"
"을 표시하는 직관적인 API를 제공합니다."

#: includes/admin/admin.php:127 includes/admin/admin.php:129
msgid "Overview"
msgstr "개요"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "위치 유형 \"%s\"이(가) 이미 등록되어 있습니다."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "\"%s\" 클래스가 존재하지 않습니다."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "논스가 잘못되었습니다."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "필드를 로드하는 중 오류가 발생했습니다."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>오류</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "위젯"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "사용자 역할"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "댓글"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "글 형식"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "메뉴 아이템"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "게시물 상태"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "메뉴"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "메뉴 위치"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "메뉴"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "게시물 택소노미"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "자식 페이지 (부모가 있습니다)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "부모 페이지 (자식이 있습니다)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "최상위 페이지 (부모가 없습니다)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "글 페이지"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "프론트 페이지"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "페이지 유형"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "백엔드 보기"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "프런트 엔드 보기"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "로그인"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "현재 사용자"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "페이지 템플릿"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "등록하기"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "추가하기 / 편집하기"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "사용자 양식"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "페이지 부모"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "최고 관리자"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "현재 사용자 역할"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "기본 템플릿"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "게시물 템플릿"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "게시물 카테고리"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "모든 %s 형식"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "첨부"

#: includes/validation.php:324
msgid "%s value is required"
msgstr "%s 값이 필요합니다."

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "다음과 같은 경우 이 필드를 표시합니다."

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:386
msgid "Conditional Logic"
msgstr "조건부 논리"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "그리고"

#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Local JSON"
msgstr "로컬 JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "클론 필드"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"또한 모든 프리미엄 애드온(%s)이 최신 버전으로 업데이트되었는지 확인하세요."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "이 버전은 데이터베이스 개선을 포함하고 있어 업그래이드가 필요합니다."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "%1$s v%2$s로 업데이트해주셔서 감사합니다!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "데이터베이스 업그래이드가 필요합니다"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "옵션 페이지"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:437
msgid "Gallery"
msgstr "갤러리"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:427
msgid "Flexible Content"
msgstr "유연한 콘텐츠"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:447
msgid "Repeater"
msgstr "리피터"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "모든 도구로 돌아가기"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"편집 화면에 여러 개의 필드 그룹이 나타나는 경우 첫 번째 필드 그룹의 옵션(순"
"서 번호가 가장 낮은 옵션)이 사용됩니다."

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "항목을 <b>선택</b>하여 편집 화면에서 <b>숨깁니다</b>."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "화면에 숨기기"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "트랙백 보내기"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "태그"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "카테고리"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "페이지 속성"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "형식"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "작성자"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "슬러그"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "리비전"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "댓글"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "논의"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "요약글"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "콘텐츠 편집기"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "퍼머링크"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "필드 그룹 목록에 표시됨"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "순서가 낮은 필드 그룹이 먼저 나타납니다."

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "주문번호"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "필드 아래"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "레이블 아래"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "지침 배치"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "레이블 배치"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "측면"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "일반(내용 뒤)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "높음(제목 뒤)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "위치"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "매끄럽게(메타박스 없음)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "표준(WP 메타박스)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "스타일"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "유형"

#: includes/admin/post-types/admin-field-groups.php:87
#: includes/admin/post-types/admin-post-types.php:107
#: includes/admin/post-types/admin-taxonomies.php:106
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "키"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "정렬하기"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "필드 닫기"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "클래스"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "너비"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "래퍼 속성"

#: includes/fields/class-acf-field.php:317
msgid "Required"
msgstr "필수"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "지침"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "필드 유형"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "한 단어, 공백 없음. 밑줄 및 대시 허용"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "필드 명"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "이것은 편집하기 페이지에 보일 이름입니다."

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "필드 레이블"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "지우기"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "필드 삭제"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "이동하기"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "다름 그룹으로 필드 이동하기"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "필드 복제하기"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "필드 편집하기"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "드래그하여 재정렬"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "다음과 같은 경우 이 필드 그룹 표시"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "사용 가능한 업데이트가 없습니다."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"데이터베이스 업그래이드를 완료했습니다. <a href=\"%s\"> 새로운 기능 보기 </a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "업그래이드 작업을 읽기 ..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "업그래이드에 실패했습니다."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "업그래이드를 완료했습니다."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "버전 %s(으)로 자료를 업그래이드하기"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"계속하기 전에 데이터베이스를 백업하는 것이 좋습니다. 지금 업데이트 도구를 실"
"행하기 원하는게 확실한가요?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "업그래이드 할 사이트를 하나 이상 선택하기 바랍니다."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"데이터베이스 업그래이드를 완료했습니다. <a href=\"%s\"> 네트워크 알림판으로 "
"돌아 가기 </a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "사이트가 최신 상태입니다."

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "사이트는 %1$s에서 %2$s(으)로 데이터베이스 업그레이드가 필요합니다."

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "사이트"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "사이트 업그래이드하기"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"다음 사이트는 DB 업그레이드가 필요합니다. 업데이트하려는 항목을 확인한 다음 "
"%s를 클릭하십시오."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "그룹 규칙 추가하기"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"이러한 Advanced Custom Fields를 사용할 편집 화면을 결정하는 일련의 규칙을 만"
"듭니다."

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "규칙"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "복사했습니다"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "클립보드에 복사하기"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"내보낼 항목을 선택한 다음 내보내기 방법을 선택합니다. JSON으로 내보내기 "
"- .json 파일로 내보낸 다음 다른 ACF 설치로 가져올 수 있습니다. PHP를 생성하"
"여 테마에 배치할 수 있는 PHP 코드로 내보냅니다."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "필드 그룹 선택하기"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "선택한 필드 그룹 없음"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "PHP 생성"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "필드 그룹 내보내기"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "가져오기 파일이 비어 있음"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "잘못된 파일 형식"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "파일을 업로드하는 중 오류가 발생했습니다. 다시 시도해 주세요"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"가져오려는 Advanced Custom Fields JSON 파일을 선택합니다. 아래 가져오기 버튼"
"을 클릭하면 ACF가 해당 파일의 항목을 가져옵니다."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "필드 그룹 가져오기"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "동기화하기"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "%s 선택하기"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "복제하기"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "이 항목 복제하기"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "지원"

#: includes/admin/admin.php:379
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "문서화"

#: includes/admin/post-types/admin-field-groups.php:86
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/post-types/admin-taxonomies.php:105
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "설명"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "동기화 가능"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:370
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "%s개의 필드 그룹을 동기화했습니다."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:363
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s 필드 그룹이 복사되었습니다."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "활성 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "사이트 검토하기 & 업그래이드하기"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "데이터베이스 업그래이드하기"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "사용자 정의 필드"

#: includes/admin/post-types/admin-field-group.php:608
msgid "Move Field"
msgstr "필드 이동하기"

#: includes/admin/post-types/admin-field-group.php:601
#: includes/admin/post-types/admin-field-group.php:605
msgid "Please select the destination for this field"
msgstr "이 필드의 대상을 선택하십시오"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:567
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s 필드는 이제 %2$s 필드 그룹에서 찾을 수 있습니다."

#: includes/admin/post-types/admin-field-group.php:564
msgid "Move Complete."
msgstr "이동완료."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "활성화"

#: includes/admin/post-types/admin-field-group.php:275
msgid "Field Keys"
msgstr "필드 키"

#: includes/admin/post-types/admin-field-group.php:179
msgid "Settings"
msgstr "설정"

#: includes/admin/post-types/admin-field-groups.php:88
msgid "Location"
msgstr "위치"

#: includes/admin/post-types/admin-field-group.php:99
msgid "Null"
msgstr "빈값"

#: includes/admin/post-types/admin-field-group.php:96
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "복사하기"

#: includes/admin/post-types/admin-field-group.php:95
msgid "(this field)"
msgstr "(이 필드)"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Checked"
msgstr "체크"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "사용자 필드 이동하기"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "사용 가능한 토글 필드 없음"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "필드 그룹 제목이 필요합니다."

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "변경 사항이 저장될 때까지 이 필드를 이동할 수 없습니다."

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "문자열 \"field_\"는 필드 이름의 시작 부분에 사용할 수 없습니다."

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "필드 그룹 초안을 업데이트했습니다."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "필드 그룹이 예정되어 있습니다."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "필드 그룹이 제출되었습니다."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "필드 그룹이 저장되었습니다."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "필드 그룹이 게시되었습니다."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "필드 그룹이 삭제되었습니다."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "필드 그룹을 업데이트했습니다."

#: includes/admin/admin-tools.php:112
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "도구"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "같지 않음"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "같음"

#: includes/locations.php:104
msgid "Forms"
msgstr "양식"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "페이지"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "게시물"

#: includes/fields.php:329
msgid "Relational"
msgstr "관계형"

#: includes/fields.php:328
msgid "Choice"
msgstr "선택하기"

#: includes/fields.php:326
msgid "Basic"
msgstr "기초"

#: includes/fields.php:277
msgid "Unknown"
msgstr "알려지지 않은"

#: includes/fields.php:277
msgid "Field type does not exist"
msgstr "필드 유형이 존재하지 않습니다"

#: includes/forms/form-front.php:219
msgid "Spam Detected"
msgstr "스팸 감지됨"

#: includes/forms/form-front.php:102
msgid "Post updated"
msgstr "글을 업데이트했습니다"

#: includes/forms/form-front.php:101
msgid "Update"
msgstr "업데이트"

#: includes/forms/form-front.php:62
msgid "Validate Email"
msgstr "이매일 확인하기"

#: includes/fields.php:327 includes/forms/form-front.php:54
msgid "Content"
msgstr "콘텐츠"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:45
msgid "Title"
msgstr "제목"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "필드 그룹 편집하기"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is less than"
msgstr "선택이 미만"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Selection is greater than"
msgstr "선택이 다음보다 큼"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is less than"
msgstr "값이 다음보다 작음"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value is greater than"
msgstr "값이 다음보다 큼"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value contains"
msgstr "값은 다음을 포함합니다."

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value matches pattern"
msgstr "값이 패턴과 일치"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is not equal to"
msgstr "값이 같지 않음"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Value is equal to"
msgstr "값은 다음과 같습니다."

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has no value"
msgstr "값이 없음"

#: includes/admin/post-types/admin-field-group.php:103
msgid "Has any value"
msgstr "값이 있음"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:350
msgid "Cancel"
msgstr "취소하기"

#: includes/assets.php:346
msgid "Are you sure?"
msgstr "확실합니까?"

#. translators: %d is the number of fields that require attention
#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d개의 필드에 주의가 필요합니다."

#: includes/assets.php:368
msgid "1 field requires attention"
msgstr "주의가 필요한 필드 1개"

#: includes/assets.php:367 includes/validation.php:258
#: includes/validation.php:266
msgid "Validation failed"
msgstr "검증에 실패했습니다"

#: includes/assets.php:366
msgid "Validation successful"
msgstr "유효성 검사 성공"

#: includes/media.php:54
msgid "Restricted"
msgstr "제한했습니다"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "세부 정보 접기"

#: includes/media.php:52
msgid "Expand Details"
msgstr "세부정보 확장하기"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "이 게시물에 업로드됨"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "업데이트"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "편집하기"

#: includes/assets.php:360
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "페이지를 벗어나면 변경 한 내용이 손실 됩니다"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "파일 유형은 %s여야 합니다."

#: includes/admin/post-types/admin-field-group.php:97
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "또는"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "파일 크기는 %s를 초과할 수 없습니다."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "파일 크기는 %s 이상이어야 합니다."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "이미지 높이는 %dpx를 초과할 수 없습니다."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "이미지 높이는 %dpx 이상이어야 합니다."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "이미지 너비는 %dpx를 초과할 수 없습니다."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "이미지 너비는 %dpx 이상이어야 합니다."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(제목 없음)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "전체 크기"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "크기가 큰"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "중간"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "썸네일"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:94
msgid "(no label)"
msgstr "(레이블 없음)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "문자 영역의 높이를 설정하기"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "행"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "텍스트 영역"

#: includes/fields/class-acf-field-checkbox.php:434
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "모든 선택 항목을 토글하려면 추가 확인란을 앞에 추가하십시오."

#: includes/fields/class-acf-field-checkbox.php:396
msgid "Save 'custom' values to the field's choices"
msgstr "선택한 필드에 ‘사용자 정의’값 저장하기"

#: includes/fields/class-acf-field-checkbox.php:385
msgid "Allow 'custom' values to be added"
msgstr "추가한 ‘사용자 정의’ 값 허용하기"

#: includes/fields/class-acf-field-checkbox.php:48
msgid "Add new choice"
msgstr "새로운 선택 추가하기"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "모두 토글하기"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "보관소 URLs 허용하기"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "아카이브"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "페이지 링크"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "추가하기"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "이름"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s 추가됨"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s이(가) 이미 존재합니다."

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "사용자가 새 %s을(를) 추가할 수 없습니다."

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "용어 ID"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "용어 객체"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "글 용어에서 값 로드하기"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "용어 로드하기"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "글에 선택한 조건을 연결하기"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "조건 저장하기"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "편집하는 동안 생성할 새로운 조건을 허용하기"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "용어 만들기"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "라디오 버튼"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "단일 값"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "다중 선택"

#: includes/fields/class-acf-field-checkbox.php:35
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "체크박스"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "여러 값"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "이 필드의 모양 선택하기"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "모양"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "보일 할 분류를 선택하기"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "%s 없음"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "값은 %d보다 작거나 같아야 합니다."

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "값은 %d 이상이어야 합니다."

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "값은 숫자여야 합니다."

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "숫자"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "선택한 필드에 ‘다른’ 값 저장하기"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "사용자 정의 값을 허용하는 ‘기타’ 선택 사항 추가하기"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "기타"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "라디오 버튼"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"중지할 이전 아코디언의 끝점을 정의합니다. 이 아코디언은 보이지 않습니다."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "다른 아코디언을 닫지 않고 이 아코디언이 열리도록 허용합니다."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "다중 확장"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "이 아코디언은 페이지 로드시 열린 것으로 보입니다."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "열기"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "아코디언"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "업로드 할 수 있는 파일 제한하기"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "파일 ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "파일 URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "파일 어레이"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "파일 추가하기"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "파일이 선택되지 않았습니다"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "파일 이름"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "파일 업데이트"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "파일 편집하기"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "파일 선택하기"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "파일"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "비밀번호"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "반환할 값 지정하기"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "지연 로드 선택에 AJAX를 사용하시겠습니까?"

#: includes/fields/class-acf-field-checkbox.php:346
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "새로운 줄에 기본 값 입력하기"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "로딩 실패"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "검색하기&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "더 많은 결과 로드 중&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "%d 항목만 선택할 수 있어요"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "항목은 1개만 선택할 수 있습니다"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "%d글자를 지우기 바래요"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "글자 1개를 지워주세요"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "%d 또는 이상의 글자를 입력하시기 바래요"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "글자를 1개 이상 입력하세요"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "일치하는 항목이 없습니다"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d개의 결과가 사용가능하니, 위와 아래 방향키를 이용해 탐색하세요."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "1개 결과를 사용할 수 있습니다. 선택하려면 Enter 키를 누르세요."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "사용자 아이디"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "사용자 객체"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "사용자 배열"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "모든 사용자 역할"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "역할로 필터하기"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "사용자"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "분리 기호"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "색상 선택하기"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "기본"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "정리"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "색상 선택기"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "오후"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "오전"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "완료"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "현재"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "시간대"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "마이크로초"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "밀리초"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "초"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "분"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "시간"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "시간"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "시간 선택하기"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "날짜 시간 선택기"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "끝점"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "왼쪽 정렬"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "상단 정렬"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "놓기"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "탭"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "값은 유효한 URL이어야 합니다."

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "링크 URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "링크 어레이"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "새 창/탭에서 열기"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "링크 선택하기"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "링크"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "이메일"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "단계 크기"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "최대값"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "최소값"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "범위"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:363
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "모두(배열)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "레이블"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "값"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:424
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "수직"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:425
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "수평"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "빨강 : 빨강"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr "더 많은 제어를 위해 다음과 같이 값과 레이블을 모두 지정할 수 있습니다."

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "새 줄에 각 선택 항목을 입력합니다."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:335
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "선택하기"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "버튼 그룹"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Null 값 허용"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "부모"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE는 필드를 클릭할 때까지 초기화되지 않습니다."

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "초기화 지연"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "미디어 업로드 버튼 표시"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "툴바"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "텍스트만"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "비주얼 전용"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "비주얼 및 텍스트"

#: includes/fields/class-acf-field-icon_picker.php:269
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "탭"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "TinyMCE를 초기화하려면 클릭하십시오."

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "텍스트"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "비주얼"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "값은 %d자를 초과할 수 없습니다."

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "제한 없이 비워두세요"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "글자 수 제한"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "입력란 뒤에 표시됩니다"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "뒤에 추가"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "입력란 앞에 표시됩니다"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "앞에 추가"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "입력란 안에 표시됩니다"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "플레이스홀더 텍스트"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "새 게시물을 작성할 때 나타납니다."

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "텍스트"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s에는 %2$s개 이상의 선택 항목이 필요합니다."

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "게시물 ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "글 개체"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "최대 게시물"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "최소 게시물"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "특성 이미지"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "선택한 요소가 각 결과에 표시됩니다."

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "엘리먼트"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "택소노미"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "게시물 유형"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "필터"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "모든 분류"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "분류로 필터하기"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "모든 게시물 유형"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "글 유형으로 필터하기"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "검색하기..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "분류 선택하기"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "글 유형 선택하기"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "검색 결과가 없습니다"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "로딩중"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "최대 값에 도달함( {max} values ​​)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "관계"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "쉼표로 구분된 목록입니다. 모든 유형에 대해 비워 두십시오."

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "허용된 파일 형식"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "최고"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "파일 크기"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "업로드 할 수 있는 이미지 제한하기"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "최저"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "게시물에 업로드됨"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "모두"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "미디어 라이브러리 선택 제한하기"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "라이브러리"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "미리보기 크기"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "이미지 ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "이미지 URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "이미지 배열"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:356
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "프론트 엔드에 반환 값 지정하기"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:355
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "값 반환하기"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "이미지 추가하기"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "선택한 이미지 없음"

#: includes/assets.php:349 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "제거하기"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "편집하기"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "모든 이미지"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "이미지 업데이트"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "이미지 편집하기"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "이미지 선택하기"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "이미지"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "렌더링 대신 보이는 텍스트로 HTML 마크 업을 허용하기"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "HTML 이탈하기"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "서식 없음"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "&lt;br&gt; 자동 추가하기"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "단락 자동 추가하기"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "새 줄이 렌더링되는 방식을 제어합니다."

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "새로운 라인"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:210
msgid "Week Starts On"
msgstr "주간 시작 날짜"

#: includes/fields/class-acf-field-date_picker.php:192
msgid "The format used when saving a value"
msgstr "값을 저장할 때 사용되는 형식"

#: includes/fields/class-acf-field-date_picker.php:191
msgid "Save Format"
msgstr "형식 저장하기"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "주"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "이전"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "다음"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "오늘"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "완료"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "날짜 선택기"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:240
msgid "Width"
msgstr "너비"

#: includes/fields/class-acf-field-oembed.php:237
#: includes/fields/class-acf-field-oembed.php:249
msgid "Embed Size"
msgstr "임베드 크기"

#: includes/fields/class-acf-field-oembed.php:197
msgid "Enter URL"
msgstr "URL 입력"

#: includes/fields/class-acf-field-oembed.php:21
msgid "oEmbed"
msgstr "포함"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "비활성 상태일 때 표시되는 텍스트"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "오프 텍스트"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "활성 상태일 때 표시되는 텍스트"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "텍스트에"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "스타일화된 UI"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "기본값"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "확인란 옆에 텍스트를 표시합니다."

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "메시지"

#: includes/assets.php:348 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "아니요"

#: includes/assets.php:347 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "예"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "참 / 거짓"

#: includes/fields/class-acf-field-group.php:414
msgid "Row"
msgstr "열"

#: includes/fields/class-acf-field-group.php:413
msgid "Table"
msgstr "태이블"

#: includes/admin/post-types/admin-field-group.php:157
#: includes/fields/class-acf-field-group.php:412
msgid "Block"
msgstr "블록"

#: includes/fields/class-acf-field-group.php:407
msgid "Specify the style used to render the selected fields"
msgstr "선택한 필드를 렌더링하는 데 사용하는 스타일 지정하기"

#: includes/fields.php:331 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:418
#: includes/fields/class-acf-field-group.php:406
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "레이아웃"

#: includes/fields/class-acf-field-group.php:390
msgid "Sub Fields"
msgstr "하위 필드"

#: includes/fields/class-acf-field-group.php:21
msgid "Group"
msgstr "그룹"

#: includes/fields/class-acf-field-google-map.php:221
msgid "Customize the map height"
msgstr "지도 높이 맞춤설정"

#: includes/fields/class-acf-field-google-map.php:220
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:252
msgid "Height"
msgstr "키"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Set the initial zoom level"
msgstr "초기화 줌 레벨 설정하기"

#: includes/fields/class-acf-field-google-map.php:208
msgid "Zoom"
msgstr "줌"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center the initial map"
msgstr "초기 맵 중앙에 배치"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:194
msgid "Center"
msgstr "중앙"

#: includes/fields/class-acf-field-google-map.php:153
msgid "Search for address..."
msgstr "주소를 검색하기..."

#: includes/fields/class-acf-field-google-map.php:150
msgid "Find current location"
msgstr "현재 위치 찾기"

#: includes/fields/class-acf-field-google-map.php:149
msgid "Clear location"
msgstr "위치 지우기"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "검색하기"

#: includes/fields/class-acf-field-google-map.php:56
msgid "Sorry, this browser does not support geolocation"
msgstr "죄송합니다. 이 브라우저는 지리적 위치를 지원하지 않습니다."

#: includes/fields/class-acf-field-google-map.php:21
msgid "Google Map"
msgstr "구글지도"

#: includes/fields/class-acf-field-date_picker.php:203
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "템플릿 함수를 통해 반환되는 형식"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:202
#: includes/fields/class-acf-field-date_time_picker.php:190
#: includes/fields/class-acf-field-icon_picker.php:292
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "반환 형식"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "사용자화:"

#: includes/fields/class-acf-field-date_picker.php:173
#: includes/fields/class-acf-field-date_time_picker.php:173
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "게시물을 편집할 때 표시되는 형식"

#: includes/fields/class-acf-field-date_picker.php:172
#: includes/fields/class-acf-field-date_time_picker.php:172
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "표시 형식"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "시간 선택기"

#. translators: counts for inactive field groups
#: acf.php:567
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "비활성 <span class=\"count\">(%s)</span>"

#: acf.php:528
msgid "No Fields found in Trash"
msgstr "휴지통에서 필드를 찾을 수 없습니다."

#: acf.php:527
msgid "No Fields found"
msgstr "필드를 찾을 수 없음"

#: acf.php:526
msgid "Search Fields"
msgstr "필드 검색하기"

#: acf.php:525
msgid "View Field"
msgstr "필드보기"

#: acf.php:524 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "새 필드"

#: acf.php:523
msgid "Edit Field"
msgstr "필드 편집하기"

#: acf.php:522
msgid "Add New Field"
msgstr "새로운 필드 추가하기"

#: acf.php:520
msgid "Field"
msgstr "필드"

#: acf.php:519 includes/admin/post-types/admin-field-group.php:178
#: includes/admin/post-types/admin-field-groups.php:89
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "필드"

#: acf.php:494
msgid "No Field Groups found in Trash"
msgstr "휴지통에서 필드 그룹을 찾을 수 없습니다."

#: acf.php:493
msgid "No Field Groups found"
msgstr "필드 그룹을 찾을 수 없음"

#: acf.php:492
msgid "Search Field Groups"
msgstr "필드 그룹 검색하기"

#: acf.php:491
msgid "View Field Group"
msgstr "필드 그룹 보기"

#: acf.php:490
msgid "New Field Group"
msgstr "새 필드 그룹"

#: acf.php:489
msgid "Edit Field Group"
msgstr "필드 그룹 편집하기"

#: acf.php:488
msgid "Add New Field Group"
msgstr "새 필드 그룹 추가하기"

#: acf.php:487 acf.php:521
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "새로 추가하기"

#: acf.php:486
msgid "Field Group"
msgstr "필드 그룹"

#: acf.php:485 includes/admin/post-types/admin-field-groups.php:52
#: includes/admin/post-types/admin-post-types.php:109
#: includes/admin/post-types/admin-taxonomies.php:108
msgid "Field Groups"
msgstr "필드 그룹"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "강력하고 전문적이며 직관적인 필드로 워드프레스를 사용자 정의하십시오."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:331
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

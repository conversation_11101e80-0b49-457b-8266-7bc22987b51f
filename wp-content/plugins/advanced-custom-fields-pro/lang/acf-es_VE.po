# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-27T14:24:00+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: es_VE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/ajax/class-acf-ajax-upgrade.php:24
msgid "Sorry, you don't have permission to do that."
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:37
msgid "Sorry, you are not allowed to do that."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:27
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: includes/class-acf-site-health.php:643
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:683
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:667
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Yes icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Wordpress-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Wordpress icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Welcome write-blog icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Welcome widgets-menus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Welcome view-site icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Welcome learn-more icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Welcome comments icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Welcome add-page icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Warning icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Visibility icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Video-alt3 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Video-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Video-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Vault icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Upload icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Update icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Unlock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Universal access alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Universal access icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Undo icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Twitter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Trash icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Translation icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Tickets alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Tickets icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Thumbs-up icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Thumbs-down icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Testimonial icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Tagcloud icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Tag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Tablet icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Store icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Sticky icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Star-half icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Star-filled icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Star-empty icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Sos icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Sort icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Smiley icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Smartphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Slides icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Shield-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Shield icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Share-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Share-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Share icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Search icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Screenoptions icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Schedule icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Rss icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Redo icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Randomize icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Products icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Pressthis icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Post-status icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Portfolio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Plus-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Plus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Playlist-video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Playlist-audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Phone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Performance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paperclip icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Palmtree icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "No alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "No icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Networking icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Nametag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Move icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Money icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Minus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Migrate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Microphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Menu icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Megaphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Media video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Media text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Media spreadsheet icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Media interactive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Media document icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Media default icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Media code icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Media audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Media archive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Marker icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Lock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Location-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Location icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "List-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Lightbulb icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Leftright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Layout icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Laptop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Info icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Index-card icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Images-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Images-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Image rotate-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Image rotate-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Image rotate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Image flip-vertical icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Image flip-horizontal icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Image filter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Image crop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Id-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Id icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Hidden icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Heart icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Hammer icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Groups icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Grid-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Googleplus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forms icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Format video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Format status icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Format quote icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Format image icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Format gallery icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Format chat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Format audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Format aside icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Flag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Filter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Feedback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Facebook alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Facebook icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "External icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Exerpt-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Email alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Email icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Unlink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Underline icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Ul icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Textcolor icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Table icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Strikethrough icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Spellcheck icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Rtl icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Removeformatting icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Quote icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Paste word icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Paste text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Paragraph icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Outdent icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Ol icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Kitchensink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Justify icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Italic icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Insertmore icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Indent icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Help icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Expand icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Customchar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Contract icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Code icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Break icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bold icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "alignright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "alignleft icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "aligncenter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Edit icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Download icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Dismiss icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Desktop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Dashboard icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Controls volumeon icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Controls volumeoff icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Controls skipforward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Controls skipback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Controls repeat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Controls play icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Controls pause icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Controls forward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Controls back icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Cloud icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Clock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Clipboard icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Chart pie icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Chart line icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Chart bar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Chart area icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Category icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Cart icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Carrot icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Camera icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Calendar alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Calendar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Businessman icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Building icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Book alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Book icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Backup icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Awards icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Art icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow up-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow up-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow up icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Arrow right-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Arrow right-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Arrow right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Arrow left-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Arrow left-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Arrow left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Arrow down-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Arrow down-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
msgid "Arrow down icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Archive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:407
msgid "Analytics icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Align-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Align-none icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Align-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Align-center icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Album icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Users icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Tools icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Customizer icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Comments icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:387
msgid "Collapse icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Appearance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Generic icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: includes/class-acf-site-health.php:704
msgid "JSON Load Paths"
msgstr ""

#: includes/class-acf-site-health.php:698
msgid "JSON Save Paths"
msgstr ""

#: includes/class-acf-site-health.php:689
msgid "Registered ACF Forms"
msgstr ""

#: includes/class-acf-site-health.php:683
msgid "Shortcode Enabled"
msgstr ""

#: includes/class-acf-site-health.php:675
msgid "Field Settings Tabs Enabled"
msgstr ""

#: includes/class-acf-site-health.php:667
msgid "Field Type Modal Enabled"
msgstr ""

#: includes/class-acf-site-health.php:659
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:650
msgid "Block Preloading Enabled"
msgstr ""

#: includes/class-acf-site-health.php:638
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:633
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:606
msgid "Registered ACF Blocks"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Light"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Standard"
msgstr ""

#: includes/class-acf-site-health.php:599
msgid "REST API Format"
msgstr ""

#: includes/class-acf-site-health.php:591
msgid "Registered Options Pages (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:572
msgid "Registered Options Pages (UI)"
msgstr ""

#: includes/class-acf-site-health.php:542
msgid "Options Pages UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:534
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:522
msgid "Registered Taxonomies (UI)"
msgstr ""

#: includes/class-acf-site-health.php:510
msgid "Registered Post Types (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:498
msgid "Registered Post Types (UI)"
msgstr ""

#: includes/class-acf-site-health.php:485
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: includes/class-acf-site-health.php:478
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:473
msgid "Number of Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:440
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: includes/class-acf-site-health.php:427
msgid "Field Groups Enabled for REST API"
msgstr ""

#: includes/class-acf-site-health.php:415
msgid "Registered Field Groups (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:403
msgid "Registered Field Groups (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:391
msgid "Registered Field Groups (UI)"
msgstr ""

#: includes/class-acf-site-health.php:379
msgid "Active Plugins"
msgstr ""

#: includes/class-acf-site-health.php:353
msgid "Parent Theme"
msgstr ""

#: includes/class-acf-site-health.php:342
msgid "Active Theme"
msgstr ""

#: includes/class-acf-site-health.php:333
msgid "Is Multisite"
msgstr ""

#: includes/class-acf-site-health.php:328
msgid "MySQL Version"
msgstr ""

#: includes/class-acf-site-health.php:323
msgid "WordPress Version"
msgstr ""

#: includes/class-acf-site-health.php:316
msgid "Subscription Expiry Date"
msgstr ""

#: includes/class-acf-site-health.php:308
msgid "License Status"
msgstr ""

#: includes/class-acf-site-health.php:303
msgid "License Type"
msgstr ""

#: includes/class-acf-site-health.php:298
msgid "Licensed URL"
msgstr ""

#: includes/class-acf-site-health.php:292
msgid "License Activated"
msgstr ""

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr ""

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr ""

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr ""

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373 assets/build/js/acf-input.js:11311
#: assets/build/js/acf-input.js:12393
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
#: assets/build/js/acf-input.js:1460 assets/build/js/acf-input.js:1558
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
#: assets/build/js/acf-input.js:1437 assets/build/js/acf-input.js:1534
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
#: assets/build/js/acf-input.js:1412 assets/build/js/acf-input.js:1507
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
#: assets/build/js/acf-input.js:1387 assets/build/js/acf-input.js:1481
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
#: assets/build/js/acf-input.js:1368 assets/build/js/acf-input.js:1461
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
#: assets/build/js/acf-input.js:1349 assets/build/js/acf-input.js:1441
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
#: assets/build/js/acf-input.js:1052 assets/build/js/acf-input.js:1116
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
#: assets/build/js/acf-input.js:1029 assets/build/js/acf-input.js:1092
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
#: assets/build/js/acf-input.js:1003 assets/build/js/acf-input.js:1064
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
#: assets/build/js/acf-input.js:976 assets/build/js/acf-input.js:1035
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
#: assets/build/js/acf-input.js:957 assets/build/js/acf-input.js:1015
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
#: assets/build/js/acf-input.js:938 assets/build/js/acf-input.js:995
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
#: assets/build/js/acf-input.js:915 assets/build/js/acf-input.js:971
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
#: assets/build/js/acf-input.js:892 assets/build/js/acf-input.js:947
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
#: assets/build/js/acf-input.js:865 assets/build/js/acf-input.js:918
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
#: assets/build/js/acf-input.js:838 assets/build/js/acf-input.js:889
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
#: assets/build/js/acf-input.js:819 assets/build/js/acf-input.js:869
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
#: assets/build/js/acf-input.js:800 assets/build/js/acf-input.js:849
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
#: assets/build/js/acf-input.js:1188 assets/build/js/acf-input.js:1259
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
#: assets/build/js/acf-input.js:1165 assets/build/js/acf-input.js:1235
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
#: assets/build/js/acf-input.js:1326 assets/build/js/acf-input.js:1415
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
#: assets/build/js/acf-input.js:1303 assets/build/js/acf-input.js:1389
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
#: assets/build/js/acf-input.js:1276 assets/build/js/acf-input.js:1358
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-input.js:1249 assets/build/js/acf-input.js:1327
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
#: assets/build/js/acf-input.js:1230 assets/build/js/acf-input.js:1305
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
#: assets/build/js/acf-input.js:1211 assets/build/js/acf-input.js:1283
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1139 assets/build/js/acf-input.js:1207
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1113 assets/build/js/acf-input.js:1180
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1094 assets/build/js/acf-input.js:1160
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1140
msgid "Relationship is equal to"
msgstr ""

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:381 includes/api/api-template.php:435
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:247
#: includes/api/api-template.php:939
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr ""

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr ""

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr ""

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr ""

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr ""

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:147
#: assets/build/js/acf-field-group.js:2803
#: assets/build/js/acf-field-group.js:3298
msgid "This Field"
msgstr ""

#: includes/admin/admin.php:352
msgid "ACF PRO"
msgstr ""

#: includes/admin/admin.php:350
msgid "Feedback"
msgstr ""

#: includes/admin/admin.php:348
msgid "Support"
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:323
msgid "is developed and maintained by"
msgstr ""

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr ""

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr ""

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:487
#: includes/fields/class-acf-field-post_object.php:400
#: includes/fields/class-acf-field-select.php:380
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr ""

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr ""

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:454
#: includes/fields/class-acf-field-post_object.php:363
#: includes/fields/class-acf-field-relationship.php:562
msgid "Any post status"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr ""

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr ""

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr ""

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr ""

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:446
#: includes/fields/class-acf-field-post_object.php:355
#: includes/fields/class-acf-field-relationship.php:554
msgid "Filter by Post Status"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr ""

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr ""

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr ""

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr ""

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1298
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1297
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1279
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1278
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1245
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1244
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1223
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1195
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1169
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1134
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1133
msgid "Archive"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1113
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1112
msgid "Pagination"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1095
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1094
msgid "Feed URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1076
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1075
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1056
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1055
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1038
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1029
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-post-type/advanced-settings.php:1198
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1026
#: includes/admin/views/acf-post-type/advanced-settings.php:1036
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:995
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:994
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:963
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:913
msgid "Exclude From Search"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:900
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:899
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:849
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:822
msgid "Menu Icon"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr ""

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr ""

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:146
#: assets/build/js/acf-field-group.js:1159
#: assets/build/js/acf-field-group.js:1383
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1186
#: assets/build/js/acf-field-group.js:2349
#: assets/build/js/acf-field-group.js:1429
#: assets/build/js/acf-field-group.js:2761
msgid "PRO Only"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:46 includes/admin/admin.php:352
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr ""

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr ""

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr ""

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr ""

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:1015
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1701
#: assets/build/js/acf-field-group.js:2032
msgid "Field moved to other group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1443 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:119
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:118
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:423
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr ""

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr ""

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr ""

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr ""

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr ""

#: includes/fields.php:383
msgid "Validation"
msgstr ""

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr ""

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2862
#: assets/build/js/acf-field-group.js:3375
msgid "Move field group to trash?"
msgstr ""

#: acf.php:500 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr ""

#: acf.php:558
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:556
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Hemos detectado una o más llamadas para obtener "
"valores de campo de ACF antes de que ACF se haya iniciado. Esto no es "
"compatible y puede ocasionar datos mal formados o faltantes. <a "
"href=\"%2$s\" target=\"_blank\">Aprende cómo corregirlo</a>."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s debe tener un usuario con el perfil %2$s."
msgstr[1] "%1$s debe tener un usuario con uno de los siguientes perfiles: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s debe tener un ID de usuario válido."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Petición no válida."

#: includes/fields/class-acf-field-select.php:637
msgid "%1$s is not one of %2$s"
msgstr "%1$s no es ninguna de las siguientes %2$s"

#: includes/fields/class-acf-field-post_object.php:649
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s debe tener un término %2$s."
msgstr[1] "%1$s debe tener uno de los siguientes términos: %2$s"

#: includes/fields/class-acf-field-post_object.php:633
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s debe ser del tipo de contenido %2$s."
msgstr[1] "%1$s debe ser de uno de los siguientes tipos de contenido: %2$s"

#: includes/fields/class-acf-field-post_object.php:624
msgid "%1$s must have a valid post ID."
msgstr "%1$s debe tener un ID de entrada válido."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s necesita un ID de adjunto válido."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Mostrar en la API REST"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Activar la transparencia"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Cadena RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Cadena hexadecimal"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Activo"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "«%s» no es una dirección de correo electrónico válida"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valor del color"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Seleccionar el color por defecto"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Vaciar el color"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Bloques"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opciones"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Usuarios"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Elementos del menú"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Adjuntos"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomías"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr "Entradas"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Última actualización: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Parámetro(s) de grupo de campos no válido(s)"

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Esperando el guardado"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Guardado"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importar"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Revisar cambios"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Localizado en: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Localizado en el plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Localizado en el tema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Varios"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sincronizar cambios"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Cargando diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Revisar cambios de JSON local"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Visitar web"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Ver detalles"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Versión %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Información"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Centro de ayuda</a>. Los profesionales de "
"soporte de nuestro centro de ayuda te ayudarán más en profundidad con los "
"retos técnicos."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentación</a>. Nuestra amplia "
"documentación contiene referencias y guías para la mayoría de situaciones en "
"las que puedas encontrarte."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Somos fanáticos del soporte, y queremos que consigas el máximo en tu web con "
"ACF:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Ayuda y soporte"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Por favor, usa la pestaña de ayuda y soporte para contactar si descubres que "
"necesitas ayuda."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Antes de crear tu primer grupo de campos te recomendamos que primero leas "
"nuestra <a href=\"%s\" target=\"_blank\">guía de primeros pasos</a> para "
"familiarizarte con la filosofía y buenas prácticas del plugin."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"El plugin Advanced Custom Fields ofrece un constructor visual con el que "
"personalizar las pantallas de WordPress con campos adicionales, y una API "
"intuitiva parra mostrar valores de campos personalizados en cualquier "
"archivo de plantilla de cualquier tema."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Resumen"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "El tipo de ubicación «%s» ya está registrado."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "La clase «%s» no existe."

#: includes/ajax/class-acf-ajax-query-users.php:28
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce no válido."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Error al cargar el campo."

#: assets/build/js/acf-input.js:3438 assets/build/js/acf-input.js:3507
#: assets/build/js/acf-input.js:3686 assets/build/js/acf-input.js:3760
msgid "Location not found: %s"
msgstr "Ubicación no encontrada: %s"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rol de usuario"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentario"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato de entrada"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Elemento de menú"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estado de entrada"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Ubicaciones de menú"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomía de entrada"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Página hija (tiene superior)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Página superior (con hijos)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Página de nivel superior (sin padres)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Página de entradas"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Página de inicio"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viendo el escritorio"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viendo la web"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Conectado"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Usuario actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Plantilla de página"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registro"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Añadir / Editar"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulario de usuario"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super administrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rol del usuario actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Plantilla predeterminada"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Plantilla de entrada"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoría de entrada"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Todo los formatos de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Adjunto"

#: includes/validation.php:313
msgid "%s value is required"
msgstr "El valor de %s es obligatorio"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostrar este campo si"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "y"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Clonar campo"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Por favor, comprueba también que todas las extensiones premium (%s) estén "
"actualizados a la última versión."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Esta versión contiene mejoras en su base de datos y requiere una "
"actualización."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "¡Gracias por actualizar a %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Es necesario actualizar la base de datos"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Página de opciones"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galería"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Contenido flexible"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Volver a todas las herramientas"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si aparecen múltiples grupos de campos en una pantalla de edición, se "
"utilizarán las opciones del primer grupo (el que tenga el número de orden "
"menor)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecciona</b> los elementos que <b>ocultar</b> de la pantalla de edición."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Ocultar en pantalla"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "Categorías"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atributos de página"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Formato"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisiones"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comentarios"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discusión"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Extracto"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor de contenido"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Enlace permanente"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Mostrado en lista de grupos de campos"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Los grupos de campos con menor orden aparecerán primero"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Número de orden"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Debajo de los campos"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Debajo de las etiquetas"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (después del contenido)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Alta (después del título)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posición"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Directo (sin caja meta)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Estándar (caja meta de WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipo"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Clave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Orden"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Cerrar campo"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "ancho"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atributos del contenedor"

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instrucciones"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola palabra, sin espacios. Se permiten guiones y guiones bajos"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nombre del campo"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Este es el nombre que aparecerá en la página EDITAR"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Etiqueta del campo"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Borrar"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Borrar campo"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Mover"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Mover campo a otro grupo"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Arrastra para reordenar"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2387
#: assets/build/js/acf-field-group.js:2812
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos si"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No hay actualizaciones disponibles."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualización de la base de datos completa. <a href=\"%s\">Ver las "
"novedades</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Leyendo tareas de actualización..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Fallo al actualizar."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Actualización completa"

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Actualizando datos a la versión %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es muy recomendable que hagas una copia de seguridad de tu base de datos "
"antes de continuar. ¿Estás seguro que quieres ejecutar ya la actualización?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Por favor, selecciona al menos un sitio para actualizarlo."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualización de base de datos completa. <a href=\"%s\">Volver al escritorio "
"de red</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "El sitio está actualizado"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "El sitio necesita actualizar la base de datos de %1$s a %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Sitio"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Actualizar los sitios"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Es necesario actualizar la base de datos de los siguientes sitios. Marca los "
"que quieras actualizar y haz clic en %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Añadir grupo de reglas"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un conjunto de reglas para determinar qué pantallas de edición "
"utilizarán estos campos personalizados"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Reglas"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copiar al portapapeles"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Selecciona grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Ningún grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Generar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Archivo de imporación vacío"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Tipo de campo incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Error al subir el archivo. Por favor, inténtalo de nuevo"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importar grupo de campos"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronizar"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplicar este elemento"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr ""

#: includes/admin/admin.php:346
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentación"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descripción"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sincronización disponible"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Revisar sitios y actualizar"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Actualizar base de datos"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Por favor, selecciona el destino para este campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "El campo %1$s ahora se puede encontrar en el grupo de campos %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Movimiento completo."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Activo"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Claves de campo"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Ajustes"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Ubicación"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:1688 assets/build/js/acf-input.js:1850
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1541
#: assets/build/js/acf-field-group.js:1860
msgid "copy"
msgstr "copiar"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:627
#: assets/build/js/acf-field-group.js:782
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:1629 assets/build/js/acf-input.js:1651
#: assets/build/js/acf-input.js:1783 assets/build/js/acf-input.js:1808
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1646
#: assets/build/js/acf-field-group.js:1972
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:653
#: assets/build/js/acf-field-group.js:808
msgid "No toggle fields available"
msgstr "No hay campos de conmutación disponibles"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "El título del grupo de campos es obligatorio"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1635
#: assets/build/js/acf-field-group.js:1958
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo se puede mover hasta que sus cambios se hayan guardado"

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1445
#: assets/build/js/acf-field-group.js:1755
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La cadena \"field_\" no se debe utilizar al comienzo de un nombre de campo"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Borrador del grupo de campos actualizado."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Grupo de campos programado."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Herramientas"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "no es igual a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "es igual a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formularios"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "Página"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "Entrada"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relación"

#: includes/fields.php:327
msgid "Choice"
msgstr "Elección"

#: includes/fields.php:325
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Desconocido"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "El tipo de campo no existe"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "Publicación actualizada"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "Validar correo electrónico"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "Contenido"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "Título"

#: includes/assets.php:376 includes/forms/form-comment.php:140
#: assets/build/js/acf-input.js:8413 assets/build/js/acf-input.js:9185
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1815 assets/build/js/acf-input.js:1990
msgid "Selection is less than"
msgstr "La selección es menor que"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1799 assets/build/js/acf-input.js:1965
msgid "Selection is greater than"
msgstr "La selección es mayor que"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1771 assets/build/js/acf-input.js:1936
msgid "Value is less than"
msgstr "El valor es menor que"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1744 assets/build/js/acf-input.js:1908
msgid "Value is greater than"
msgstr "El valor es mayor que"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:1602 assets/build/js/acf-input.js:1744
msgid "Value contains"
msgstr "El valor contiene"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:1579 assets/build/js/acf-input.js:1713
msgid "Value matches pattern"
msgstr "El valor coincide con el patrón"

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:1560 assets/build/js/acf-input.js:1725
#: assets/build/js/acf-input.js:1693 assets/build/js/acf-input.js:1888
msgid "Value is not equal to"
msgstr "El valor no es igual a"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:1533 assets/build/js/acf-input.js:1669
#: assets/build/js/acf-input.js:1657 assets/build/js/acf-input.js:1828
msgid "Value is equal to"
msgstr "El valor es igual a"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:1514 assets/build/js/acf-input.js:1637
msgid "Has no value"
msgstr "No tiene ningún valor"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:1487 assets/build/js/acf-input.js:1586
msgid "Has any value"
msgstr "No tiene algún valor"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1570 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:350 assets/build/js/acf.js:1744
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "¿Estás seguro?"

#: includes/assets.php:370 assets/build/js/acf-input.js:10481
#: assets/build/js/acf-input.js:11531
msgid "%d fields require attention"
msgstr "%d campos requieren atención"

#: includes/assets.php:369 assets/build/js/acf-input.js:10479
#: assets/build/js/acf-input.js:11529
msgid "1 field requires attention"
msgstr "1 campo requiere atención"

#: includes/assets.php:368 includes/validation.php:247
#: includes/validation.php:255 assets/build/js/acf-input.js:10474
#: assets/build/js/acf-input.js:11524
msgid "Validation failed"
msgstr "Validación fallida"

#: includes/assets.php:367 assets/build/js/acf-input.js:10642
#: assets/build/js/acf-input.js:11702
msgid "Validation successful"
msgstr "Validación correcta"

#: includes/media.php:54 assets/build/js/acf-input.js:8241
#: assets/build/js/acf-input.js:8989
msgid "Restricted"
msgstr "Restringido"

#: includes/media.php:53 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8753
msgid "Collapse Details"
msgstr "Contraer detalles"

#: includes/media.php:52 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8750
msgid "Expand Details"
msgstr "Ampliar detalles"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:7923
#: assets/build/js/acf-input.js:8598
msgid "Uploaded to this post"
msgstr "Subido a esta publicación"

#: includes/media.php:50 assets/build/js/acf-input.js:7962
#: assets/build/js/acf-input.js:8637
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:364 assets/build/js/acf-input.js:10252
#: assets/build/js/acf-input.js:11296
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Los cambios que has realizado se perderán si navegas hacia otra página"

#: includes/api/api-helpers.php:2959
msgid "File type must be %s."
msgstr "El tipo de archivo debe ser %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2956 assets/build/js/acf-field-group.js:781
#: assets/build/js/acf-field-group.js:2427
#: assets/build/js/acf-field-group.js:946
#: assets/build/js/acf-field-group.js:2859
msgid "or"
msgstr "o"

#: includes/api/api-helpers.php:2932
msgid "File size must not exceed %s."
msgstr "El tamaño del archivo no debe ser mayor de %s."

#: includes/api/api-helpers.php:2928
msgid "File size must be at least %s."
msgstr "El tamaño de archivo debe ser al menos %s."

#: includes/api/api-helpers.php:2915
msgid "Image height must not exceed %dpx."
msgstr "La altura de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:2911
msgid "Image height must be at least %dpx."
msgstr "La altura de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:2899
msgid "Image width must not exceed %dpx."
msgstr "El ancho de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:2895
msgid "Image width must be at least %dpx."
msgstr "El ancho de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:1409 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(sin título)"

#: includes/api/api-helpers.php:765
msgid "Full Size"
msgstr "Tamaño completo"

#: includes/api/api-helpers.php:730
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:729
msgid "Medium"
msgstr "Mediano"

#: includes/api/api-helpers.php:728
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1090
#: assets/build/js/acf-field-group.js:1277
msgid "(no label)"
msgstr "(sin etiqueta)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Establece la altura del área de texto"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Filas"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Anteponer una casilla de verificación extra para cambiar todas las opciones"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Guardar los valores «personalizados» a las opciones del campo"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Permite añadir valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Añadir nueva opción"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Invertir todos"

#: includes/fields/class-acf-field-page_link.php:476
msgid "Allow Archives URLs"
msgstr "Permitir las URLs de los archivos"

#: includes/fields/class-acf-field-page_link.php:185
msgid "Archives"
msgstr "Archivo"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Enlace a página"

#: includes/fields/class-acf-field-taxonomy.php:870
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Añadir"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:840
msgid "Name"
msgstr "Nombre"

#: includes/fields/class-acf-field-taxonomy.php:825
msgid "%s added"
msgstr "%s añadido/s"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "%s already exists"
msgstr "%s ya existe"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "User unable to add new %s"
msgstr "El usuario no puede añadir nuevos %s"

#: includes/fields/class-acf-field-taxonomy.php:664
msgid "Term ID"
msgstr "ID de término"

#: includes/fields/class-acf-field-taxonomy.php:663
msgid "Term Object"
msgstr "Objeto de término"

#: includes/fields/class-acf-field-taxonomy.php:648
msgid "Load value from posts terms"
msgstr "Cargar el valor de los términos de la publicación"

#: includes/fields/class-acf-field-taxonomy.php:647
msgid "Load Terms"
msgstr "Cargar términos"

#: includes/fields/class-acf-field-taxonomy.php:637
msgid "Connect selected terms to the post"
msgstr "Conectar los términos seleccionados con la publicación"

#: includes/fields/class-acf-field-taxonomy.php:636
msgid "Save Terms"
msgstr "Guardar términos"

#: includes/fields/class-acf-field-taxonomy.php:626
msgid "Allow new terms to be created whilst editing"
msgstr "Permitir la creación de nuevos términos mientras se edita"

#: includes/fields/class-acf-field-taxonomy.php:625
msgid "Create Terms"
msgstr "Crear términos"

#: includes/fields/class-acf-field-taxonomy.php:684
msgid "Radio Buttons"
msgstr "Botones de radio"

#: includes/fields/class-acf-field-taxonomy.php:683
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:681
msgid "Multi Select"
msgstr "Selección múltiple"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:680
msgid "Checkbox"
msgstr "Casilla de verificación"

#: includes/fields/class-acf-field-taxonomy.php:679
msgid "Multiple Values"
msgstr "Valores múltiples"

#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Select the appearance of this field"
msgstr "Selecciona la apariencia de este campo"

#: includes/fields/class-acf-field-taxonomy.php:673
msgid "Appearance"
msgstr "Apariencia"

#: includes/fields/class-acf-field-taxonomy.php:615
msgid "Select the taxonomy to be displayed"
msgstr "Selecciona la taxonomía a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:579
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "El valor debe ser menor o igual a %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "El valor debe ser mayor o igual a %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "El valor debe ser un número"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Guardar los valores de 'otros' en las opciones del campo"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Añade la opción 'otros' para permitir valores personalizados"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "Otros"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Botón de radio"

#: includes/fields/class-acf-field-accordion.php:103
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define un punto final para que el acordeón anterior se detenga. Este "
"acordeón no será visible."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Allow this accordion to open without closing others."
msgstr "Permita que este acordeón se abra sin cerrar otros."

#: includes/fields/class-acf-field-accordion.php:91
msgid "Multi-Expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:81
msgid "Display this accordion as open on page load."
msgstr "Muestra este acordeón como abierto en la carga de la página."

#: includes/fields/class-acf-field-accordion.php:80
msgid "Open"
msgstr "Abrir"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordeón"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restringen los archivos que se pueden subir"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID del archivo"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL del archivo"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array del archivo"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Añadir archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Ningún archivo seleccionado"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nombre del archivo"

#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:3162 assets/build/js/acf-input.js:3385
msgid "Update File"
msgstr "Actualizar archivo"

#: includes/fields/class-acf-field-file.php:56
#: assets/build/js/acf-input.js:3161 assets/build/js/acf-input.js:3384
msgid "Edit File"
msgstr "Editar archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
#: assets/build/js/acf-input.js:3135 assets/build/js/acf-input.js:3357
msgid "Select File"
msgstr "Seleccionar archivo"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Archivo"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Contraseña"

#: includes/fields/class-acf-field-select.php:365
msgid "Specify the value returned"
msgstr "Especifica el valor devuelto"

#: includes/fields/class-acf-field-select.php:433
msgid "Use AJAX to lazy load choices?"
msgstr "¿Usar AJAX para hacer cargar las opciones de forma asíncrona?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:354
msgid "Enter each default value on a new line"
msgstr "Añade cada valor en una nueva línea"

#: includes/fields/class-acf-field-select.php:229 includes/media.php:48
#: assets/build/js/acf-input.js:7821 assets/build/js/acf-input.js:8483
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Error al cargar"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Buscando&hellip;"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Cargando más resultados&hellip;"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Solo puedes seleccionar %d elementos"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Solo puedes seleccionar 1 elemento"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor, borra %d caracteres"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor, borra 1 carácter"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor, introduce %d o más caracteres"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor, introduce 1 o más caracteres"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados disponibles, utiliza las flechas arriba y abajo para navegar "
"por los resultados."

#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hay un resultado disponible, pulsa enter para seleccionarlo."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:685
msgctxt "noun"
msgid "Select"
msgstr "Selección"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID del usuario"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Grupo de objetos"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Grupo de usuarios"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Todos los roles de usuario"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr ""

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Usuario"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Seleccionar color"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "Por defecto"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Vaciar"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hecho"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ahora"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zona horaria"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisegundo"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Elegir hora"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Selector de fecha y hora"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Endpoint"
msgstr "Variable"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:109
msgid "Left aligned"
msgstr "Alineada a la izquierda"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:108
msgid "Top aligned"
msgstr "Alineada arriba"

#: includes/fields/class-acf-field-tab.php:104
msgid "Placement"
msgstr "Ubicación"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Pestaña"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "El valor debe ser una URL válida"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL del enlace"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Array de enlaces"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Abrir en una nueva ventana/pestaña"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Elige el enlace"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Enlace"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Correo electrónico"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Tamaño de paso"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Rango"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:372
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:371
msgid "Label"
msgstr "Etiqueta"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:370
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "red : Red"
msgstr "rojo : Rojo"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para más control, puedes especificar tanto un valor como una etiqueta, así:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "Enter each choice on a new line."
msgstr "Añade cada opción en una nueva línea."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:342
msgid "Choices"
msgstr "Opciones"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Grupo de botones"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:401
#: includes/fields/class-acf-field-taxonomy.php:694
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:262
#: includes/fields/class-acf-field-post_object.php:243
#: includes/fields/class-acf-field-taxonomy.php:858
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE no se inicializará hasta que se haga clic en el campo"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barra de herramientas"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Sólo texto"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Sólo visual"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visual y Texto"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Pestañas"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Haz clic para iniciar TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "El valor no debe exceder los %d caracteres"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Déjalo en blanco para ilimitado"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Límite de caracteres"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Aparece después del campo"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Anexar"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Aparece antes del campo"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Anteponer"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Aparece en el campo"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Marcador de posición"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Aparece cuando se está creando una nueva entrada"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-relationship.php:742
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s necesita al menos %2$s selección"
msgstr[1] "%1$s necesita al menos %2$s selecciones"

#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:605
msgid "Post ID"
msgstr "ID de publicación"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:390
#: includes/fields/class-acf-field-relationship.php:604
msgid "Post Object"
msgstr "Objeto de publicación"

#: includes/fields/class-acf-field-relationship.php:637
msgid "Maximum Posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:627
msgid "Minimum Posts"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:662
msgid "Featured Image"
msgstr "Imagen destacada"

#: includes/fields/class-acf-field-relationship.php:658
msgid "Selected elements will be displayed in each result"
msgstr "Los elementos seleccionados se mostrarán en cada resultado"

#: includes/fields/class-acf-field-relationship.php:657
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:591
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:614
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomía"

#: includes/fields/class-acf-field-relationship.php:590
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:584
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:577
msgid "All taxonomies"
msgstr "Todas las taxonomías"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:370
#: includes/fields/class-acf-field-relationship.php:569
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomía"

#: includes/fields/class-acf-field-page_link.php:439
#: includes/fields/class-acf-field-post_object.php:348
#: includes/fields/class-acf-field-relationship.php:547
msgid "All post types"
msgstr "Todos los tipos de contenido"

#: includes/fields/class-acf-field-page_link.php:431
#: includes/fields/class-acf-field-post_object.php:340
#: includes/fields/class-acf-field-relationship.php:539
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:439
msgid "Search..."
msgstr "Buscar..."

#: includes/fields/class-acf-field-relationship.php:369
msgid "Select taxonomy"
msgstr "Selecciona taxonomía"

#: includes/fields/class-acf-field-relationship.php:361
msgid "Select post type"
msgstr "Seleccionar tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:78
#: assets/build/js/acf-input.js:4937 assets/build/js/acf-input.js:5402
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#: includes/fields/class-acf-field-relationship.php:77
#: assets/build/js/acf-input.js:4920 assets/build/js/acf-input.js:5381
msgid "Loading"
msgstr "Cargando"

#: includes/fields/class-acf-field-relationship.php:76
#: assets/build/js/acf-input.js:4824 assets/build/js/acf-input.js:5271
msgid "Maximum values reached ( {max} values )"
msgstr "Valores máximos alcanzados ( {max} valores )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relación"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separada por comas. Déjalo en blanco para todos los tipos"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr ""

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Tamaño del archivo"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restringir que las imágenes se pueden subir"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Subidos al contenido"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limitar las opciones de la biblioteca de medios"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Tamaño de vista previa"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID de imagen"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL de imagen"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Array de imágenes"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Especificar el valor devuelto en la web"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:658
msgid "Return Value"
msgstr "Valor de retorno"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Añadir imagen"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "No hay ninguna imagen seleccionada"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124 assets/build/js/acf.js:1569
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Quitar"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Editar"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
#: assets/build/js/acf-input.js:7868 assets/build/js/acf-input.js:8537
msgid "All images"
msgstr "Todas las imágenes"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:4181 assets/build/js/acf-input.js:4579
msgid "Update Image"
msgstr "Actualizar imagen"

#: includes/fields/class-acf-field-image.php:61
#: assets/build/js/acf-input.js:4180 assets/build/js/acf-input.js:4578
msgid "Edit Image"
msgstr "Editar imagen"

#: includes/fields/class-acf-field-image.php:60
#: assets/build/js/acf-input.js:4016 assets/build/js/acf-input.js:4156
#: assets/build/js/acf-input.js:4404 assets/build/js/acf-input.js:4553
msgid "Select Image"
msgstr "Seleccionar imagen"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Imagen"

#: includes/fields/class-acf-field-message.php:110
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permitir que el maquetado HTML se muestre como texto visible en vez de "
"interpretarlo"

#: includes/fields/class-acf-field-message.php:109
msgid "Escape HTML"
msgstr "Escapar HTML"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Sin formato"

#: includes/fields/class-acf-field-message.php:100
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Añadir &lt;br&gt; automáticamente"

#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Añadir párrafos automáticamente"

#: includes/fields/class-acf-field-message.php:95
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controla cómo se muestran los saltos de línea"

#: includes/fields/class-acf-field-message.php:94
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nuevas líneas"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "La semana comienza el"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "El formato utilizado cuando se guarda un valor"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Guardar formato"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Siguiente"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoy"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Listo"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Selector de fecha"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Ancho"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Tamaño de incrustación"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Introduce la URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Texto mostrado cuando está inactivo"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Texto desactivado"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texto mostrado cuando está activo"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texto activado"

#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:353
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valor por defecto"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Muestra el texto junto a la casilla de verificación"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:84
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Mensaje"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: assets/build/js/acf.js:1746 assets/build/js/acf.js:1861
msgid "No"
msgstr "No"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: assets/build/js/acf.js:1745 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Verdadero / Falso"

#: includes/fields/class-acf-field-group.php:412
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-group.php:411
msgid "Table"
msgstr "Tabla"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:410
msgid "Block"
msgstr "Bloque"

#: includes/fields/class-acf-field-group.php:405
msgid "Specify the style used to render the selected fields"
msgstr ""
"Especifica el estilo utilizado para representar los campos seleccionados"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:404
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Estructura"

#: includes/fields/class-acf-field-group.php:388
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Personalizar la altura del mapa"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Establecer el nivel inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centrar inicialmente el mapa"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Buscar dirección..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Encontrar ubicación actual"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Borrar ubicación"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:589
msgid "Search"
msgstr "Buscar"

#: includes/fields/class-acf-field-google-map.php:57
#: assets/build/js/acf-input.js:3528 assets/build/js/acf-input.js:3786
msgid "Sorry, this browser does not support geolocation"
msgstr "Lo siento, este navegador no es compatible con la geolocalización"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "El formato devuelto por de las funciones del tema"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:385
#: includes/fields/class-acf-field-relationship.php:599
#: includes/fields/class-acf-field-select.php:364
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Formato de retorno"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "El formato mostrado cuando se edita una publicación"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Formato de visualización"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Selector de hora"

#. translators: counts for inactive field groups
#: acf.php:506
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: acf.php:467
msgid "No Fields found in Trash"
msgstr "No se han encontrado campos en la papelera"

#: acf.php:466
msgid "No Fields found"
msgstr "No se han encontrado campos"

#: acf.php:465
msgid "Search Fields"
msgstr "Buscar campos"

#: acf.php:464
msgid "View Field"
msgstr "Ver campo"

#: acf.php:463 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nuevo campo"

#: acf.php:462
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:461
msgid "Add New Field"
msgstr "Añadir nuevo campo"

#: acf.php:459
msgid "Field"
msgstr "Campo"

#: acf.php:458 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Campos"

#: acf.php:433
msgid "No Field Groups found in Trash"
msgstr "No se han encontrado grupos de campos en la papelera"

#: acf.php:432
msgid "No Field Groups found"
msgstr "No se han encontrado grupos de campos"

#: acf.php:431
msgid "Search Field Groups"
msgstr "Buscar grupo de campos"

#: acf.php:430
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:429
msgid "New Field Group"
msgstr "Nuevo grupo de campos"

#: acf.php:428
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:427
msgid "Add New Field Group"
msgstr "Añadir nuevo grupo de campos"

#: acf.php:426 acf.php:460
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Añadir nuevo"

#: acf.php:425
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:424 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grupos de campos"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personaliza WordPress con campos potentes, profesionales e intuitivos."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

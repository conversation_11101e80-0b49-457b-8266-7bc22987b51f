# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-12T11:23:47+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-date_picker.php:235
#: includes/fields/class-acf-field-date_time_picker.php:222
msgid "Use the current date as the default value for this field."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:234
#: includes/fields/class-acf-field-date_time_picker.php:221
msgid "Default to the current date"
msgstr ""

#: includes/assets.php:363
msgid "Toggle panel"
msgstr ""

#. translators: %1$s - Plugin name, %2$s URL to documentation
#: includes/admin/admin.php:302
msgid ""
"%1$s We have detected that this website is configured to use v3 of the "
"Select2 jQuery library, which has been deprecated in favor of v4 and will be "
"removed in a future version of ACF. <a href=\"%2$s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:528
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr "Numero di gruppi di campi con posizionamento in blocchi e altri"

#: src/Site_Health/Site_Health.php:523
msgid "Number of Field Groups with Multiple Block Locations"
msgstr "Numero di gruppi di campi con posizionamento in blocchi multipli"

#: src/Site_Health/Site_Health.php:518
msgid "Number of Field Groups with a Single Block Location"
msgstr "Numero di gruppi di campi con posizionamento in blocco singolo"

#: src/Site_Health/Site_Health.php:487
msgid "All Location Rules"
msgstr "Tutte le regole di posizionamento"

#: includes/validation.php:145
msgid "Learn more"
msgstr "Scopri di più"

#: includes/validation.php:134
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF non ha potuto effettuare la validazione perché è fallita la verifica del "
"nonce fornito."

#: includes/validation.php:132
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"ACF non ha potuto effettuare la validazione perché non è stato ricevuto "
"nessun nonce dal server."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:348
msgid "are developed and maintained by"
msgstr "sono sviluppati e mantenuti da"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Aggiorna fonte"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""
"Per impostazione predefinita, solo gli utenti amministratori possono "
"modificare questa impostazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""
"Per impostazione predefinita, solo gli utenti super-amministratori possono "
"modificare questa impostazione."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Chiudi e aggiungi il campo"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Il nome di una funzione PHP da chiamare per gestire il contenuto di un meta "
"box nella tua tassonomia. Per motivi di sicurezza, questa callback sarà "
"eseguita in un contesto speciale senza accesso a nessuna superglobale come "
"$_POST o $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Il nome di una funzione PHP da chiamare durante la preparazione delle meta "
"box per la schermata di modifica. Per motivi di sicurezza, questa callback "
"sarà eseguita in un contesto speciale senza accesso a nessuna superglobale "
"come $_POST o $_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:364
msgid "Allow Access to Value in Editor UI"
msgstr "Permetti l'accesso al valore nell'interfaccia utente dell'editor"

#: includes/fields/class-acf-field.php:346
msgid "Learn more."
msgstr "Approfondisci."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:345
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Permetti agli editori del contenuto di accedere e visualizzare il valore del "
"campo nell'interfaccia utente dell'editor utilizzando blocchi con "
"associazioni o lo shortcode di ACF. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"Il tipo di campo di ACF richiesto non supporta output nei blocchi con "
"associazioni o nello shortcode di ACF."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Il campo di ACF richiesto non può essere un output in associazioni o nello "
"shortcode di ACF."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Il tipo di campo di ACF richiesto non supporta output in associazioni o "
"nello shortcode di ACF."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""
"[Lo shortcode di ACF non può visualizzare campi da articoli non pubblici]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[Lo shortcode di ACF è disabilitato su questo sito]"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Businessman Icon"
msgstr "Icona uomo d'affari"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Forums Icon"
msgstr "Icona forum"

#: includes/fields/class-acf-field-icon_picker.php:754
msgid "YouTube Icon"
msgstr "Icona YouTube"

#: includes/fields/class-acf-field-icon_picker.php:753
msgid "Yes (alt) Icon"
msgstr "Icona sì (alt)"

#: includes/fields/class-acf-field-icon_picker.php:751
msgid "Xing Icon"
msgstr "Icona Xing"

#: includes/fields/class-acf-field-icon_picker.php:750
msgid "WordPress (alt) Icon"
msgstr "Icona WordPress (alt)"

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "WhatsApp Icon"
msgstr "Icona WhatsApp"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "Write Blog Icon"
msgstr "Icona scrivi blog"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Widgets Menus Icon"
msgstr "Icona widget menu"

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "View Site Icon"
msgstr "Icona visualizza sito"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Learn More Icon"
msgstr "Icona approfondisci"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "Add Page Icon"
msgstr "Icona aggiungi pagina"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Video (alt3) Icon"
msgstr "Icona video (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "Video (alt2) Icon"
msgstr "Icona video (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Video (alt) Icon"
msgstr "Icona video (alt)"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Update (alt) Icon"
msgstr "Icona aggiorna (alt)"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Universal Access (alt) Icon"
msgstr "Icona accesso universale (alt)"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Twitter (alt) Icon"
msgstr "Icona Twitter (alt)"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Twitch Icon"
msgstr "Icona Twitch"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Tide Icon"
msgstr "Icona marea"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Tickets (alt) Icon"
msgstr "Icona biglietti (alt)"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Text Page Icon"
msgstr "Icona pagina di testo"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Table Row Delete Icon"
msgstr "Icona elimina riga tabella"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Table Row Before Icon"
msgstr "Icona aggiungi riga tabella sopra"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Table Row After Icon"
msgstr "Icona aggiungi riga tabella sotto"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Table Col Delete Icon"
msgstr "Icona elimina colonna tabella"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Table Col Before Icon"
msgstr "Icona aggiungi colonna tabella prima"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Table Col After Icon"
msgstr "Icona aggiungi colonna tabella dopo"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Superhero (alt) Icon"
msgstr "Icona supereroe (alt)"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Superhero Icon"
msgstr "Icona supereroe"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Spotify Icon"
msgstr "Icona Spotify"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Shortcode Icon"
msgstr "Icona shortcode"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Shield (alt) Icon"
msgstr "Icona scudo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Share (alt2) Icon"
msgstr "Icona condivisione (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Share (alt) Icon"
msgstr "Icona condivisione (alt)"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Saved Icon"
msgstr "Icona salvato"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "RSS Icon"
msgstr "Icona RSS"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "REST API Icon"
msgstr "Icona REST API"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Remove Icon"
msgstr "Icona rimuovi"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Reddit Icon"
msgstr "Icona Reddit"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Privacy Icon"
msgstr "Icona privacy"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Printer Icon"
msgstr "Icona stampante"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Podio Icon"
msgstr "Icona podio"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Plus (alt2) Icon"
msgstr "Icona più (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Plus (alt) Icon"
msgstr "Icona più (alt)"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Plugins Checked Icon"
msgstr "Icona plugin con spunta"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Pinterest Icon"
msgstr "Icona Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Pets Icon"
msgstr "Icona zampa"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "PDF Icon"
msgstr "Icona PDF"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Palm Tree Icon"
msgstr "Icona palma"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Open Folder Icon"
msgstr "Icona apri cartella"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "No (alt) Icon"
msgstr "Icona no (alt)"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Money (alt) Icon"
msgstr "Icona denaro (alt)"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Menu (alt3) Icon"
msgstr "Icona menu (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Menu (alt2) Icon"
msgstr "Icona menu (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Menu (alt) Icon"
msgstr "Icona menu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Spreadsheet Icon"
msgstr "Icona foglio di calcolo"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Interactive Icon"
msgstr "Icona interattiva"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Document Icon"
msgstr "Icona documento"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Default Icon"
msgstr "Icona predefinito"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Location (alt) Icon"
msgstr "Icona luogo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "LinkedIn Icon"
msgstr "Icona LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Instagram Icon"
msgstr "Icona Instagram"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Insert Before Icon"
msgstr "Icona inserisci prima"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Insert After Icon"
msgstr "Icona inserisci dopo"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Insert Icon"
msgstr "Icona inserisci"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Info Outline Icon"
msgstr "Icona informazioni contornata"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Images (alt2) Icon"
msgstr "Icona immagini (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Images (alt) Icon"
msgstr "Icona immagini (alt)"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Rotate Right Icon"
msgstr "Icona ruota a destra"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Rotate Left Icon"
msgstr "Icona ruota a sinistra"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Rotate Icon"
msgstr "Icona ruota"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Flip Vertical Icon"
msgstr "Icona inverti verticalmente"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Flip Horizontal Icon"
msgstr "Icona inverti orizzontalmente"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Crop Icon"
msgstr "Icona ritaglio"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "ID (alt) Icon"
msgstr "Icona ID (alt)"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "HTML Icon"
msgstr "Icona HTML"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Hourglass Icon"
msgstr "Icona clessidra"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Heading Icon"
msgstr "Icona titolo"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Google Icon"
msgstr "Icona Google"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Games Icon"
msgstr "Icona giochI"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Fullscreen Exit (alt) Icon"
msgstr "Icona esci da schermo intero (alt)"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Fullscreen (alt) Icon"
msgstr "Icona schermo intero (alt)"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Status Icon"
msgstr "Icona stato"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Image Icon"
msgstr "Icona immagine"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Gallery Icon"
msgstr "Icona galleria"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Chat Icon"
msgstr "Icona chat"

#: includes/fields/class-acf-field-icon_picker.php:585
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Audio Icon"
msgstr "Icona audio"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Aside Icon"
msgstr "Icona nota"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Food Icon"
msgstr "Icona cibo"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Exit Icon"
msgstr "Icona uscita"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Excerpt View Icon"
msgstr "Icona visualizzazione riassunto"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Embed Video Icon"
msgstr "Icona video incorporato"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Embed Post Icon"
msgstr "Icona articolo incorporato"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Embed Photo Icon"
msgstr "Icona foto incorporata"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Embed Generic Icon"
msgstr "Icona oggetto generico incorporato"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Embed Audio Icon"
msgstr "Icona audio incorporato"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Email (alt2) Icon"
msgstr "Icona email (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Ellipsis Icon"
msgstr "Icona ellisse"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Unordered List Icon"
msgstr "Icona lista non ordinata"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "RTL Icon"
msgstr "Icona RTL"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Ordered List RTL Icon"
msgstr "Icona lista ordinata RTL"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Ordered List Icon"
msgstr "Icona lista ordinata"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "LTR Icon"
msgstr "Icona LTR"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Custom Character Icon"
msgstr "Icona carattere personalizzato"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Edit Page Icon"
msgstr "Icona modifica pagina"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Edit Large Icon"
msgstr "Icona modifica grande"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Drumstick Icon"
msgstr "Icona coscia di pollo"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Database View Icon"
msgstr "Icona visualizzazione database"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Database Remove Icon"
msgstr "Icona rimuovi database"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Database Import Icon"
msgstr "Icona importa database"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Database Export Icon"
msgstr "Icona esporta database"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Database Add Icon"
msgstr "Icona aggiungi database"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Database Icon"
msgstr "Icona database"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Cover Image Icon"
msgstr "Icona immagine di copertina"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Volume On Icon"
msgstr "Icona volume attivato"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Volume Off Icon"
msgstr "Icona volume disattivato"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Skip Forward Icon"
msgstr "Icona salta in avanti"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Skip Back Icon"
msgstr "Icona salta indietro"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Repeat Icon"
msgstr "Icona ripeti"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Play Icon"
msgstr "Icona play"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Pause Icon"
msgstr "Icona pausa"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Forward Icon"
msgstr "Icona avanti"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Back Icon"
msgstr "Icona indietro"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Columns Icon"
msgstr "Icona colonne"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Color Picker Icon"
msgstr "Icona selettore colore"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Coffee Icon"
msgstr "Icona caffè"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Code Standards Icon"
msgstr "Icona standard di codice"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Cloud Upload Icon"
msgstr "Icona caricamento cloud"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Cloud Saved Icon"
msgstr "Icona salvataggio cloud"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Car Icon"
msgstr "Icona macchina"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Camera (alt) Icon"
msgstr "Icona fotocamera (alt)"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Calculator Icon"
msgstr "Icona calcolatrice"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Button Icon"
msgstr "Icona pulsante"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Businessperson Icon"
msgstr "Icona persona d'affari"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Tracking Icon"
msgstr "Icona tracciamento"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Topics Icon"
msgstr "Icona argomenti"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Replies Icon"
msgstr "Icona risposte"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "PM Icon"
msgstr "Icona PM"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Friends Icon"
msgstr "Icona amici"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Community Icon"
msgstr "Icona community"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "BuddyPress Icon"
msgstr "Icona BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "bbPress Icon"
msgstr "icona bbPress"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Activity Icon"
msgstr "Icona attività"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Book (alt) Icon"
msgstr "Icona libro (alt)"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Block Default Icon"
msgstr "Icona blocco predefinito"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Bell Icon"
msgstr "Icona campana"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Beer Icon"
msgstr "Icona birra"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Bank Icon"
msgstr "Icona banca"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Arrow Up (alt2) Icon"
msgstr "Icona freccia su (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Arrow Up (alt) Icon"
msgstr "Icona freccia su (alt)"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Arrow Right (alt2) Icon"
msgstr "Icona freccia destra (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Arrow Right (alt) Icon"
msgstr "Icona freccia destra (alt)"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Arrow Left (alt2) Icon"
msgstr "Icona freccia sinistra (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Left (alt) Icon"
msgstr "Icona freccia sinistra (alt)"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Down (alt2) Icon"
msgstr "Icona giù (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Down (alt) Icon"
msgstr "Icona giù (alt)"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Amazon Icon"
msgstr "Icona Amazon"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Align Wide Icon"
msgstr "Icona allineamento ampio"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Align Pull Right Icon"
msgstr "Icona allineamento a destra"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Align Pull Left Icon"
msgstr "Icona allineamento a sinistra"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Align Full Width Icon"
msgstr "Icona allineamento larghezza piena"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Airplane Icon"
msgstr "Icona aeroplano"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Site (alt3) Icon"
msgstr "Icona sito (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Site (alt2) Icon"
msgstr "Icona sito (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Site (alt) Icon"
msgstr "Icona sito (alt)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Effettua l'upgrade ad ACF PRO per creare pagine opzioni con pochi clic."

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Argomenti della richiesta non validi."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Non hai i permessi per farlo."

#: src/Site_Health/Site_Health.php:726
msgid "Blocks Using Post Meta"
msgstr "Blocchi che utilizzano meta dell'articolo"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Logo di ACF PRO"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Logo di ACF PRO"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:820
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s richiede un ID dell'allegato valido quando il tipo è impostato come "
"media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:804
msgid "%s is a required property of acf."
msgstr "%s è una proprietà necessaria di acf."

#: includes/fields/class-acf-field-icon_picker.php:780
msgid "The value of icon to save."
msgstr "Il valore dell'icona da salvare."

#: includes/fields/class-acf-field-icon_picker.php:774
msgid "The type of icon to save."
msgstr "Il tipo di icona da salvare."

#: includes/fields/class-acf-field-icon_picker.php:752
msgid "Yes Icon"
msgstr "Icona sì"

#: includes/fields/class-acf-field-icon_picker.php:749
msgid "WordPress Icon"
msgstr "Icona WordPress"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "Warning Icon"
msgstr "Icona avviso"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Visibility Icon"
msgstr "Icona visibilità"

#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Vault Icon"
msgstr "Icona camera blindata"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Upload Icon"
msgstr "Icona caricamento"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Update Icon"
msgstr "Icona aggiornamento"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Unlock Icon"
msgstr "Icona sbloccare"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Universal Access Icon"
msgstr "Icona accesso universale"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Undo Icon"
msgstr "Icona annullare"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Twitter Icon"
msgstr "Icona Twitter"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Trash Icon"
msgstr "Icona cestino"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Translation Icon"
msgstr "Icona traduzione"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Tickets Icon"
msgstr "Icona biglietti"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Thumbs Up Icon"
msgstr "Icona pollice in su"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Thumbs Down Icon"
msgstr "Icona pollice in giù"

#: includes/fields/class-acf-field-icon_picker.php:640
#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Text Icon"
msgstr "Icona testo"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Testimonial Icon"
msgstr "Icona testimonial"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tagcloud Icon"
msgstr "Icona nuvola di tag"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tag Icon"
msgstr "Icona tag"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Tablet Icon"
msgstr "Icona tablet"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Store Icon"
msgstr "Icona negozio"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Sticky Icon"
msgstr "Icona in evidenza"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Star Half Icon"
msgstr "Icona mezza stella"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Star Filled Icon"
msgstr "Icona stella piena"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Star Empty Icon"
msgstr "Icona stella vuota"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Sos Icon"
msgstr "Icona sos"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Sort Icon"
msgstr "Icona ordina"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Smiley Icon"
msgstr "Icona smiley"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Smartphone Icon"
msgstr "Icona smartphone"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Slides Icon"
msgstr "Icona slide"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Shield Icon"
msgstr "Icona scudo"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Share Icon"
msgstr "Icona condividi"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Search Icon"
msgstr "Icona ricerca"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Screen Options Icon"
msgstr "Icona opzioni schermo"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Schedule Icon"
msgstr "Icona programma"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Redo Icon"
msgstr "Icona ripeti"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Randomize Icon"
msgstr "Icona ordinamento casuale"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Products Icon"
msgstr "Icona prodotti"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Pressthis Icon"
msgstr "Icona pressthis"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Post Status Icon"
msgstr "Icona stato del post"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Portfolio Icon"
msgstr "Icona portfolio"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Plus Icon"
msgstr "Icona più"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Playlist Video Icon"
msgstr "Icona playlist video"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Playlist Audio Icon"
msgstr "Icona playlist audio"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Phone Icon"
msgstr "Icona telefono"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Performance Icon"
msgstr "Icona performance"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Paperclip Icon"
msgstr "Icona graffetta"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "No Icon"
msgstr "Nessuna icona"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Networking Icon"
msgstr "Icona rete"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Nametag Icon"
msgstr "Icona etichetta"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Move Icon"
msgstr "Icona sposta"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Money Icon"
msgstr "Icona denaro"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Minus Icon"
msgstr "Icona meno"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Migrate Icon"
msgstr "Icona migra"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Microphone Icon"
msgstr "Icona microfono"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Megaphone Icon"
msgstr "Icona megafono"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Marker Icon"
msgstr "Icona indicatore"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Lock Icon"
msgstr "Icona lucchetto"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Location Icon"
msgstr "Icona luogo"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "List View Icon"
msgstr "Icona vista elenco"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Lightbulb Icon"
msgstr "Icona lampadina"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Left Right Icon"
msgstr "Icona sinistra destra"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Layout Icon"
msgstr "Icona layout"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Laptop Icon"
msgstr "Icona laptop"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Info Icon"
msgstr "Icona informazione"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Index Card Icon"
msgstr "Icona carta indice"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "ID Icon"
msgstr "Icona ID"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Hidden Icon"
msgstr "Icona nascosto"

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Heart Icon"
msgstr "Icona cuore"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Hammer Icon"
msgstr "Icona martello"

#: includes/fields/class-acf-field-icon_picker.php:477
#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Groups Icon"
msgstr "Icona gruppi"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Grid View Icon"
msgstr "Icona vista griglia"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Forms Icon"
msgstr "Icona moduli"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Flag Icon"
msgstr "Icona bandiera"

#: includes/fields/class-acf-field-icon_picker.php:581
#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Filter Icon"
msgstr "Icona filtro"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Feedback Icon"
msgstr "Icona feedback"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Facebook (alt) Icon"
msgstr "Icona Facebook (alt)"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Facebook Icon"
msgstr "Icona Facebook"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "External Icon"
msgstr "Icona esterno"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Email (alt) Icon"
msgstr "Icona email (alt)"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Email Icon"
msgstr "Icona email"

#: includes/fields/class-acf-field-icon_picker.php:565
#: includes/fields/class-acf-field-icon_picker.php:591
#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Video Icon"
msgstr "Icona video"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Unlink Icon"
msgstr "Icona scollega"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Underline Icon"
msgstr "Icona sottolineatura"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Text Color Icon"
msgstr "Icona colore testo"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Table Icon"
msgstr "Icona tabella"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Strikethrough Icon"
msgstr "Icona barrato"

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Spellcheck Icon"
msgstr "Icona controllo ortografia"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Remove Formatting Icon"
msgstr "Icona rimuovi formattazione"

#: includes/fields/class-acf-field-icon_picker.php:555
#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Quote Icon"
msgstr "Icona citazione"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Paste Word Icon"
msgstr "Icona incolla parola"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paste Text Icon"
msgstr "Icona incolla testo"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Paragraph Icon"
msgstr "Icona paragrafo"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Outdent Icon"
msgstr "Icona non indentare"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Kitchen Sink Icon"
msgstr "Icona lavello cucina"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Justify Icon"
msgstr "Icona giustifica"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Italic Icon"
msgstr "Icona corsivo"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Insert More Icon"
msgstr "Icona inserisci altro"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Indent Icon"
msgstr "Icona indentare"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Help Icon"
msgstr "Icona aiuto"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Expand Icon"
msgstr "Icona espandi"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Contract Icon"
msgstr "Icona contrai"

#: includes/fields/class-acf-field-icon_picker.php:538
#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Code Icon"
msgstr "Icona codice"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Break Icon"
msgstr "Icona interruzione"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Bold Icon"
msgstr "Icona grassetto"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Edit Icon"
msgstr "Icona modifica"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Download Icon"
msgstr "Icona scarica"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Dismiss Icon"
msgstr "Icona ignora"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Desktop Icon"
msgstr "Icona desktop"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Dashboard Icon"
msgstr "Icona bacheca"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Cloud Icon"
msgstr "Icona cloud"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Clock Icon"
msgstr "Icona orologio"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Clipboard Icon"
msgstr "Icona appunti"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Chart Pie Icon"
msgstr "Icona grafico a torta"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Chart Line Icon"
msgstr "Icona grafico a linee"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Chart Bar Icon"
msgstr "Icona grafico a barre"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Chart Area Icon"
msgstr "Icona area grafico"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Category Icon"
msgstr "Icona categoria"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Cart Icon"
msgstr "Icona carrello"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Carrot Icon"
msgstr "Icona carota"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Camera Icon"
msgstr "Icona fotocamera"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Calendar (alt) Icon"
msgstr "Icona calendario (alt)"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Calendar Icon"
msgstr "Icona calendario"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Businesswoman Icon"
msgstr "Icona donna d'affari"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Building Icon"
msgstr "Icona edificio"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Book Icon"
msgstr "Icona libro"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Backup Icon"
msgstr "Icona backup"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Awards Icon"
msgstr "Icona premi"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Art Icon"
msgstr "Icona arte"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Arrow Up Icon"
msgstr "Icona freccia su"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Arrow Right Icon"
msgstr "Icona freccia destra"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Left Icon"
msgstr "Icona freccia sinistra"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Down Icon"
msgstr "Icona freccia giù"

#: includes/fields/class-acf-field-icon_picker.php:449
#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Archive Icon"
msgstr "Icona archivio"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Analytics Icon"
msgstr "Icona analytics"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Align Right Icon"
msgstr "Icona allinea a destra"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Align None Icon"
msgstr "Icona nessun allineamento"

#: includes/fields/class-acf-field-icon_picker.php:441
#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Align Left Icon"
msgstr "Icona allinea a sinistra"

#: includes/fields/class-acf-field-icon_picker.php:439
#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Align Center Icon"
msgstr "Icona allinea al centro"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Album Icon"
msgstr "Icona album"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Users Icon"
msgstr "Icona utenti"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Tools Icon"
msgstr "Icona strumenti"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Site Icon"
msgstr "Icona sito"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Settings Icon"
msgstr "Icona impostazioni"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Post Icon"
msgstr "Icona articolo"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Plugins Icon"
msgstr "Icona plugin"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Page Icon"
msgstr "Icona pagina"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Network Icon"
msgstr "Icona rete"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Multisite Icon"
msgstr "Icona multisito"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Media Icon"
msgstr "Icona media"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Links Icon"
msgstr "Icona link"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Home Icon"
msgstr "Icona home"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Customizer Icon"
msgstr "Icona personalizza"

#: includes/fields/class-acf-field-icon_picker.php:419
#: includes/fields/class-acf-field-icon_picker.php:743
msgid "Comments Icon"
msgstr "Icona commenti"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Collapse Icon"
msgstr "Icona richiudi"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Appearance Icon"
msgstr "Icona aspetto"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Generic Icon"
msgstr "Icona generica"

#: includes/fields/class-acf-field-icon_picker.php:353
msgid "Icon picker requires a value."
msgstr "Il selettore delle icone richiede un valore."

#: includes/fields/class-acf-field-icon_picker.php:348
msgid "Icon picker requires an icon type."
msgstr "Il selettore delle icone richiede un tipo di icona."

#: includes/fields/class-acf-field-icon_picker.php:317
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Le icone disponibili che corrispondenti alla tua query di ricerca sono state "
"caricate nel selettore delle icone qui sotto."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "No results found for that search term"
msgstr "Nessun risultato trovato per quel termine di ricerca"

#: includes/fields/class-acf-field-icon_picker.php:298
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:297
msgid "String"
msgstr "Stringa"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:285
msgid "Specify the return format for the icon. %s"
msgstr "Specifica il formato restituito per l'icona. %s"

#: includes/fields/class-acf-field-icon_picker.php:270
msgid "Select where content editors can choose the icon from."
msgstr "Seleziona da dove gli editor dei contenuti possono scegliere l'icona."

#: includes/fields/class-acf-field-icon_picker.php:242
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "L'URL all'icona che vorresti utilizzare, oppure svg come Data URI"

#: includes/fields/class-acf-field-icon_picker.php:225
msgid "Browse Media Library"
msgstr "Sfoglia la libreria dei media"

#: includes/fields/class-acf-field-icon_picker.php:216
msgid "The currently selected image preview"
msgstr "L'anteprima dell'immagine attualmente selezionata"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Click to change the icon in the Media Library"
msgstr "Fai clic per cambiare l'icona nella libreria dei media"

#: includes/fields/class-acf-field-icon_picker.php:95
msgid "Search icons..."
msgstr "Cerca le icone..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Libreria dei media"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Un'interfaccia utente interattiva per selezionare un'icona. Scegli tra "
"Dashicons, la libreria dei media o un campo URL indipendente."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Selettore delle icone"

#: src/Site_Health/Site_Health.php:787
msgid "JSON Load Paths"
msgstr "Percorsi di caricamento JSON"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Save Paths"
msgstr "Percorsi di salvataggio JSON"

#: src/Site_Health/Site_Health.php:772
msgid "Registered ACF Forms"
msgstr "Moduli ACF registrati"

#: src/Site_Health/Site_Health.php:766
msgid "Shortcode Enabled"
msgstr "Shortcode abilitato"

#: src/Site_Health/Site_Health.php:758
msgid "Field Settings Tabs Enabled"
msgstr "Schede delle impostazioni del campo abilitate"

#: src/Site_Health/Site_Health.php:750
msgid "Field Type Modal Enabled"
msgstr "Modal del tipo di campo abilitato"

#: src/Site_Health/Site_Health.php:742
msgid "Admin UI Enabled"
msgstr "Interfaccia utente dell'amministratore abilitata"

#: src/Site_Health/Site_Health.php:733
msgid "Block Preloading Enabled"
msgstr "Precaricamento del blocco abilitato"

#: src/Site_Health/Site_Health.php:721
msgid "Blocks Per ACF Block Version"
msgstr "Blocchi per la versione a blocchi di ACF"

#: src/Site_Health/Site_Health.php:716
msgid "Blocks Per API Version"
msgstr "Blocchi per la versione API"

#: src/Site_Health/Site_Health.php:689
msgid "Registered ACF Blocks"
msgstr "Blocchi di ACF registrati"

#: src/Site_Health/Site_Health.php:683
msgid "Light"
msgstr "Light"

#: src/Site_Health/Site_Health.php:683
msgid "Standard"
msgstr "Standard"

#: src/Site_Health/Site_Health.php:682
msgid "REST API Format"
msgstr "Formato REST API"

#: src/Site_Health/Site_Health.php:674
msgid "Registered Options Pages (PHP)"
msgstr "Pagine opzioni registrate (PHP)"

#: src/Site_Health/Site_Health.php:660
msgid "Registered Options Pages (JSON)"
msgstr "Pagine opzioni registrate (JSON)"

#: src/Site_Health/Site_Health.php:655
msgid "Registered Options Pages (UI)"
msgstr "Pagine opzioni registrate (UI)"

#: src/Site_Health/Site_Health.php:625
msgid "Options Pages UI Enabled"
msgstr "Interfaccia utente delle pagine delle opzioni abilitata"

#: src/Site_Health/Site_Health.php:617
msgid "Registered Taxonomies (JSON)"
msgstr "Tassonomie registrate (JSON)"

#: src/Site_Health/Site_Health.php:605
msgid "Registered Taxonomies (UI)"
msgstr "Tassonomie registrate (UI)"

#: src/Site_Health/Site_Health.php:593
msgid "Registered Post Types (JSON)"
msgstr "Post type registrati (JSON)"

#: src/Site_Health/Site_Health.php:581
msgid "Registered Post Types (UI)"
msgstr "Post type registrati (UI)"

#: src/Site_Health/Site_Health.php:568
msgid "Post Types and Taxonomies Enabled"
msgstr "Post type e tassonomie registrate"

#: src/Site_Health/Site_Health.php:561
msgid "Number of Third Party Fields by Field Type"
msgstr "Numbero di campi di terze parti per tipo di campo"

#: src/Site_Health/Site_Health.php:556
msgid "Number of Fields by Field Type"
msgstr "Numero di campi per tipo di campo"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Gruppi di campi abilitati per GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Gruppi di campi abilitati per REST API"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Gruppi di campi registrati (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Gruppi di campi registrati (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Gruppi di campi registrati (IU)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Plugin attivi"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Tema genitore"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Tema attivo"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "È multisito"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "Versione MySQL"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "Versione di WordPress"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Data di scadenza dell'abbonamento"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Stato della licenza"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Tipo di licenza"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "URL con licenza"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licenza attivata"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Gratuito"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Tipo di plugin"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Versione del plugin"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Questa sezione contiene informazioni di debug sulla tua configurazione di "
"ACF che possono essere utili per fornirti supporto."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""
"Un blocco ACF su questa pagina richiede attenzioni prima che tu possa "
"salvare."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Questo dato è registrato quando rileviamo valori che sono stati modificati "
"durante l'output. %1$sPulisci il registro e ignora%2$s dopo aver effettuato "
"l'escape dei valori nel tuo codice. Questa notifica riapparirà se rileveremo "
"nuovamente valori modificati."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Ignora definitivamente"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""
"Istruzione per gli editori del contenuto. Mostrato quando si inviano dati."

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has no term selected"
msgstr "Non ha nessun termine selezionato"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Has any term selected"
msgstr "Ha un qualsiasi termine selezionato"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms do not contain"
msgstr "I termini non contengono"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Terms contain"
msgstr "I termini contengono"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is not equal to"
msgstr "Il termine non è uguale a"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Term is equal to"
msgstr "Il termine è uguale a"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has no user selected"
msgstr "Non ha nessun utente selezionato"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Has any user selected"
msgstr "Ha un qualsiasi utente selezionato"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users do not contain"
msgstr "Gli utenti non contegnono"

#: includes/admin/post-types/admin-field-group.php:133
msgid "Users contain"
msgstr "Gli utenti contengono"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is not equal to"
msgstr "L'utente non è uguale a"

#: includes/admin/post-types/admin-field-group.php:131
msgid "User is equal to"
msgstr "L'utente è uguale a"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has no page selected"
msgstr "Non ha nessuna pagina selezionata"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Has any page selected"
msgstr "Ha una qualsiasi pagina selezionata"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages do not contain"
msgstr "Le pagine non contengono"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Pages contain"
msgstr "Le pagine contengono"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is not equal to"
msgstr "La pagina non è uguale a"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Page is equal to"
msgstr "La pagina è uguale a"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has no relationship selected"
msgstr "Non ha nessuna relazione selezionata"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has any relationship selected"
msgstr "Ha una qualsiasi relazione selezionata"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has no post selected"
msgstr "Non ha nessun articolo selezionato"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Has any post selected"
msgstr "Ha un qualsiasi articolo selezionato"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts do not contain"
msgstr "Gli articoli non contengono"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Posts contain"
msgstr "Gli articoli contengono"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is not equal to"
msgstr "L'articolo non è uguale a"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Post is equal to"
msgstr "L'articolo è uguale a"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships do not contain"
msgstr "Le relazioni non contengono"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationships contain"
msgstr "Le relazioni contengono"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is not equal to"
msgstr "La relazione non è uguale a"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Relationship is equal to"
msgstr "La relazione è uguale a"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Campi ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Funzionalità di ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Rinnova la versione PRO per sbloccare"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Rinnova licenza della versione PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "I campi PRO non possono essere modificati senza una licenza attiva."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Attiva la tua licenza ACF PRO per modificare i gruppi di campi assegnati ad "
"un blocco ACF."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Attiva la tua licenza ACF PRO per modificare questa pagina delle opzioni."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Restituire valori HTML con escape effettuato è possibile solamente quando "
"anche format_value è vero. I valori del campo non sono stati restituiti per "
"sicurezza."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Restituire un valore HTML con escape effettuato è possibile solamente quando "
"anche format_value è vero. Il valore del campo non è stato restituito per "
"sicurezza."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ora effettua l'escape del codice HTML non sicuro quando è restituito da "
"<code>the_field</code> o dallo shortcode ACF. Abbiamo rilevato che l'output "
"di alcuni dei tuoi campi è stato alterato da questa modifica, ma potrebbe "
"anche non trattarsi di una manomissione. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Contatta l'amministratore o lo sviluppatore del tuo sito per ulteriori "
"dettagli."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Approfondisci"

#: includes/admin/admin.php:64
msgid "Hide&nbsp;details"
msgstr "Nascondi&nbsp;dettagli"

#: includes/admin/admin.php:63 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Mostra&nbsp;dettagli"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - restituito da %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Rinnova la licenza di ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Rinnova la licenza"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Gestisci la licenza"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "La posizione \"In alto\" non è supportata nell'editor a blocchi"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Esegui l'upgrade ad ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"Le <a href=\"%s\" target=\"_blank\">pagine opzioni</a> di ACF sono pagine di "
"amministrazione personalizzate per gestire impostazioni globali attraverso "
"dei campi. Puoi creare più pagine e sottopagine."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Aggiungi pagina opzioni"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Nell'editor utilizzato come segnaposto del titolo."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Segnaposto del titolo"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 mesi gratuiti"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplicato da %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Seleziona pagine opzioni"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplica tassonomia"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Crea tassonomia"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplica post type"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Crea post type"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Collega gruppi di campi"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Aggiungi campi"

#: includes/admin/post-types/admin-field-group.php:146
msgid "This Field"
msgstr "Questo campo"

#: includes/admin/admin.php:385
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:383
msgid "Feedback"
msgstr "Feedback"

#: includes/admin/admin.php:381
msgid "Support"
msgstr "Supporto"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:356
msgid "is developed and maintained by"
msgstr "è sviluppato e manutenuto da"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""
"Aggiungi questo/a %s alle regole di posizionamento dei gruppi di campi "
"selezionati."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Abilitare l'impostazione bidirezionale ti permette di aggiornare un valore "
"nei campi di destinazione per ogni valore selezionato per questo campo, "
"aggiungendo o rimuovendo l'ID dell'articolo, l'ID della tassonomia o l'ID "
"dell'utente dell'elemento che si sta aggiornando. Per ulteriori informazioni "
"leggi la <a href=\"%s\" target=\"_blank\">documentazione</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Seleziona il campo o i campi per memorizzare il riferimento all'elemento che "
"si sta aggiornando. Puoi selezionare anche questo stesso campo. I campi di "
"destinazione devono essere compatibili con dove questo campo sarà "
"visualizzato. Per esempio, se questo campo è visualizzato su una tassonomia, "
"il tuo campo di destinazione deve essere di tipo tassonomia."

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Campo di destinazione"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Aggiorna un campo nei valori selezionati, facendo riferimento a questo ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidirezionale"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Campo %s"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Selezione multipla"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logo WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Solo lettere minuscole, trattini bassi e trattini. Massimo 32 caratteri."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Il nome della capacità per assegnare termini di questa tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Capacità per assegnare termini"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Il nome della capacità per eliminare termini di questa tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Capacità per eliminare termini"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Il nome della capacità per modificare i termini di questa tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Capacità per modificare termini"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Il nome della capacità per gestire i termini di questa tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Capacità per gestire termini"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Stabilisce se gli articoli dovrebbero essere esclusi o meno dai risultati di "
"ricerca e dalle pagine di archivio delle tassonomia."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Altri strumenti da WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Costruito per chi costruisce con WordPress, dal team di %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Visualizza i prezzi ed esegui l'upgrade"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:280
msgid "Learn More"
msgstr "Approfondisci"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Accelera il tuo flusso di lavoro e sviluppa siti web migliori con "
"funzionalità come i blocchi di ACF, le pagine opzioni e campi evoluti come "
"ripetitore, contenuto flessibile, clone e galleria."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Sblocca funzionalità avanzate e costruisci ancora meglio con ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "Campi di %s"

#: includes/admin/post-types/admin-taxonomies.php:263
msgid "No terms"
msgstr "Nessun termine"

#: includes/admin/post-types/admin-taxonomies.php:236
msgid "No post types"
msgstr "Nessun post type"

#: includes/admin/post-types/admin-post-types.php:260
msgid "No posts"
msgstr "Nessun articolo"

#: includes/admin/post-types/admin-post-types.php:234
msgid "No taxonomies"
msgstr "Nessuna tassonomia"

#: includes/admin/post-types/admin-post-types.php:179
#: includes/admin/post-types/admin-taxonomies.php:178
msgid "No field groups"
msgstr "Nessun gruppo di campi"

#: includes/admin/post-types/admin-field-groups.php:251
msgid "No fields"
msgstr "Nessun campo"

#: includes/admin/post-types/admin-field-groups.php:124
#: includes/admin/post-types/admin-post-types.php:143
#: includes/admin/post-types/admin-taxonomies.php:142
msgid "No description"
msgstr "Nessuna descrizione"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Qualsiasi stato dell'articolo"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Questa chiave di tassonomia è già utilizzata da un'altra tassonomia "
"registrata al di fuori di ACF e, di conseguenza, non può essere utilizzata."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Questa chiave di tassonomia è già stata usata da un'altra tassonomia in ACF "
"e non può essere usata."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La chiave della tassonomia deve contenere esclusivamente caratteri "
"alfanumerici minuscoli, trattini bassi e trattini."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "La chiave della tassonomia deve contenere al massimo 32 caratteri."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Nessuna tassonomia trovata nel cestino"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Nessuna tassonomia trovata"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Cerca tassonomie"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Visualizza tassonomia"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nuova tassonomia"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Modifica tassonomia"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Aggiungi nuova tassonomia"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Nessun post type trovato nel cestino"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Nessun post type trovato"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Cerca post type"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Visualizza post type"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nuovo post type"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Modifica post type"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Aggiungi nuovo post type"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Questa chiave del post type è già utilizzata da un altro post type "
"registrato al di fuori di ACF e, di conseguenza, non può essere utilizzata."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Questa chiave del post type è già utilizzata da un altro post type di ACF e, "
"di conseguenza, non può essere utilizzata."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Questo campo non deve essere un <a href=\"%s\" target=\"_blank\">termine "
"riservato</a> di WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La chiave del post type deve necessariamente contenere solo caratteri "
"alfanumerici minuscoli, trattini bassi o trattini."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "La chiave del post type deve essere al massimo di 20 caratteri."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Ti sconsigliamo di utilizzare questo campo nei blocchi di ACF."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Visualizza l'editor WYSIWYG di WordPress, come quello che si vede negli "
"articoli e nelle pagine, che permette di modificare il testo in modalità "
"rich text e che permette anche di inserire contenuti multimediali."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Editor WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Permette di selezionare uno o più utenti che possono essere utilizzati per "
"creare relazioni tra oggetti di dati."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""
"Un campo di testo progettato specificamente per memorizzare indirizzi web."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Un commutatore che ti permette di scegliere un valore 1 o 0 (on o off, vero "
"o falso ecc.). Può essere presentato come un interruttore stilizzato oppure "
"un checkbox."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Un'interfaccia utente interattiva per selezionare un orario. Il formato "
"dell'orario può essere personalizzato utilizzando le impostazioni del campo."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Un'area di testo di base per memorizzare paragrafi o testo."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Un campo di testo base, utile per memorizzare singoli valori di stringa."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Permette la selezione di uno o più termini di tassonomia sulla base dei "
"criteri e delle opzioni specificate nelle impostazioni del campo."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Ti permette di raggruppare i campi all'interno di sezioni a scheda nella "
"schermata di modifica. Utile per tenere i campi ordinati e strutturati."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Un elenco a discesa con una selezione di opzioni a tua scelta."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Un'interfaccia a due colonne per selezionare uno o più articoli, pagine o "
"post type personalizzati per creare una relazione con l'elemento che stai "
"attualmente modificando. Include opzioni per cercare e per filtrare."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Un campo per selezionare, attraverso un elemento di controllo "
"dell'intervallo, un valore numerico compreso all'interno di un intervallo "
"definito."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Un gruppo di elementi radio button che permettono all'utente di esprimere "
"una singola scelta tra valori da te specificati."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Un'interfaccia utente interattiva e personalizzabile per selezionare uno o "
"più articoli, pagine o elementi post type con la possibilità di effettuare "
"una ricerca. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Un input per fornire una password utilizzando un campo mascherato."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtra per stato dell'articolo"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Un menu a discesa interattivo per selezionare uno o più articoli, pagine, "
"elementi post type personalizzati o URL di archivi con la possibilità di "
"effettuare una ricerca."

#: includes/fields/class-acf-field-oembed.php:23
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Un componente interattivo per incorporare video, immagini, tweet, audio e "
"altri contenuti utilizzando la funzionalità oEmbed nativa di WordPress."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Un input limitato a valori numerici."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Utilizzato per mostrare un messaggio agli editori accanto agli altri campi. "
"Utile per fornire ulteriore contesto o istruzioni riguardanti i tuoi campi."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Ti permette di specificare un link e le sue proprietà, quali \"title\" e "
"\"target\", utilizzando il selettore di link nativo di WordPress."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Utilizza il selettore di media nativo di WordPress per caricare o scegliere "
"le immagini."

#: includes/fields/class-acf-field-group.php:23
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Fornisce un modo per strutturare i campi all'interno di gruppi per "
"organizzare meglio i dati e la schermata di modifica."

#: includes/fields/class-acf-field-google-map.php:23
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Un'interfaccia utente interattiva per selezionare un luogo utilizzando "
"Google Maps. Richiede una chiave API di Google Maps e una configurazione "
"aggiuntiva per essere visualizzata correttamente."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Utilizza il selettore di media nativo di WordPress per caricare o scegliere "
"i file."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Un campo di testo pensato specificamente per memorizzare indirizzi email."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Un'interfaccia utente interattiva per scegliere una data e un orario. Il "
"formato della data restituito può essere personalizzato utilizzando le "
"impostazioni del campo."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Un'interfaccia utente interattiva per scegliere una data. Il formato della "
"data restituito può essere personalizzato utilizzando le impostazioni del "
"campo."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Un'interfaccia utente interattiva per selezionare un coloro o per "
"specificare un valore HEX."

#: includes/fields/class-acf-field-checkbox.php:37
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Un gruppo di elementi checkbox che permettono all'utente di selezionare uno "
"o più valori tra quelli da te specificati."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Un gruppo di pulsanti con valori da te specificati. Gli utenti possono "
"scegliere un'opzione tra i valori forniti."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Ti permette di raggruppare e organizzare i campi personalizzati in pannelli "
"richiudibili che sono mostrati durante la modifica del contenuto. Utile per "
"mantenere in ordine grandi insiemi di dati."

#: includes/fields.php:450
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Questo campo fornisce una soluzione per ripetere contenuti, quali slide, "
"membri del team e riquadri di invito all'azione, fungendo da genitore di una "
"serie di sotto-campi che possono essere ripetuti più e più volte."

#: includes/fields.php:440
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Questo campo fornisce un'interfaccia interattiva per gestire una collezione "
"di allegati. La maggior parte delle impostazioni è simile a quella del tipo "
"di campo \"Immagine\". Impostazioni aggiuntive ti permettono di specificare "
"dove saranno aggiunti i nuovi allegati all'interno della galleria e il "
"numero minimo/massimo di allegati permessi."

#: includes/fields.php:430
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Questo campo fornisce un editor semplice, strutturato e basato sul layout. "
"Il campo \"Contenuto flessibile\" ti permette di definire, creare e gestire "
"il contenuto con un controllo totale, attraverso l'utilizzo di layout e "
"sotto-campi per progettare i blocchi disponibili."

#: includes/fields.php:420
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Questo campo ti permette di selezionare e visualizzare campi esistenti. Non "
"duplica nessun campo nel database, ma carica e visualizza i campi "
"selezionati al momento dell'esecuzione. Il campo \"Clone\" può sostituire sé "
"stesso con i campi selezionati oppure visualizzarle come gruppo di sotto-"
"campi."

#: includes/fields.php:417
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:332
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:330 includes/fields.php:387
msgid "Advanced"
msgstr "Avanzato"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (più recente)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Originale"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "ID articolo non valido."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Post type non valido selezionato per la revisione."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Altro"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Seleziona campo"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Prova un termine di ricerca diverso oppure sfoglia %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Campi popolari"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:115
msgid "No search results for '%s'"
msgstr "Nessun risultato di ricerca per '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Cerca campi..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Seleziona tipo di campo"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popolare"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Aggiungi tassonomia"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Crea tassonomie personalizzate per classificare il contenuto del post type"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Aggiungi la tua prima tassonomia"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Le tassonomie gerarchiche possono avere discendenti (come le categorie)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Rendi una tassonomia visibile sul frontend e nella bacheca di "
"amministrazione."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Uno o più post type che possono essere classificati con questa tassonomia."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genere"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genere"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Generi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Controllore personalizzato opzionale da utilizzare invece di "
"`WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Rivela questo post type nella REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Personalizza il nome della variabile della query"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Si può accedere ai termini utilizzando permalink non-pretty, ad es. "
"{query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Termini genitore-figlio negli URL per le tassonomie gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Personalizza lo slug utilizzato nell'URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "I permalink per questa tassonomia sono disabilitati."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Riscrivi l'URL utilizzando la chiave della tassonomia come slug. La tua "
"struttura del permalink sarà"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Chiave della tassonomia"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Seleziona il tipo di permalink da utilizzare per questa tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Visualizza una colonna per la tassonomia nelle schermate che elencano i post "
"type."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Mostra colonna amministratore"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Mostra la tassonomia nel pannello di modifica rapida/di massa."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Modifica rapida"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Elenca la tassonomia nei controlli del widget Tag Cloud."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Tag Cloud"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Il nome di una funzione PHP da richiamare per sanificare i dati della "
"tassonomia salvati da un meta box."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Callback della sanificazione del meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Registra la callback del meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Nessun meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Meta box personalizzato"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Controlla il meta box nella schermata di modifica del contenuto. Per "
"impostazione predefinita, il meta box delle categorie è mostrato per "
"tassonomie gerarchiche, mentre quello dei tag è mostrato per le tassonomie "
"non gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Meta box delle categorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Meta box dei tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Un link ad un tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Descrive una variazione del blocco di link di navigazione utilizzata "
"nell'editor a blocchi."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Un link a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Link del tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Assegna un titolo alla variazione del blocco di link di navigazione "
"utilizzata nell'editor a blocchi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Vai ai tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Assegna il testo utilizzato per il link che permette di tornare indietro "
"all'indice principale dopo aver aggiornato un termine."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Torna agli elementi"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Torna a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Elenco dei tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Assegna il testo al titolo nascosto della tabella."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navigazione elenco dei tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Assegna il testo al titolo nascosto della paginazione della tabella."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtra per categoria"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""
"Assegna il testo al pulsante filtro nella tabella di elenchi di articoli."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtra per elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtra per %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Per impostazione predefinita, la descrizione non è visibile; tuttavia, "
"alcuni temi potrebbero mostrarla."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Descrive il campo \"Descrizione\" nella schermata di modifica del tag."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Descrizione del campo \"Descrizione\""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Per creare una gerarchia assegna un termine genitore. Ad esempio, il termine "
"Jazz potrebbe essere genitore di Bebop e Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Descrive il campo \"Genitore\" nella schermata di modifica dei tag."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Descrizione del campo \"Genitore\""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"Lo \"slug\" è la versione del nome compatibile con l'URL. Di solito è tutto "
"minuscolo e contiene solo lettere, numeri e trattini."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Descrive il campo \"Slug\" nella schermata di modifica dei tag."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Descrizione del campo \"Slug\""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Il nome è come apparirà sul tuo sito"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Descrive il campo \"Nome\" nella schermata di modifica dei tag."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Descrizione del campo \"Nome\""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Nessun tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Assegna il testo visualizzato nelle tabelle di elenco degli articoli e dei "
"media nel caso in cui non siano disponibili né tag né categorie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Nessun termine"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Nessun %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Nessun tag trovato"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Assegna il testo visualizzato quando si fa clic sul testo \"Scegli tra i più "
"utilizzati\" nel meta box della tassonomia nel caso in cui non sia "
"disponibile nessun tag, e assegna il testo utilizzato nella tabella di "
"elenco dei termini quando non ci sono elementi per una tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Non trovato"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Assegna il testo al campo \"Titolo\" della scheda \"Più utilizzati\"."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Più utilizzati"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Scegli tra i tag più utilizzati"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Assegna il testo \"Scegli tra i più utilizzati\" nel meta box nel caso in "
"cui JavaScript sia disabilitato. È utilizzato esclusivamente per le "
"tassonomie non gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Scegli tra i più utilizzati"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Scegli tra i %s più utilizzati"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Aggiungi o rimuovi tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assegna il testo per aggiungere o rimuovere elementi utilizzato nel meta box "
"nel caso in cui JavaScript sia disabilitato. È utilizzato esclusivamente per "
"le tassonomie non gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Aggiungi o rimuovi elementi"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Aggiungi o rimuovi %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separa i tag con le virgole"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Assegna il testo per separare gli elementi con le virgole utilizzato nel "
"meta box della tassonomia. È utilizzato esclusivamente per le tassonomie non "
"gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separa gli elementi con le virgole"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separa i %s con una virgola"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Tag popolari"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Assegna il testo per gli elementi popolari. È utilizzato esclusivamente per "
"le tassonomie non gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Elementi popolari"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s popolari"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Cerca tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Assegna il testo per la ricerca di elementi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Categoria genitore:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Assegna il testo per l'elemento genitore, ma con i due punti (:) aggiunti "
"alla fine."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Elemento genitore con due punti"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Categoria genitore"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Assegna il testo per l'elemento genitore. Utilizzato esclusivamente per le "
"tassonomie gerarchiche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Elemento genitore"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Genitore %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nome del nuovo tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Assegna il testo per il nome del nuovo elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nome del nuovo elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nome del nuovo %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Aggiungi nuovo tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Assegna il testo per l'aggiunta di un nuovo elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Aggiorna tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Assegna il testo per l'aggiornamento dell'elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Aggiorna elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Aggiorna %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Visualizza tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""
"Nella barra di amministrazione per visualizzare il termine durante la "
"modifica."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Modifica tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""
"Nella parte alta della schermata di modifica durante la modifica di un "
"termine."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Tutti i tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Assegna il testo per tutti gli elementi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Assegna il testo per il nome nel menu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Etichetta del menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Le tassonomie attive sono abilitate e registrate con WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Un riassunto descrittivo della tassonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Un riassunto descrittivo del termine."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Descrizione del termine"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Singola parola, nessun spazio. Sottolineatura e trattini consentiti."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Slug del termine"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Il nome del termine predefinito."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nome del termine"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Crea un termine per la tassonomia che non potrà essere eliminato. Non sarà "
"selezionato negli articoli per impostazione predefinita."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Termine predefinito"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Se i termini in questa tassonomia debbano essere o meno ordinati in base "
"all'ordine in cui vengono forniti a `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Ordina i termini"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Aggiunti post type"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Espandi la funzionalità di WordPress con i post type personalizzati, andando "
"oltre gli articoli e le pagine standard."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Aggiungi il tuo primo post type"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "So cosa sto facendo, mostrami tutte le opzioni."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Configurazione avanzata"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""
"I post type gerarchici possono avere discendenti (come avviene per le "
"pagine)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Gerarchico"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Visibile sul frontend e sulla bacheca di amministrazione."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Pubblico"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Solo lettere minuscole, trattini bassi e trattini, massimo 20 caratteri."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Etichetta singolare"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Etichetta plurale"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Controllore personalizzato opzionale da utilizzare invece di "
"`WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Classe del controllore"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "La parte del namespace nell'URL della REST API."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Percorso del namespace"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "L'URL di base per gli URL della REST API del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "URL di base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Mostra questo post type nella REST API. Necessario per utilizzare l'editor a "
"blocchi."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Mostra nella REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Personalizza il nome della variabile della query."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Variabile della query"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Nessun supporto per la variabile della query"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Variabile personalizzata della query"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Si può accedere agli elementi utilizzando permalink non-pretty, ad es. "
"{post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Supporto per la variabile della query"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Gli elementi e gli URL di un elemento sono accessibili tramite una stringa "
"query."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Interrogabile pubblicamente tramite query"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Slug personalizzato per l'URL dell'archivio."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Slug dell'archivio"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Ha un elemento archivio che può essere personalizzato con un file template "
"dell'archivio nel tuo tema."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archivio"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""
"Supporto per la paginazione degli URL degli elementi, come gli archivi."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Paginazione"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL del feed RSS per gli elementi del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL del feed"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Altera la struttura del permalink per aggiungere il prefisso `WP_Rewrite::"
"$front` agli URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Prefisso dell'URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Personalizza lo slug utilizzato nell'URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Slug dell'URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "I permalink per questo post type sono disabilitati."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Riscrivi l'URL utilizzando uno slug personalizzato definito nel campo qui "
"sotto. La tua struttura del permalink sarà"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Nessun permalink (evita la riscrittura dell'URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Permalink personalizzato"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Chiave del post type"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Riscrivi l'URL utilizzando la chiave del post type come slug. La tua "
"struttura del permalink sarà"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Riscrittura del permalink"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Elimina gli elementi di un utente quando quell'utente è eliminato."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Elimina con l'utente"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Permetti l'esportazione del post type da \"Strumenti\" > \"Esporta\"."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Si può esportare"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Fornisci, facoltativamente, un plurale da utilizzare nelle capacità."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Nome plurale per la capacità"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Scegli un altro post type su cui basare le capacità per questo post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Nome singolare per la capacità"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Per impostazione predefinita, le capacità del post type erediteranno i nomi "
"delle capacità degli articoli, ad esempio edit_post, delete_posts. Abilita "
"questa opzione per utilizzare capacità specifiche per questo post type, ad "
"esempio edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Rinomina capacità"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Escludi dalla ricerca"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Permetti che gli elementi possano essere aggiunti ai menu nella schermata "
"\"Aspetto\" > \"Menu\". Questi elementi devono essere abilitati nelle "
"\"Impostazioni schermata\"."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Supporto per i menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""
"Appare come un elemento nel menu \"Nuovo\" nella barra di amministrazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Mostra nella barra di amministrazione"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Callback personalizzata per il meta box"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Menu Icon"
msgstr "Icona del menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""
"La posizione nel menu nella barra laterale nella bacheca di amministrazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Posizione nel menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Per impostazione predefinita il post type otterrà un nuovo elemento di primo "
"livello nel menu di amministrazione. Se imposti qui un elemento di primo "
"livello esistente, il post type sarà aggiunto come elemento del suo "
"sottomenu."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Elemento genitore nel menu di amministrazione"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""
"Elemento di navigazione nel menu di amministrazione nella barra laterale."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Mostra nel menu di amministrazione"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""
"Gli elementi possono essere modificati e gestiti nella bacheca di "
"amministrazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Mostra nell'interfaccia utente"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Un link a un articolo."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Descrizione per una variazione del blocco di link di navigazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Descrizione del link all'elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Un link a un/a %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Link dell'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Titolo per una variazione del blocco di link di navigazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Link dell'elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Link %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Articolo aggiornato."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Nella notifica nell'editor dopo che un elemento è stato aggiornato."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Elemento aggiornato"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s aggiornato/a."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Articolo programmato."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Nella notifica nell'editor dopo aver programmato un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Elemento programmato"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s programmato/a."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Articolo riconvertito in bozza."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""
"Nella notifica nell'editor dopo aver riconvertito in bozza un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Elemento riconvertito in bozza."

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s riconvertito/a in bozza."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Articolo pubblicato privatamente."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""
"Nella notifica nell'editor dopo aver pubblicato privatamente un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Elemento pubblicato privatamente"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s pubblicato/a privatamente."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Articolo pubblicato."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Nella notifica nell'editor dopo aver pubblicato un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Elemento pubblicato"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s pubblicato/a."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Elenco degli articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Utilizzato dai lettori di schermo per l'elenco degli elementi nella "
"schermata di elenco del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Elenco degli elementi"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Elenco %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navigazione dell'elenco degli articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Utilizzato dei lettori di schermo per i filtri di paginazione dell'elenco "
"nella schermata di elenco del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navigazione dell'elenco degli elementi"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navigazione elenco %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtra articoli per data"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Utilizzato dai lettori di schermo per il titolo del filtro per data nella "
"schermata di elenco del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtra elementi per data"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtra %s per data"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtra elenco articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Utilizzato dai lettori di schermo per i link del titolo del filtro nella "
"schermata di elenco del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtra l'elenco degli elementi"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrare l'elenco di %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"Nel modal dei media che mostra tutti i media caricati in questo elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Caricato in questo elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Caricato in questo/a %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Inserisci nell'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Come etichetta del pulsante quando si aggiungono media al contenuto."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Pulsante per inserimento media"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Inserisci in %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Utilizza come immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Come etichetta del pulsante per la selezione di un'immagine da utilizzare "
"come immagine in evidenza."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Usa come immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Rimuovi l'immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Come etichetta del pulsante per rimuovere l'immagine in evidenza."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Rimuovi l'immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Imposta l'immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Come etichetta del pulsante quando si imposta l'immagine in evidenza."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Imposta l'immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"Utilizzato nell'editor come titolo per il meta box dell'immagine in evidenza."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Meta box dell'immagine in evidenza"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Attributi dell'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"Utilizzato nell'editor come titolo del meta box degli attributi "
"dell'articolo."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Meta box degli attributi"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Attributi %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Archivi dell'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Aggiunge elementi \"Archivio del post type\" con quest'etichetta alla lista "
"di articoli mostrati quando si aggiungono elementi ad un menu esistente in "
"un tipo di articolo personalizzato quando sono abilitati gli archivi. Appare "
"solamente durante la modifica dei menu in modalità \"Anteprima\" e se è "
"stato fornito uno slug personalizzato dell'archivio."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menu di navigazione degli archivi"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Archivi %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Nessun articolo trovato nel cestino"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"Nella parte alta della schermata di elenco del post type quando non ci sono "
"articoli nel cestino."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Nessun elemento trovato nel cestino"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Non ci sono %s nel cestino"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Nessun articolo trovato"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"Nella parte alta della schermata di elenco del post type quando non ci sono "
"articoli da visualizzare."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Nessun elemento trovato"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Non abbiamo trovato %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Cerca articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""
"Nella parte alta della schermata degli elementi quando si cerca un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Cerca elementi"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Cerca %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Pagina genitore:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Per elementi gerarchici nella schermata di elenco del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Prefisso dell'elemento genitore"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s genitore:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nuovo articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nuovo elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nuovo/a %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Aggiungi un nuovo articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""
"Nella parte superiore della schermata di modifica durante l'aggiunta di un "
"nuovo elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Aggiungi un nuovo elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Aggiungi nuovo/a %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Visualizza gli articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Appare nella barra di amministrazione nella vista \"Tutti gli articoli\", "
"purché il post type supporti gli archivi e la homepage non sia un archivio "
"di quel post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Visualizza gli elementi"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Visualizza l'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""
"Nella barra di amministrazione per visualizzare l'elemento durante la sua "
"modifica."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Visualizza l'elemento"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Visualizza %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Modifica l'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""
"Nella parte superiore della schermata di modifica durante la modifica di un "
"elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Modifica elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Modifica %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Tutti gli articoli"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "Nel sotto menu del post type nella bacheca di amministrazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Tutti gli elementi"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Tutti/e gli/le %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Nome del post type nel menu di amministrazione."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nome nel menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Rigenera tutte le etichette utilizzando le etichette per il singolare e per "
"il plurale"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Rigenera"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "I post type attivi sono abilitati e registrati con WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Un riassunto descrittivo del post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Aggiungi un'opzione personalizzata"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Abilita varie funzionalità nell'editor del contenuto."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formati dell'articolo"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackback"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Seleziona le tassonomie esistenti per classificare gli elementi del post "
"type."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Sfoglia i campi"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Nulla da importare"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Il plugin Custom Post Type UI può essere disattivato."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "%d elemento importato da Custom Post Type UI -"
msgstr[1] "%d elementi importati da Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Impossibile importare le tassonomie."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Impossibile importare i post type."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"Non è stato selezionato nulla da importare dal plugin Custom Post Type UI."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "1 elemento importato."
msgstr[1] "%s elementi importati."

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Importare un post type o una tassonomia con la stessa chiave di una già "
"esistente sovrascriverà le impostazioni del post type o della tassonomia già "
"esistente con quelle dell'importazione."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importare da Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Il codice seguente può essere utilizzato per registrare una versione locale "
"degli elementi selezionati. Memorizzare localmente i gruppi di campi, i post "
"type o le tassonomie può fornire numerosi vantaggi come ad esempio tempi di "
"caricamento più veloci, controllo di versione e impostazioni o campi "
"dinamici. Devi semplicemente copiare e incollare il seguente codice nel file "
"functions.php del tuo tema, oppure includerlo tramite un file esterno, per "
"poi disattivare o eliminare gli elementi dal pannello di amministrazione di "
"ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Esporta - Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Esporta"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Seleziona le tassonomie"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Seleziona i post type"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "1 elemento esportato."
msgstr[1] "%s elementi esportati."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Categoria"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Tag"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Tassonomia %s creata"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Tassonomia %s aggiornata"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Bozza della tassonomia aggiornata."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Tassonomia programmata per."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Tassonomia inviata."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Tassonomia salvata."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Tassonomia eliminata."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Tassonomia aggiornata."

#: includes/admin/post-types/admin-taxonomies.php:347
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Questa tassonomia non può essere registrata perché la sua chiave è già usata "
"da un'altra tassonomia registrata da un altro plugin o dal tema."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:329
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Tassonomia sincronizzata."
msgstr[1] "%s tassonomie sincronizzate."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:322
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Tassonomia duplicata."
msgstr[1] "%s tassonomie duplicate."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:315
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Tassonomia disattivata."
msgstr[1] "%s tassonomie disattivate."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:308
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Tassonomia attivata."
msgstr[1] "%s tassonomie attivate."

#: includes/admin/post-types/admin-taxonomies.php:109
msgid "Terms"
msgstr "Termini"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:323
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Post type sincronizzato."
msgstr[1] "%s post type sincronizzati."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:316
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Post type duplicato."
msgstr[1] "%s post type duplicati."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:309
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Post type disattivati."
msgstr[1] "%s post type disattivati."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:302
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Post type attivati."
msgstr[1] "%s post type attivati."

#: includes/admin/post-types/admin-post-types.php:84
#: includes/admin/post-types/admin-taxonomies.php:107
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Post type"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Impostazioni avanzate"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Impostazioni di base"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:341
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Questo post type non può essere registrato perché la sua chiave è già "
"utilizzata da un altro post type registrato da un altro plugin o dal tema."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Pagine"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Collega gruppi di campi esistenti"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Post type %s creato"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Aggiungi campi a %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Post type %s aggiornato"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Bozza del post type aggiornata."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Post type programmato per."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Post type inviato."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Post type salvato."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Post type aggiornato."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Post type eliminato."

#: includes/admin/post-types/admin-field-group.php:145
msgid "Type to search..."
msgstr "Digita per cercare..."

#: includes/admin/post-types/admin-field-group.php:100
msgid "PRO Only"
msgstr "Solo PRO"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field groups linked successfully."
msgstr "Gruppi di campi collegati con successo."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:195
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importa post type e tassonomie registrate con Custom Post Type UI e "
"gestiscile con ACF. <a href=\"%s\">Inizia qui</a>."

#: includes/admin/admin.php:47 includes/admin/admin.php:385
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "tassonomia"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "post type"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Fatto"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Gruppo/i di campi"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Seleziona uno o più gruppi di campi..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Seleziona i gruppi di campi da collegare."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Gruppo di campi collegato con successo."
msgstr[1] "Gruppi di campi collegati con successo."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:342
#: includes/admin/post-types/admin-taxonomies.php:348
msgctxt "post status"
msgid "Registration Failed"
msgstr "Registrazione fallita."

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Questo elemento non può essere registrato perché la sua chiave è già in uso "
"da un altro elemento registrato da un altro plugin o dal tema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Autorizzazioni"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibilità"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etichette"

#: includes/admin/post-types/admin-field-group.php:278
msgid "Field Settings Tabs"
msgstr "Schede delle impostazioni del campo"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Valore dello shortcode ACF disabilitato per l'anteprima]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:571
msgid "Close Modal"
msgstr "Chiudi modal"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field moved to other group"
msgstr "Campo spostato in altro gruppo"

#: includes/assets.php:351
msgid "Close modal"
msgstr "Chiudi modale"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Inizia un nuovo gruppo di schede in questa scheda."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nuovo gruppo schede"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Usa un checkbox stilizzato con select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Salva la scelta \"Altro\""

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Consenti la scelta \"Altro\""

#: includes/fields/class-acf-field-checkbox.php:433
msgid "Add Toggle All"
msgstr "Aggiungi mostra/nascondi tutti"

#: includes/fields/class-acf-field-checkbox.php:392
msgid "Save Custom Values"
msgstr "Salva valori personalizzati"

#: includes/fields/class-acf-field-checkbox.php:381
msgid "Allow Custom Values"
msgstr "Consenti valori personalizzati"

#: includes/fields/class-acf-field-checkbox.php:147
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"I valori personalizzati del checkbox non possono essere vuoti. Deseleziona "
"tutti i valori vuoti."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Aggiornamenti"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logo Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Salva le modifiche"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Titolo gruppo di campi"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Aggiungi titolo"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Nuovo in ACF? Dai un'occhiata alla nostra <a href=\"%s\" "
"target=\"_blank\">guida per iniziare</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Aggiungi un nuovo gruppo di campi"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF utilizza <a href=\"%s\" target=\"_blank\">i gruppi di campi</a> per "
"raggruppare i campi personalizzati ed aggiungerli alle schermate di modifica."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Aggiungi il tuo primo gruppo di campi"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Pagine opzioni"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Blocchi ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Campo galleria"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Campo contenuto flessibile"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Campo ripetitore"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Sblocca funzionalità aggiuntive con ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Elimina gruppo di campi"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Creato il %1$s alle %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Impostazioni gruppo"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Regole di posizionamento"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Scegli tra più di 30 tipologie di campo. <a href=\"%s\" "
"target=\"_blank\">Scopri di più</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Inizia a creare nuovi campi personalizzati per i tuoi articoli, le tue "
"pagine, i tuoi post type personalizzati e altro contenuto di WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Aggiungi il tuo primo campo"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Aggiungi campo"

#: includes/acf-field-group-functions.php:496 includes/fields.php:385
msgid "Presentation"
msgstr "Presentazione"

#: includes/fields.php:384
msgid "Validation"
msgstr "Validazione"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:383
msgid "General"
msgstr "Generale"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importa JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Esporta come JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:356
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Gruppo di campi disattivato."
msgstr[1] "%s gruppi di campi disattivati."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:349
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Gruppo di campi attivato."
msgstr[1] "%s gruppi di campi attivati."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Disattiva"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Disattiva questo elemento"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Attiva"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Attiva questo elemento"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Spostare il gruppo di campi nel cestino?"

#: acf.php:561 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inattivo"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:619
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields e Advanced Custom Fields PRO non dovrebbero essere "
"attivi contemporaneamente. Abbiamo automaticamente disattivato Advanced "
"Custom Fields PRO."

#: acf.php:617
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields e Advanced Custom Fields PRO non dovrebbero essere "
"attivi contemporaneamente. Abbiamo automaticamente disattivato Advanced "
"Custom Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s deve aver un utente con il ruolo %2$s."
msgstr[1] "%1$s deve aver un utente con uno dei seguenti ruoli: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s deve avere un ID utente valido."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Richiesta non valida."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s non è uno di %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s deve avere il termine %2$s."
msgstr[1] "%1$s deve avere uno dei seguenti termini: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s deve essere di tipo %2$s."
msgstr[1] "%1$s deve essere di uno dei seguenti tipi: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s deve avere un ID articolo valido."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s richiede un ID allegato valido."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Mostra in API REST"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Abilita trasparenza"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Stringa RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Stringa esadecimale"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Aggiorna a Pro"

#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Attivo"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' non è un indirizzo email valido"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valore del colore"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Seleziona il colore predefinito"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Rimuovi colore"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blocchi"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opzioni"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Utenti"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Voci di menu"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widget"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Allegati"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:108
#: includes/admin/post-types/admin-taxonomies.php:83
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Tassonomie"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Articoli"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Ultimo aggiornamento: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Questo articolo non è disponibile per un confronto di differenze."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Parametri del gruppo di campi non validi."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "In attesa del salvataggio"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Salvato"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importa"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Rivedi le modifiche"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Situato in: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Situato in plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Situato in tema: %s"

#: includes/admin/post-types/admin-field-groups.php:231
msgid "Various"
msgstr "Varie"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sincronizza modifiche"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Caricamento differenze"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Verifica modifiche a JSON locale"

#: includes/admin/admin.php:170
msgid "Visit website"
msgstr "Visita il sito web"

#: includes/admin/admin.php:169
msgid "View details"
msgstr "Visualizza i dettagli"

#: includes/admin/admin.php:168
msgid "Version %s"
msgstr "Versione %s"

#: includes/admin/admin.php:167
msgid "Information"
msgstr "Informazioni"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. I professionisti del nostro "
"Help Desk ti assisteranno per problematiche tecniche."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussioni</a>. Abbiamo una community "
"attiva e accogliente nei nostri Community Forum che potrebbe aiutarti a "
"capire come utilizzare al meglio ACF."

#: includes/admin/admin.php:150
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentazione</a>. La nostra estesa "
"documentazione contiene riferimenti e guide per la maggior parte delle "
"situazioni che potresti incontrare."

#: includes/admin/admin.php:147
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Siamo fissati con il supporto, e vogliamo che tu ottenga il meglio dal tuo "
"sito web con ACF. Se incontri difficoltà, ci sono vari posti in cui puoi "
"trovare aiuto:"

#: includes/admin/admin.php:144 includes/admin/admin.php:146
msgid "Help & Support"
msgstr "Aiuto e supporto"

#: includes/admin/admin.php:135
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Utilizza la scheda \"Aiuto e supporto\" per contattarci in caso di necessità "
"di assistenza."

#: includes/admin/admin.php:132
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Prima di creare il tuo primo gruppo di campi, ti raccomandiamo di leggere la "
"nostra guida <a href=\"%s\" target=\"_blank\">Getting started</a> per "
"familiarizzare con la filosofia del plugin e le buone pratiche da adottare."

#: includes/admin/admin.php:130
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Il plugin Advanced Custom Fields fornisce un costruttore visuale di moduli "
"per personalizzare le schermate di modifica di WordPress con campi "
"aggiuntivi, ed un'intuitiva API per la visualizzazione dei campi "
"personalizzati in qualunque file di template di un tema."

#: includes/admin/admin.php:127 includes/admin/admin.php:129
msgid "Overview"
msgstr "Panoramica"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Il tipo di posizione \"%s\" è già registrato."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "La classe \"%s\" non esiste."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce non valido."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Errore nel caricamento del campo."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Errore</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Ruolo utente"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Commento"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato dell'articolo"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Voce del menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Stato dell'articolo"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Posizioni del menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Tassonomia dell'articolo"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pagina figlia (ha un genitore)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Pagina genitore (ha figlie)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Pagina di primo livello (non ha un genitore)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Pagina degli articoli"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Home page"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo di pagina"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Visualizzando il backend"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Visualizzando il frontend"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connesso"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Utente attuale"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Template pagina"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registrati"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Aggiungi / Modifica"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Modulo utente"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Pagina genitore"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Ruolo dell'utente attuale"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Template predefinito"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Template articolo"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria dell'articolo"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tutti i formati di %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Allegato"

#: includes/validation.php:324
msgid "%s value is required"
msgstr "Il valore %s è richiesto"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostra questo campo se"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:386
msgid "Conditional Logic"
msgstr "Logica condizionale"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "e"

#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Local JSON"
msgstr "JSON locale"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Campo clone"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Controlla anche che tutti gli add-on premium (%s) siano aggiornati "
"all'ultima versione."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Questa versione contiene miglioramenti al tuo database e richiede un "
"aggiornamento."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Grazie per aver aggiornato a %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "È necessario aggiornare il database"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Pagina delle opzioni"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:437
msgid "Gallery"
msgstr "Galleria"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:427
msgid "Flexible Content"
msgstr "Contenuto flessibile"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:447
msgid "Repeater"
msgstr "Ripetitore"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Torna a tutti gli strumenti"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se più gruppi di campi appaiono su una schermata di modifica, verranno usate "
"le opzioni del primo gruppo di campi usato (quello con il numero d'ordine "
"più basso)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Seleziona</b> gli elementi per <b>nasconderli</b> dalla schermata di "
"modifica."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Nascondi dalla schermata"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Invia trackback"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Tag"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categorie"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Attributi della pagina"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Formato"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autore"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisioni"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Commenti"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discussione"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Riassunto"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor del contenuto"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Mostrato nell'elenco dei gruppi di campi"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "I gruppi di campi con un valore inferiore appariranno per primi"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "N. ordine"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Sotto i campi"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Sotto le etichette"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Posizione delle istruzioni"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Posizione etichetta"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Di lato"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normale (dopo il contenuto)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "In alto (dopo il titolo)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posizione"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Senza soluzione di continuità (senza metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (metabox WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stile"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipo"

#: includes/admin/post-types/admin-field-groups.php:87
#: includes/admin/post-types/admin-post-types.php:107
#: includes/admin/post-types/admin-taxonomies.php:106
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Chiave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Ordine"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Chiudi campo"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "larghezza"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Attributi del contenitore"

#: includes/fields/class-acf-field.php:317
msgid "Required"
msgstr "﻿Necessario"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Istruzioni"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipo di campo"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Singola parola, nessun spazio. Sottolineatura e trattini consentiti"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nome del campo"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Questo è il nome che sarà visualizzato nella pagina di modifica"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Etichetta del campo"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Elimina"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Elimina il campo"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Sposta"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Sposta il campo in un altro gruppo"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplica il campo"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Modifica il campo"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Trascina per riordinare"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Mostra questo gruppo di campi se"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Nessun aggiornamento disponibile."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aggiornamento del database completato. <a href=\"%s\">Guarda le novità</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Lettura attività di aggiornamento..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Aggiornamento fallito."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Aggiornamento completato."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Aggiornamento dei dati alla versione %s in corso"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"È caldamente raccomandato eseguire il backup del tuo database prima di "
"procedere. Desideri davvero eseguire il programma di aggiornamento ora?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Seleziona almeno un sito da aggiornare."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aggiornamento del database completato. <a href=\"%s\">Ritorna alla bacheca "
"di rete</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Il sito è aggiornato"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"Il sito necessita di un aggiornamento del database dalla versione %1$s alla "
"%2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Sito"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Aggiorna i siti"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"I seguenti siti hanno necessità di un aggiornamento del DB. Controlla quelli "
"che vuoi aggiornare e fai clic su %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Aggiungi gruppo di regole"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un insieme di regole per determinare in quali schermate di modifica "
"saranno usati i campi personalizzati avanzati"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regole"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiato"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copia negli appunti"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Seleziona gli elementi che vorresti esportare e successivamente il metodo di "
"esportazioni. \"Esporta come JSON\" per esportare il tutto in un file .json "
"che potrai poi importare in un'altra installazione di ACF. \"Genera PHP\" "
"per esportare in codice PHP da poter inserire nel tuo tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Seleziona gruppi di campi"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Nessun gruppo di campi selezionato"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Esporta gruppi di campi"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "File di importazione vuoto"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Tipo di file non corretto"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Errore durante il caricamento del file. Riprova."

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Seleziona il file JSON di Advanced Custom Fields che desideri importare. "
"Quando farai clic sul pulsante di importazione sotto, ACF importerà gli "
"elementi in quel file."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importa gruppi di campi"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronizza"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Seleziona %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplica questo elemento"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Supporta"

#: includes/admin/admin.php:379
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentazione"

#: includes/admin/post-types/admin-field-groups.php:86
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/post-types/admin-taxonomies.php:105
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descrizione"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sincronizzazione disponibile"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:370
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Gruppo di campi sincronizzato."
msgstr[1] "%s gruppi di campi sincronizzati."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:363
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Gruppo di campi duplicato."
msgstr[1] "%s gruppi di campi duplicati."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Attivo <span class=\"count\">(%s)</span>"
msgstr[1] "Attivi <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Verifica i siti ed effettua l'aggiornamento"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Aggiorna il database"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Campi personalizzati"

#: includes/admin/post-types/admin-field-group.php:608
msgid "Move Field"
msgstr "Sposta campo"

#: includes/admin/post-types/admin-field-group.php:601
#: includes/admin/post-types/admin-field-group.php:605
msgid "Please select the destination for this field"
msgstr "Seleziona la destinazione per questo campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:567
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Il campo %1$s può essere trovato nel gruppo di campi %2$s"

#: includes/admin/post-types/admin-field-group.php:564
msgid "Move Complete."
msgstr "Spostamento completato."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Attivo"

#: includes/admin/post-types/admin-field-group.php:275
msgid "Field Keys"
msgstr "Chiavi del campo"

#: includes/admin/post-types/admin-field-group.php:179
msgid "Settings"
msgstr "Impostazioni"

#: includes/admin/post-types/admin-field-groups.php:88
msgid "Location"
msgstr "Posizione"

#: includes/admin/post-types/admin-field-group.php:99
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:96
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "copia"

#: includes/admin/post-types/admin-field-group.php:95
msgid "(this field)"
msgstr "(questo campo)"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Checked"
msgstr "Selezionato"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Sposta campo personalizzato"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Nessun campo attiva/disattiva disponibile"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Il titolo del gruppo di campi è necessario"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Questo campo non può essere spostato fino a quando non saranno state salvate "
"le modifiche"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La stringa \"field_\" non può essere usata come inizio nel nome di un campo"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Bozza del gruppo di campi aggiornata."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Gruppo di campi programmato."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Gruppo di campi inviato."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Gruppo di campi salvato."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Gruppo di campi pubblicato."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Gruppo di campi eliminato."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Gruppo di campi aggiornato."

#: includes/admin/admin-tools.php:112
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Strumenti"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "non è uguale a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "è uguale a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Moduli"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Pagina"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Articolo"

#: includes/fields.php:329
msgid "Relational"
msgstr "Relazionale"

#: includes/fields.php:328
msgid "Choice"
msgstr "Scelta"

#: includes/fields.php:326
msgid "Basic"
msgstr "Base"

#: includes/fields.php:277
msgid "Unknown"
msgstr "Sconosciuto"

#: includes/fields.php:277
msgid "Field type does not exist"
msgstr "Il tipo di campo non esiste"

#: includes/forms/form-front.php:219
msgid "Spam Detected"
msgstr "Spam rilevato"

#: includes/forms/form-front.php:102
msgid "Post updated"
msgstr "Articolo aggiornato"

#: includes/forms/form-front.php:101
msgid "Update"
msgstr "Aggiorna"

#: includes/forms/form-front.php:62
msgid "Validate Email"
msgstr "Valida l'email"

#: includes/fields.php:327 includes/forms/form-front.php:54
msgid "Content"
msgstr "Contenuto"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:45
msgid "Title"
msgstr "Titolo"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Modifica gruppo di campi"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is less than"
msgstr "La selezione è minore di"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Selection is greater than"
msgstr "La selezione è maggiore di"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is less than"
msgstr "Il valore è inferiore a"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value is greater than"
msgstr "Il valore è maggiore di"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value contains"
msgstr "Il valore contiene"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value matches pattern"
msgstr "Il valore ha corrispondenza con il pattern"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is not equal to"
msgstr "Il valore non è uguale a"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Value is equal to"
msgstr "Il valore è uguale a"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has no value"
msgstr "Non ha valori"

#: includes/admin/post-types/admin-field-group.php:103
msgid "Has any value"
msgstr "Ha qualsiasi valore"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:350
msgid "Cancel"
msgstr "Annulla"

#: includes/assets.php:346
msgid "Are you sure?"
msgstr "Confermi?"

#. translators: %d is the number of fields that require attention
#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d campi necessitano attenzione"

#: includes/assets.php:368
msgid "1 field requires attention"
msgstr "1 campo richiede attenzione"

#: includes/assets.php:367 includes/validation.php:258
#: includes/validation.php:266
msgid "Validation failed"
msgstr "Validazione fallita"

#: includes/assets.php:366
msgid "Validation successful"
msgstr "Validazione avvenuta con successo"

#: includes/media.php:54
msgid "Restricted"
msgstr "Limitato"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Comprimi dettagli"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Espandi dettagli"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Caricato in questo articolo"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Aggiorna"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Modifica"

#: includes/assets.php:360
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Le modifiche effettuate verranno cancellate se esci da questa pagina"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "La tipologia del file deve essere %s."

#: includes/admin/post-types/admin-field-group.php:97
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "oppure"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "La dimensione del file non deve superare %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "La dimensione del file deve essere di almeno %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "L'altezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "L'altezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "La larghezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "La larghezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(nessun titolo)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Dimensione originale"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Medio"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:94
msgid "(no label)"
msgstr "(nessuna etichetta)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Imposta l'altezza dell'area di testo"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Righe"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Area di testo"

#: includes/fields/class-acf-field-checkbox.php:434
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Anteponi un checkbox aggiuntivo per poter selezionare/deselzionare tutte le "
"opzioni"

#: includes/fields/class-acf-field-checkbox.php:396
msgid "Save 'custom' values to the field's choices"
msgstr "Salva i valori 'personalizzati' per le scelte del campo"

#: includes/fields/class-acf-field-checkbox.php:385
msgid "Allow 'custom' values to be added"
msgstr "Consenti l'aggiunta di valori 'personalizzati'"

#: includes/fields/class-acf-field-checkbox.php:48
msgid "Add new choice"
msgstr "Aggiungi nuova scelta"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Seleziona tutti"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Consenti URL degli archivi"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archivi"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Link pagina"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Aggiungi"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nome"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s aggiunti"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s esiste già"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "L'utente non può aggiungere %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID del termine"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Oggetto termine"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Carica valori dai termini dell'articolo"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Carica termini"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Collega i termini selezionati all'articolo"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Salva i termini"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Abilita la creazione di nuovi termini in fase di modifica"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Crea termini"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Pulsanti radio"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Valore singolo"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Selezione multipla"

#: includes/fields/class-acf-field-checkbox.php:35
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Valori multipli"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Seleziona l'aspetto di questo campo"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Aspetto"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Seleziona la tassonomia da visualizzare"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Nessun %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Il valore deve essere uguale o inferiore a %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Il valore deve essere uguale o superiore a %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Il valore deve essere un numero"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Numero"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Salvare gli 'altri' valori nelle scelte del campo"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Aggiungi scelta 'altro' per consentire valori personalizzati"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Altro"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Radio button"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definisce il punto di chiusura del precedente accordion. Questo accordion "
"non sarà visibile."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Consenti a questo accordion di essere aperto senza chiudere gli altri."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Espansione multipla"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Mostra questo accordion aperto l caricamento della pagina."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Apri"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Fisarmonica"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Limita quali tipi di file possono essere caricati"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID del file"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL del file"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array di file"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Aggiungi un file"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Nessun file selezionato"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nome del file"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Aggiorna il file"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Modifica il file"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Seleziona il file"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "File"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Password"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Specifica il valore restituito"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per il caricamento differito delle opzioni?"

#: includes/fields/class-acf-field-checkbox.php:346
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Inserisci ogni valore predefinito in una nuova riga"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Selezionare"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Caricamento non riuscito"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Ricerca in corso&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Caricamento di altri risultati in corso&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Puoi selezionare solo %d elementi"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Puoi selezionare solo 1 elemento"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Elimina %d caratteri"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Elimina 1 carattere"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "﻿Inserisci %d o più caratteri"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Inserisci 1 o più caratteri"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nessuna corrispondenza trovata"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d risultati disponibili, usa i tasti freccia su e giù per scorrere."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un risultato disponibile. Premi Invio per selezionarlo."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Selezione"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID dell'utente"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Oggetto utente"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Array di utenti"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Tutti i ruoli utente"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtra per ruolo"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Utente"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separatore"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Seleziona il colore"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Predefinito"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Rimuovi"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Selettore colore"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Adesso"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso orario"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecondo"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisecondo"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Secondo"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Ora"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Orario"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Scegli l'orario"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Selettore data/ora"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Allineato a sinistra"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Allineato in alto"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Posizionamento"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Scheda"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Il valore deve essere un URL valido"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL del link"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Array di link"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Apri in una nuova scheda/finestra"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Seleziona il link"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Dimensione step"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valore massimo"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valore minimo"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Intervallo"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:363
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Entrambi (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Etichetta"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Valore"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:424
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Verticale"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:425
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Orizzontale"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "rosso : Rosso"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per un maggiore controllo, puoi specificare sia un valore che un'etichetta "
"in questo modo:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Inserisci ogni scelta su una nuova linea."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:335
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Scelte"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Gruppo di pulsanti"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Consenti valore nullo"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Genitore"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""
"TinyMCE non sarà inizializzato fino a quando non verrà fatto clic sul campo"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Ritarda inizializzazione"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Mostra pulsanti per il caricamento di media"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barra degli strumenti"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Solo testo"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Solo visuale"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visuale e testo"

#: includes/fields/class-acf-field-icon_picker.php:269
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Schede"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Fare clic per inizializzare TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visuale"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Il valore non può superare i %d caratteri"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Lasciare vuoto per nessun limite"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Limite di caratteri"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Appare dopo il campo di input"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Postponi"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Appare prima del campo di input"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Anteponi"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Appare all'interno del campo di input"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Testo segnaposto"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Appare quando si crea un nuovo articolo"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s richiede la selezione di almeno %2$s elemento"
msgstr[1] "%1$s richiede la selezione di almeno %2$s elementi"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID dell'articolo"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Oggetto articolo"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Numero massimo di articoli"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Numero minimo di articoli"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Gli elementi selezionati saranno visualizzati in ogni risultato"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Tassonomia"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Post type"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtri"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Tutte le tassonomie"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtra per tassonomia"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Tutti i tipi di articolo"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtra per tipo di articolo"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Cerca..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Seleziona tassonomia"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Seleziona tipo di articolo"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Nessuna corrispondenza trovata"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Caricamento in corso..."

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Numero massimo di valori raggiunto ( {max} valori )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relazione"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista con valori separati da virgole. Lascia vuoto per tutti i tipi"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Tipi di file consentiti"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Massimo"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Dimensioni del file"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Limita quali immagini possono essere caricate"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimo"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Caricato nell'articolo"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tutti"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limitare la scelta dalla libreria media"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Libreria"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Dimensioni dell'anteprima"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID dell'immagine"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL dell'immagine"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Array di immagini"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:356
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Specificare il valore restituito sul front-end"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:355
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Valore di ritorno"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Aggiungi un'immagine"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Nessuna immagine selezionata"

#: includes/assets.php:349 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Rimuovi"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Modifica"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Tutte le immagini"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Aggiorna l'immagine"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Modifica l'immagine"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Seleziona un'immagine"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Immagine"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Consenti al markup HTML di essere visualizzato come testo visibile anziché "
"essere processato"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Effettua escape HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Nessuna formattazione"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Aggiungi automaticamente &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Aggiungi automaticamente paragrafi"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controlla come sono rese le nuove righe"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nuove righe"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:210
msgid "Week Starts On"
msgstr "La settimana comincia di"

#: includes/fields/class-acf-field-date_picker.php:192
msgid "The format used when saving a value"
msgstr "Il formato utilizzato durante il salvataggio di un valore"

#: includes/fields/class-acf-field-date_picker.php:191
msgid "Save Format"
msgstr "Formato di salvataggio"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sett"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prec"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Succ"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Oggi"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Selettore data"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:240
msgid "Width"
msgstr "Larghezza"

#: includes/fields/class-acf-field-oembed.php:237
#: includes/fields/class-acf-field-oembed.php:249
msgid "Embed Size"
msgstr "Dimensione oggetto incorporato"

#: includes/fields/class-acf-field-oembed.php:197
msgid "Enter URL"
msgstr "Inserisci URL"

#: includes/fields/class-acf-field-oembed.php:21
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Testo mostrato quando inattivo"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Testo Off"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Testo visualizzato quando attivo"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Testo On"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Interfaccia Utente stilizzata"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valore predefinito"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Visualizza il testo accanto al checkbox"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Messaggio"

#: includes/assets.php:348 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "No"

#: includes/assets.php:347 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Sì"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Vero / Falso"

#: includes/fields/class-acf-field-group.php:414
msgid "Row"
msgstr "Riga"

#: includes/fields/class-acf-field-group.php:413
msgid "Table"
msgstr "Tabella"

#: includes/admin/post-types/admin-field-group.php:157
#: includes/fields/class-acf-field-group.php:412
msgid "Block"
msgstr "Blocco"

#: includes/fields/class-acf-field-group.php:407
msgid "Specify the style used to render the selected fields"
msgstr ""
"Specifica lo stile utilizzato per la visualizzazione dei campi selezionati"

#: includes/fields.php:331 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:418
#: includes/fields/class-acf-field-group.php:406
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:390
msgid "Sub Fields"
msgstr "Sottocampi"

#: includes/fields/class-acf-field-group.php:21
msgid "Group"
msgstr "Gruppo"

#: includes/fields/class-acf-field-google-map.php:221
msgid "Customize the map height"
msgstr "Personalizza l'altezza della mappa"

#: includes/fields/class-acf-field-google-map.php:220
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:252
msgid "Height"
msgstr "Altezza"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Set the initial zoom level"
msgstr "Imposta il livello iniziale dello zoom"

#: includes/fields/class-acf-field-google-map.php:208
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center the initial map"
msgstr "Centra la mappa iniziale"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:194
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:153
msgid "Search for address..."
msgstr "Cerca per indirizzo..."

#: includes/fields/class-acf-field-google-map.php:150
msgid "Find current location"
msgstr "Trova posizione corrente"

#: includes/fields/class-acf-field-google-map.php:149
msgid "Clear location"
msgstr "Rimuovi posizione"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Cerca"

#: includes/fields/class-acf-field-google-map.php:56
msgid "Sorry, this browser does not support geolocation"
msgstr "Purtroppo questo browser non supporta la geolocalizzazione"

#: includes/fields/class-acf-field-google-map.php:21
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:203
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Il formato restituito tramite funzioni template"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:202
#: includes/fields/class-acf-field-date_time_picker.php:190
#: includes/fields/class-acf-field-icon_picker.php:292
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Formato di ritorno"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personalizzato:"

#: includes/fields/class-acf-field-date_picker.php:173
#: includes/fields/class-acf-field-date_time_picker.php:173
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Il formato visualizzato durante la modifica di un articolo"

#: includes/fields/class-acf-field-date_picker.php:172
#: includes/fields/class-acf-field-date_time_picker.php:172
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Formato di visualizzazione"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Selettore orario"

#. translators: counts for inactive field groups
#: acf.php:567
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Non attivo <span class=\"count\">(%s)</span>"
msgstr[1] "Non attivi <span class=\"count\">(%s)</span>"

#: acf.php:528
msgid "No Fields found in Trash"
msgstr "Nessun campo trovato nel Cestino"

#: acf.php:527
msgid "No Fields found"
msgstr "Nessun campo trovato"

#: acf.php:526
msgid "Search Fields"
msgstr "Cerca campi"

#: acf.php:525
msgid "View Field"
msgstr "Visualizza campo"

#: acf.php:524 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nuovo campo"

#: acf.php:523
msgid "Edit Field"
msgstr "Modifica campo"

#: acf.php:522
msgid "Add New Field"
msgstr "Aggiungi nuovo campo"

#: acf.php:520
msgid "Field"
msgstr "Campo"

#: acf.php:519 includes/admin/post-types/admin-field-group.php:178
#: includes/admin/post-types/admin-field-groups.php:89
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Campi"

#: acf.php:494
msgid "No Field Groups found in Trash"
msgstr "Nessun gruppo di campi trovato nel cestino"

#: acf.php:493
msgid "No Field Groups found"
msgstr "Nessun gruppo di campi trovato"

#: acf.php:492
msgid "Search Field Groups"
msgstr "Cerca gruppo di campi"

#: acf.php:491
msgid "View Field Group"
msgstr "Visualizza gruppo di campi"

#: acf.php:490
msgid "New Field Group"
msgstr "Nuovo gruppo di campi"

#: acf.php:489
msgid "Edit Field Group"
msgstr "Modifica gruppo di campi"

#: acf.php:488
msgid "Add New Field Group"
msgstr "Aggiungi nuovo gruppo di campi"

#: acf.php:487 acf.php:521
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Aggiungi nuovo"

#: acf.php:486
msgid "Field Group"
msgstr "Gruppo di campi"

#: acf.php:485 includes/admin/post-types/admin-field-groups.php:52
#: includes/admin/post-types/admin-post-types.php:109
#: includes/admin/post-types/admin-taxonomies.php:108
msgid "Field Groups"
msgstr "Gruppi di campi"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personalizza WordPress con campi potenti, professionali e intuitivi."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:331
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Opzioni Aggiornate"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "Ricontrollare"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Pubblica"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nessun Field Group personalizzato trovato in questa Pagina Opzioni. <a "
"href=\"%s\">Crea un Field Group personalizzato</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Errore</b>.Impossibile connettersi al server di aggiornamento"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Selezionare uno o più campi che si desidera clonare"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Visualizza"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Specificare lo stile utilizzato per il rendering del campo clona"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppo (Visualizza campi selezionati in un gruppo all'interno di questo "
"campo)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Senza interruzione (sostituisce questo campo con i campi selezionati)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Etichette verranno visualizzate come %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefisso Etichetta Campo"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "I valori verranno salvati come %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefisso Nomi Campo"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Campo sconosciuto"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Field Group sconosciuto"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tutti i campi dal %s field group"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Aggiungi Riga"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layout"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Questo campo richiede almeno {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponibile (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} richiesto (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexible Content richiede almeno 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clicca il bottone \"%s\" qui sotto per iniziare a creare il layout"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Rimuovi Layout"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Clicca per alternare"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Cancella Layout"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplica Layout"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Aggiungi Nuovo Layout"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Layout Minimi"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Layout Massimi"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Etichetta Bottone"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Aggiungi Immagine alla Galleria"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Selezione massima raggiunta"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Lunghezza"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Didascalia"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Testo Alt"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Aggiungi a Galleria"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Azioni in blocco"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ordina per aggiornamento data"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ordina per data modifica"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ordina per titolo"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Ordine corrente inversa"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Chiudi"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Seleziona Minima"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Seleziona Massima"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Tipologie File permesse"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Inserisci"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Specificare dove vengono aggiunti nuovi allegati"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Aggiungere alla fine"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Anteporre all'inizio"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Righe minime raggiunte ({min} righe)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Righe massime raggiunte ({max} righe)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Righe Minime"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Righe Massime"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Collassata"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Selezionare un campo secondario da visualizzare quando la riga è collassata"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Trascinare per riordinare"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Aggiungi riga"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Rimuovi riga"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Pagina Principale"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Pagina Post"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Pagina Principale"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Pagina Post"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Nessuna Pagina Opzioni esistente"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Disattivare Licenza"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Attiva Licenza"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informazioni Licenza"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per sbloccare gli aggiornamenti, si prega di inserire la chiave di licenza "
"qui sotto. Se non hai una chiave di licenza, si prega di vedere <a "
"href=\"%s\" target=\"_blank\">Dettagli e prezzi</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Chiave di licenza"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informazioni di aggiornamento"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versione corrente"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Ultima versione"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aggiornamento Disponibile"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Avviso di Aggiornamento"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Inserisci il tuo codice di licenza per sbloccare gli aggiornamenti"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Aggiorna Plugin"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""

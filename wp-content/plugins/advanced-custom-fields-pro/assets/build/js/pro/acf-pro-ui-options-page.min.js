(()=>{var e={805:()=>{!function(e){const t=function(t){return void 0===t.element?t:t.children&&"None"===t.text?void 0:("acfOptionsPages"===t.text&&(t.text=acf.__("Options Pages")),e('<span class="acf-selection"></span>').data("element",t.element).html(acf.strEscape(t.text)))},a=function(t){if(void 0===t.element)return t;const a=e('<span class="acf-selection"></span>');return a.html(acf.strEscape(t.element.innerHTML)),"options"!==t.id&&"edit_posts"!==t.id||a.append('<span class="acf-select2-default-pill">'+acf.__("Default")+"</span>"),a.data("element",t.element),a};new acf.Model({id:"UIOptionsPageManager",wait:"ready",events:{"change .acf-options-page-parent_slug":"toggleMenuPositionDesc"},initialize:function(){"ui_options_page"===acf.get("screen")&&(acf.newSelect2(e("select.acf-options-page-parent_slug"),{field:!1,templateSelection:t,templateResult:t,dropdownCssClass:"field-type-select-results"}),acf.newSelect2(e("select.acf-options-page-capability"),{field:!1,templateSelection:a,templateResult:a}),acf.newSelect2(e("select.acf-options-page-data_storage"),{field:!1,templateSelection:a,templateResult:a}),this.toggleMenuPositionDesc())},toggleMenuPositionDesc:function(t,a){"none"===e("select.acf-options-page-parent_slug").val()?(e(".acf-menu-position-desc-child").hide(),e(".acf-menu-position-desc-parent").show()):(e(".acf-menu-position-desc-parent").hide(),e(".acf-menu-position-desc-child").show())}}),new acf.Model({id:"optionsPageModalManager",events:{"change .location-rule-value":"createOptionsPage"},createOptionsPage:function(a){const n=e(a.target);if("add_new_options_page"!==n.val())return;let o=!1;const s=function(a){o=acf.newPopup({title:a.data.title,content:a.data.content,width:"600px"}),o.$el.addClass("acf-create-options-page-popup");const n=o.$el.find("#acf_ui_options_page-page_title"),s=n.val();n.focus().val("").val(s),acf.newSelect2(e("#acf_ui_options_page-parent_slug"),{field:!1,templateSelection:t,templateResult:t,dropdownCssClass:"field-type-select-results"}),o.on("submit","form",c)},c=function(t){t.preventDefault(),acf.validateForm({form:e("#acf-create-options-page-form"),success:i,failure:l})},i=function(){const t=e("#acf-create-options-page-form").serializeArray(),a={action:"acf/create_options_page"};t.forEach(e=>{a[e.name]=e.value}),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",success:p})},l=function(t){const a=e("#acf-create-options-page-form"),n=a.find(".acf-field .acf-error-message");a.find(".acf-notice").first().remove(),n.each(function(){const t=e(this).closest(".acf-field").find(".acf-label:first");e(this).attr("class","acf-options-page-modal-error").appendTo(t)})},p=function(e){e.success&&e.data.menu_slug?(n.prepend('<option value="'+e.data.menu_slug+'">'+e.data.page_title+"</option>"),n.val(e.data.menu_slug),o.close()):!e.success&&e.data.error&&alert(e.data.error)};!function(){const t=e(".acf-headerbar-title-field").val(),a={action:"acf/create_options_page",acf_parent_page_choices:this.acf.data.optionPageParentOptions?this.acf.data.optionPageParentOptions:[]};t.length&&(a.field_group_title=t),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",success:s})}()}})}(jQuery)}},t={};function a(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,a),s.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";a(805)})()})();
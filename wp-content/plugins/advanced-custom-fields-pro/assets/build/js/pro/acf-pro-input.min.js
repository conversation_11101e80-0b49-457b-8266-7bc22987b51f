(()=>{var t={327:()=>{!function(t){var e=acf.Field.extend({type:"repeater",wait:"",page:1,nextRowNum:0,events:{'click a[data-event="add-row"]':"onClickAdd",'click a[data-event="duplicate-row"]':"onClickDuplicate",'click a[data-event="remove-row"]':"onClickRemove",'click a[data-event="collapse-row"]':"onClickCollapse",'click a[data-event="first-page"]:not(.disabled)':"onClickFirstPage",'click a[data-event="last-page"]:not(.disabled)':"onClickLastPage",'click a[data-event="prev-page"]:not(.disabled)':"onClickPrevPage",'click a[data-event="next-page"]:not(.disabled)':"onClickNextPage","change .current-page":"onChangeCurrentPage","click .acf-order-input-wrap":"onClickRowOrder","blur .acf-order-input":"onBlurRowOrder","change .acf-order-input":"onChangeRowOrder","changed:total_rows":"onChangeTotalRows",showField:"onShow",unloadField:"onUnload",mouseover:"onHover",change:"onChangeField"},$control:function(){return this.$(".acf-repeater:first")},$table:function(){return this.$("table:first")},$tbody:function(){return this.$("tbody:first")},$rows:function(){return this.$("tbody:first > tr").not(".acf-clone, .acf-deleted")},$row:function(t){return this.$("tbody:first > tr:eq("+t+")")},$clone:function(){return this.$("tbody:first > tr.acf-clone")},$actions:function(){return this.$(".acf-actions:last")},$button:function(){return this.$(".acf-actions:last .button")},$firstPageButton:function(){return this.$(".acf-tablenav:last .first-page")},$prevPageButton:function(){return this.$(".acf-tablenav:last .prev-page")},$nextPageButton:function(){return this.$(".acf-tablenav:last .next-page")},$lastPageButton:function(){return this.$(".acf-tablenav:last .last-page")},$pageInput:function(){return this.$(".current-page:last")},totalPages:function(){const t=this.$(".acf-total-pages:last").text();return parseInt(t)},getValue:function(){return this.$rows().length},allowRemove:function(){let t=this.val(),e=parseInt(this.get("min"));return this.get("pagination")&&(t=this.get("total_rows")),!e||e<t},allowAdd:function(){let t=this.val(),e=parseInt(this.get("max"));return this.get("pagination")&&(t=this.get("total_rows")),!e||e>t},addSortable:function(t){1!=this.get("max")&&(this.get("pagination")||this.$tbody().sortable({items:"> tr",handle:"> td.order",zIndex:9999,forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:function(e,a){t.render()},update:function(e,a){t.$input().trigger("change")}}))},addCollapsed:function(){var e=a.load(this.get("key"));if(!e)return!1;this.$rows().each(function(a){e.indexOf(a)>-1&&t(this).find(".-collapsed-target").length&&t(this).addClass("-collapsed")})},addUnscopedEvents:function(e){this.on("invalidField",".acf-row",function(a){var i=t(this);e.isCollapsed(i)&&e.expand(i)}),this.get("pagination")&&this.on("change","input, select, textarea",function(a){const i=t(a.currentTarget);i.hasClass("acf-order-input")||i.hasClass("acf-row-status")||e.onChangeField(a,t(this))}),this.listenForSavedMetaBoxes()},initialize:function(){this.addUnscopedEvents(this),this.addCollapsed(),acf.disable(this.$clone(),this.cid),this.get("pagination")&&(this.nextRowNum=this.get("total_rows")),this.render()},render:function(e=!0){e&&this.$rows().each(function(e){t(this).find("> .order > span").html(e+1)});var a=this.$control(),i=this.$button();0==this.val()?a.addClass("-empty"):a.removeClass("-empty"),this.allowAdd()?(a.removeClass("-max"),i.removeClass("disabled")):(a.addClass("-max"),i.addClass("disabled")),this.get("pagination")&&this.maybeDisablePagination()},listenForSavedMetaBoxes:function(){if(!acf.isGutenbergPostEditor()||!this.get("pagination"))return;let t=!0;wp.data.subscribe(()=>{wp.data.select("core/edit-post").isSavingMetaBoxes()?t=!1:t||(t=!0,this.set("total_rows",0,!0),this.ajaxLoadPage(!0))})},incrementTotalRows:function(){let t=this.get("total_rows");this.set("total_rows",++t,!0)},decrementTotalRows:function(){let t=this.get("total_rows");this.set("total_rows",--t,!0)},validateAdd:function(){if(this.allowAdd())return!0;var t=this.get("max"),e=acf.__("Maximum rows reached ({max} rows)");return e=e.replace("{max}",t),this.showNotice({text:e,type:"warning"}),!1},onClickAdd:function(t,e){if(!this.validateAdd())return!1;e.hasClass("acf-icon")?this.add({before:e.closest(".acf-row")}):this.add()},add:function(t){if(!this.allowAdd())return!1;t=acf.parseArgs(t,{before:!1});var e=acf.duplicate({target:this.$clone(),append:this.proxy(function(e,a){t.before?t.before.before(a):e.before(a),a.removeClass("acf-clone"),acf.enable(a,this.cid)})});if(this.get("pagination")){if(this.incrementTotalRows(),!1!==t.before){let a=parseInt(t.before.find(".acf-row-number").first().text())||0;if(!a||t.before.hasClass("acf-inserted")||t.before.hasClass("acf-added")||--a,t.before.hasClass("acf-divider")&&(t.before.removeClass("acf-divider"),e.addClass("acf-divider")),this.updateRowStatus(e,"inserted"),this.updateRowStatus(e,"reordered",a),e.find(".acf-row-number").first().hide().text(a),!e.find(".acf-order-input-wrap").hasClass("disabled")){let t=acf.__("Order will be assigned upon save");e.find(".acf-order-input-wrap").addClass("disabled"),e.find(".acf-row-number").first().after('<span title="'+t+'">-</span>')}e.find(".acf-order-input").first().hide(),e.attr("data-inserted",a)}else this.nextRowNum++,e.find(".acf-order-input").first().val(this.nextRowNum),e.find(".acf-row-number").first().text(this.nextRowNum),this.updateRowStatus(e,"added"),this.$tbody().find(".acf-divider").length||e.addClass("acf-divider");e.find(".acf-input:first").find("input:not([type=hidden]), select, textarea").first().trigger("focus")}return this.render(),this.$input().trigger("change"),e},onClickDuplicate:function(t,e){if(!this.validateAdd())return!1;var a=e.closest(".acf-row");this.duplicateRow(a)},duplicateRow:function(t){if(!this.allowAdd())return!1;var e=this.get("key"),a=acf.duplicate({target:t,rename:function(t,a,i,n){return"id"===t||"for"===t?a.replace(e+"-"+i,e+"-"+n):a.replace(e+"]["+i,e+"]["+n)},before:function(t){acf.doAction("unmount",t)},after:function(t,e){acf.doAction("remount",t)}});if(this.get("pagination")){this.incrementTotalRows();const e=parseInt(t.find(".acf-row-number").first().text())||0;if(this.updateRowStatus(a,"inserted"),this.updateRowStatus(a,"reordered",e),a.find(".acf-row-number").first().hide(),!a.find(".acf-order-input-wrap").hasClass("disabled")){let t=acf.__("Order will be assigned upon save");a.find(".acf-order-input-wrap").addClass("disabled"),a.find(".acf-row-number").first().after('<span title="'+t+'">-</span>')}a.find(".acf-order-input").first().hide(),a.attr("data-inserted",e),a.removeClass("acf-divider")}return this.$input().trigger("change"),this.render(),acf.focusAttention(a),a},validateRemove:function(){if(this.allowRemove())return!0;var t=this.get("min"),e=acf.__("Minimum rows not reached ({min} rows)");return e=e.replace("{min}",t),this.showNotice({text:e,type:"warning"}),!1},onClickRemove:function(t,e){var a=e.closest(".acf-row");if(t.shiftKey)return this.remove(a);a.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:e,context:this,confirm:function(){this.remove(a)},cancel:function(){a.removeClass("-hover")}})},onClickRowOrder:function(t,e){this.get("pagination")&&(e.hasClass("disabled")||(e.find(".acf-row-number").hide(),e.find(".acf-order-input").show().trigger("select")))},onBlurRowOrder:function(t,e){this.onChangeRowOrder(t,e,!1)},onChangeRowOrder:function(t,e,a=!0){if(!this.get("pagination"))return;const i=e.closest(".acf-row"),n=i.find(".acf-row-number").first();let o=e.val();if(i.find(".acf-order-input").first().hide(),!acf.isNumeric(o)||parseFloat(o)<0)return void n.show();o=Math.round(o);const s=o-1;e.val(o),n.text(o).show(),a&&this.updateRowStatus(i,"reordered",s)},onChangeTotalRows:function(){const t=parseInt(this.get("per_page"))||20,e=parseInt(this.get("total_rows"))||0,a=Math.ceil(e/t);this.$(".acf-total-pages:last").text(a),this.nextRowNum=e,this.page>a&&(this.page=a,this.ajaxLoadPage())},remove:function(t){const e=this;if(this.get("pagination")){if(this.decrementTotalRows(),t.data("id").includes("row-"))return this.updateRowStatus(t,"deleted"),t.hide(),e.$input().trigger("change"),void e.render(!1);t.hasClass("acf-divider")&&t.next(".acf-added").addClass("acf-divider")}acf.remove({target:t,endHeight:0,complete:function(){e.$input().trigger("change"),e.render()}})},isCollapsed:function(t){return t.hasClass("-collapsed")},collapse:function(t){t.addClass("-collapsed"),acf.doAction("hide",t,"collapse")},expand:function(t){t.removeClass("-collapsed"),acf.doAction("show",t,"collapse")},onClickCollapse:function(t,e){var a=e.closest(".acf-row"),i=this.isCollapsed(a);t.shiftKey&&(a=this.$rows()),i?this.expand(a):this.collapse(a)},onShow:function(t,e,a){var i=acf.getFields({is:":visible",parent:this.$el});acf.doAction("show_fields",i)},onUnload:function(){var e=[];this.$rows().each(function(a){t(this).hasClass("-collapsed")&&e.push(a)}),e=e.length?e:null,a.save(this.get("key"),e)},onHover:function(){this.addSortable(this),this.off("mouseover")},onChangeField:function(e,a){const i=t(e.delegateTarget);let n=a.closest(".acf-row");n.closest(".acf-field-repeater").data("key")!==i.data("key")&&(n=n.parent().closest(".acf-row")),this.updateRowStatus(n,"changed")},updateRowStatus:function(t,e,a=!0){if(!this.get("pagination"))return;const i=t.parents(".acf-field-repeater").data("key");if(this.parent()&&i!==this.get("key"))return;const n=t.data("id"),o=`${this.$el.find(".acf-repeater-hidden-input:first").attr("name")}[${n}][acf_${e}]`,s=`<input type="hidden" class="acf-row-status" name="${o}" value="${a}" />`;t.hasClass("acf-"+e)||t.addClass("acf-"+e);const l=t.find(`input[name='${o}']`);l.length?l.val(a):t.find("td").first().append(s)},onClickFirstPage:function(){this.validatePage(1)},onClickPrevPage:function(){this.validatePage(this.page-1)},onClickNextPage:function(t){this.validatePage(this.page+1)},onClickLastPage:function(){this.validatePage(this.totalPages())},onChangeCurrentPage:function(){this.validatePage(this.$pageInput().val())},maybeDisablePagination:function(){this.$actions().find(".acf-nav").removeClass("disabled"),this.page<=1&&(this.$firstPageButton().addClass("disabled"),this.$prevPageButton().addClass("disabled")),this.page>=this.totalPages()&&(this.$nextPageButton().addClass("disabled"),this.$lastPageButton().addClass("disabled"))},validatePage:function(t){const e=this;acf.validateForm({form:this.$control(),event:"",reset:!0,success:function(a){e.page=t,e.page<=1&&(e.page=1),e.page>=e.totalPages()&&(e.page=e.totalPages()),e.ajaxLoadPage()},failure:function(t){return e.$pageInput().val(e.page),!1}})},ajaxLoadPage:function(e=!1){const a=acf.prepareForAjax({action:"acf/ajax/query_repeater",paged:this.page,field_key:this.get("key"),field_name:this.get("orig_name"),field_prefix:this.get("prefix"),rows_per_page:parseInt(this.get("per_page")),refresh:e,nonce:this.get("nonce")});t.ajax({url:ajaxurl,method:"POST",dataType:"json",data:a,context:this}).done(function(a){const{rows:i}=a.data,n=this.$tbody().find("> tr");n.not(".acf-clone").hide(),e?(n.not(".acf-clone").remove(),this.set("total_rows",a.data.total_rows,!1)):n.not(".acf-changed, .acf-deleted, .acf-reordered, .acf-added, .acf-inserted, .acf-clone").remove(),Object.keys(i).forEach(e=>{let a=!1,n=this.$tbody().find("> *[data-id=row-"+e+"]"),o=this.$tbody().find("> *[data-inserted="+e+"]");if(o.length&&(o.appendTo(this.$tbody()).show(),acf.doAction("remount",o)),!n.hasClass("acf-deleted")){if(n.length)return acf.doAction("unmount",n),n.appendTo(this.$tbody()).show(),void acf.doAction("remount",n);a=t(i[e]),this.$tbody().append(a).show(),acf.doAction("remount",a),this.$clone().appendTo(this.$tbody())}});const o=this.$tbody().find(".acf-added:hidden");if(o.length){const e=this;o.each(function(){const a=t(this);a.insertBefore(e.$clone()).show(),acf.doAction("remount",a)})}this.$pageInput().val(this.page),this.maybeDisablePagination()}).fail(function(t,e,a){const i=acf.getXhrError(t);let n=acf.__("Error loading page");""!==i&&(n=`${n}: ${i}`),this.showNotice({text:n,type:"warning"})})}});acf.registerFieldType(e),acf.registerConditionForFieldType("hasValue","repeater"),acf.registerConditionForFieldType("hasNoValue","repeater"),acf.registerConditionForFieldType("lessThan","repeater"),acf.registerConditionForFieldType("greaterThan","repeater");var a=new acf.Model({name:"this.collapsedRows",key:function(t,e){var a=this.get(t+e)||0;return a++,this.set(t+e,a,!0),a>1&&(t+="-"+a),t},load:function(t){t=this.key(t,"load");var e=acf.getPreference(this.name);return!(!e||!e[t])&&e[t]},save:function(e,a){e=this.key(e,"save");var i=acf.getPreference(this.name)||{};null===a?delete i[e]:i[e]=a,t.isEmptyObject(i)&&(i=null),acf.setPreference(this.name,i)}})}(jQuery)},340:()=>{!function(t){var e=acf.Field.extend({type:"flexible_content",wait:"",events:{'click [data-name="add-layout"]':"onClickAdd",'click [data-name="duplicate-layout"]':"onClickDuplicate",'click [data-name="collapse-layout"]':"onClickCollapse",'click [data-name="more-layout-actions"]':"onClickMoreActions","click .acf-fc-expand-all":"onClickExpandAll","click .acf-fc-collapse-all":"onClickCollapseAll",showField:"onShow",unloadField:"onUnload",mouseover:"onHover"},$control:function(){return this.$(".acf-flexible-content:first")},$layoutsWrap:function(){return this.$(".acf-flexible-content:first > .values")},$layouts:function(){return this.$(".acf-flexible-content:first > .values > .layout")},$layout:function(t){return this.$(".acf-flexible-content:first > .values > .layout:eq("+t+")")},$clonesWrap:function(){return this.$(".acf-flexible-content:first > .clones")},$clones:function(){return this.$(".acf-flexible-content:first > .clones  > .layout")},$clone:function(t){return this.$('.acf-flexible-content:first > .clones  > .layout[data-layout="'+t+'"]')},$actions:function(){return this.$(".acf-actions:last")},$button:function(){return this.$(".acf-actions:last .button")},$popup:function(){return this.$(".tmpl-popup:last")},$moreLayoutActions:function(){return this.$(".tmpl-more-layout-actions:last")},getPopupHTML:function(){var e=this.$popup().html(),a=t(e),i=this;return a.find("[data-layout]").each(function(){var e=t(this),a=e.data("min")||0,n=e.data("max")||0,o=e.data("layout")||"",s=i.countLayouts(o);if(n&&s>=n)e.addClass("disabled");else if(a&&s<a){var l=a-s,r=acf.__("{required} {label} {identifier} required (min {min})"),c=acf._n("layout","layouts",l);r=(r=(r=(r=r.replace("{required}",l)).replace("{label}",o)).replace("{identifier}",c)).replace("{min}",a),e.append('<span class="badge" title="'+r+'">'+l+"</span>")}}),a.outerHTML()},getMoreLayoutActionsHTML:function(){return this.$moreLayoutActions().html()},getValue:function(){return this.$layouts().length},allowRemove:function(){var t=parseInt(this.get("min"));return!t||t<this.val()},allowAdd:function(){var t=parseInt(this.get("max"));return!t||t>this.val()},isFull:function(){var t=parseInt(this.get("max"));return t&&this.val()>=t},addSortable:function(t){1!=this.get("max")&&this.$layoutsWrap().sortable({items:"> .layout",handle:"> .acf-fc-layout-actions-wrap .acf-fc-layout-handle",forceHelperSize:!0,zIndex:9999,forcePlaceholderSize:!0,scroll:!0,stop:function(e,a){t.render()},update:function(e,a){t.$input().trigger("change")}})},addCollapsed:function(){var e=s.load(this.get("key"));if(!e)return!1;this.$layouts().each(function(a){e.indexOf(a)>-1&&t(this).addClass("-collapsed")})},addUnscopedEvents:function(e){this.on("invalidField",".layout",function(a){e.onInvalidField(a,t(this))}),t(document).on("click focusin",function(a){t(a.target).closest(".acf-flexible-content .layout").length||e.setActiveLayout(t([]))})},initialize:function(){this.addUnscopedEvents(this),this.addCollapsed(),acf.disable(this.$clonesWrap(),this.cid),this.render()},render:function(){this.$layouts().each(function(e){t(this).find(".acf-fc-layout-order:first").html(e+1)});const e=this;this.$control().on("click focus","> .values > .layout",function(a){const i=t(a.target).closest(".layout");e.setActiveLayout(i)}),0==this.val()?this.$control().addClass("-empty"):this.$control().removeClass("-empty"),this.isFull()?this.$button().addClass("disabled"):this.$button().removeClass("disabled")},setActiveLayout:function(e){t(".layout").removeClass("active-layout"),e.length&&e.addClass("active-layout")},onShow:function(t,e,a){var i=acf.getFields({is:":visible",parent:this.$el});acf.doAction("show_fields",i)},countLayouts:function(e){return this.$layouts().filter(function(){return t(this).data("layout")===e}).length},countLayoutsByName:function(t){const e=t.data("max");if(!e)return!0;const a=t.data("layout")||"";if(this.countLayouts(a)>=e){let a=acf.__("This field has a limit of {max} {label} {identifier}");const i=acf._n("layout","layouts",e),n='"'+t.data("label")+'"';return a=a.replace("{max}",e),a=a.replace("{label}",n),a=a.replace("{identifier}",i),this.showNotice({text:a,type:"warning"}),!1}return!0},validateAdd:function(){if(this.allowAdd())return!0;var t=this.get("max"),e=acf.__("This field has a limit of {max} {label} {identifier}"),a=acf._n("layout","layouts",t);return e=(e=(e=e.replace("{max}",t)).replace("{label}","")).replace("{identifier}",a),this.showNotice({text:e,type:"warning"}),!1},onClickAdd:function(t,e){if(!this.validateAdd())return!1;var i=null;"layout"===e.data("context")?(i=e.closest(".layout")).addClass("-hover"):"top-actions"===e.data("context")&&(i=e.closest(".acf-flexible-content").find(".values .layout").first()).addClass("-hover");var n=new a({target:e,targetConfirm:!1,text:this.getPopupHTML(),context:this,confirm:function(t,e){e.hasClass("disabled")||this.add({layout:e.data("layout"),before:i})},cancel:function(){i&&i.removeClass("-hover")}});n.on("click","[data-layout]","onConfirm")},add:function(t){if(t=acf.parseArgs(t,{layout:"",before:!1}),!this.allowAdd())return!1;var e=acf.duplicate({target:this.$clone(t.layout),append:this.proxy(function(e,a){t.before?t.before.before(a):this.$layoutsWrap().append(a),acf.enable(a,this.cid),this.render()})});return this.$input().trigger("change"),this.setActiveLayout(e),e},onClickDuplicate:function(t,e){var a=e.closest(".layout");return!!this.countLayoutsByName(a.first())&&!!this.validateAdd()&&void this.duplicateLayout(a)},duplicateLayout:function(t){if(!this.allowAdd())return!1;var e=this.get("key"),a=acf.duplicate({target:t,rename:function(t,a,i,n){return"id"===t||"for"===t?a.replace(e+"-"+i,e+"-"+n):a.replace(e+"]["+i,e+"]["+n)},before:function(t){acf.doAction("unmount",t)},after:function(t,e){acf.doAction("remount",t)}});return this.$input().trigger("change"),this.render(),acf.focusAttention(a),this.setActiveLayout(a),a},onClickToggleLayout:function(t,e){const a=e.find(".acf-fc-layout-disabled:first");"1"===e.attr("data-enabled")?(e.attr("data-enabled","0"),a.val("1")):(e.attr("data-enabled","1"),a.val("0")),this.$input().trigger("change")},onClickRenameLayout:function(t,e){const a=e.find(".acf-fc-layout-custom-label:first").val(),i={context:this,title:acf.__("Rename Layout"),textConfirm:acf.__("Rename"),textCancel:acf.__("Cancel"),currentName:a,openedBy:e.find('a[data-name="more-layout-actions"]').first(),width:"500px",confirm:function(t,a,i){this.renameLayout(e,i)},cancel:function(){e.removeClass("-hover")}};new n(i)},renameLayout:function(t,e){t.find(".acf-fc-layout-custom-label:first").val(acf.strEscape(e));const a=t.find(".acf-fc-layout-title:first");if(a.text(e),e.length)t.attr("data-renamed","1");else{let e=t.find(".acf-fc-layout-original-title:first").text().trim();e=e.substring(1,e.length-1),a.text(e),t.attr("data-renamed","0")}this.$input().trigger("change")},validateRemove:function(){if(this.allowRemove())return!0;var t=this.get("min"),e=acf.__("This field requires at least {min} {label} {identifier}"),a=acf._n("layout","layouts",t);return e=(e=(e=e.replace("{min}",t)).replace("{label}","")).replace("{identifier}",a),this.showNotice({text:e,type:"warning"}),!1},onClickRemove:function(t,e){if(t.shiftKey)return this.removeLayout(e);e.addClass("-hover");const a={confirmRemove:!0,context:this,title:acf.__("Delete Layout"),text:acf.__("Are you sure you want to delete the layout?"),textConfirm:acf.__("Delete"),textCancel:acf.__("Cancel"),openedBy:e.find('a[data-name="more-layout-actions"]').first(),width:"500px",confirm:function(t,a){this.removeLayout(e)},cancel:function(){e.removeClass("-hover")}},i=e.data("label");i.length&&(a.title=acf.__("Delete %s").replace("%s",acf.strEscape(i)),a.text=acf.__("Are you sure you want to delete %s?").replace("%s",i)),acf.newPopup(a)},removeLayout:function(t){var e=this,a=1==this.getValue()?60:0;acf.remove({target:t,endHeight:a,complete:function(){e.$input().trigger("change"),e.render()}})},onClickCollapse:function(t,e){var a=e.closest(".layout");this.isLayoutClosed(a)?this.openLayout(a):this.closeLayout(a)},onClickExpandAll:function(e,a){e.preventDefault();const i=this;this.$layouts().each(function(){i.openLayout(t(this))})},onClickCollapseAll:function(e,a){e.preventDefault();const i=this;this.$layouts().each(function(){i.closeLayout(t(this))})},onClickMoreActions:function(t,e){const a=e.closest(".layout");new i({target:e,targetConfirm:!1,text:this.getMoreLayoutActionsHTML(),context:this,confirm:function(t,e){const i=e.data("action");"remove-layout"===i&&this.onClickRemove(t,a),"toggle-layout"===i&&this.onClickToggleLayout(t,a),"rename-layout"===i&&this.onClickRenameLayout(t,a)},cancel:function(t,e){a.find('a[data-name="more-layout-actions"]').first().trigger("focus")}})},isLayoutClosed:function(t){return t.hasClass("-collapsed")},openLayout:function(t){t.removeClass("-collapsed"),acf.doAction("show",t,"collapse")},closeLayout:function(t){t.addClass("-collapsed"),acf.doAction("hide",t,"collapse"),this.renderLayout(t)},renderLayout:function(e){const a=e.children("input").attr("name").replace("[acf_fc_layout]",""),i={action:"acf/fields/flexible_content/layout_title",field_key:this.get("key"),i:e.index(),layout:e.data("layout"),value:acf.serialize(e,a)};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),dataType:"html",type:"post",success:function(t){t&&(1===e.data("renamed")?e.find(".acf-fc-layout-original-title").first().html(`(${t})`):e.find(".acf-fc-layout-title").first().html(t))}})},onUnload:function(){var e=[];this.$layouts().each(function(a){t(this).hasClass("-collapsed")&&e.push(a)}),e=e.length?e:null,s.save(this.get("key"),e)},onInvalidField:function(t,e){this.isLayoutClosed(e)&&this.openLayout(e)},onHover:function(){this.addSortable(this),this.off("mouseover")}});acf.registerFieldType(e);var a=acf.models.TooltipConfirm.extend({events:{"click [data-layout]":"onConfirm",'click [data-event="cancel"]':"onCancel"},render:function(){this.html(this.get("text")),this.$el.addClass("acf-fc-popup"),this.position()},show:function(){const e=this.get("target").closest(".acf-flexible-content");t(e).append(this.$el)},position:function(){const t=this.$el,e=this.get("target"),a=e.closest(".acf-flexible-content");o(t,e,a,8)}});const i=acf.models.TooltipConfirm.extend({events:{"click [data-action]":"onConfirm",'keydown [role="menu"]':"onKeyDown"},render:function(){const t=this.get("target").closest(".layout");this.html(this.get("text")),this.$el.addClass("acf-fc-popup acf-more-layout-actions"),"0"===t.attr("data-enabled")?this.$el.addClass("enable-layout"):this.$el.removeClass("enable-layout");const e=this;setTimeout(function(){e.$el.find("a").first().trigger("focus")},1)},show:function(){const e=this.get("target").closest(".layout");t(e).append(this.$el)},position:function(){const t=this.$el,e=this.get("target"),a=e.closest(".layout");o(t,e,a,2)},onKeyDown:function(e,a){if(-1===["ArrowDown","ArrowUp","Escape","Tab"].indexOf(e.key))return;if(e.preventDefault(),"Escape"===e.key)return void this.onCancel(e,a);const i=this.$el.find('[role="menu"]').find('[role="menuitem"]:visible'),n=t(document.activeElement),o=i.length;let s,l=i.index(n);s="ArrowDown"===e.key||"Tab"===e.key&&!e.shiftKey?(l+1)%o:(l-1+o)%o,i.eq(s).trigger("focus")}}),n=acf.models.PopupConfirm.extend({events:{'click [data-event="close"]':"onCancel","click .acf-close-popup":"onClickClose",keydown:"onPressEscapeClose",'click [data-event="confirm"]':"onConfirm","click .acf-reset-label":"onReset","submit .acf-rename-layout-form":"onConfirm"},tmpl:function(){return`\n\t\t\t<div id="acf-popup" role="dialog" aria-labelledby="acf-rename-layout-title" tabindex="-1">\n\t\t\t\t<div class="acf-popup-box acf-box acf-confirm-popup acf-rename-layout-popup">\n\t\t\t\t\t<div class="title">\n\t\t\t\t\t\t<h3 id="acf-rename-layout-title">${this.get("title")}</h3>\n\t\t\t\t\t\t<a href="#" data-event="close" aria-label="${acf.strEscape(acf.__("Close modal"))}">\n\t\t\t\t\t\t\t<i class="acf-icon -close"></i>\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</div>\n\t\t\t\t\t<form class="inner acf-rename-layout-form">\n\t\t\t\t\t\t<div class="acf-field">\n\t\t\t\t\t\t\t<div class="acf-label">\n\t\t\t\t\t\t\t\t<label for="acf-new-layout-label">${acf.strEscape(acf.__("New Label"))}</label>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="acf-input">\n\t\t\t\t\t\t\t\t<input id="acf-new-layout-label" type="text" name="acf_new_layout_label" value="${this.get("currentName")}">\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="acf-actions"> \n\t\t\t\t\t\t\t${""===this.get("currentName")?"":`<button type="button" data-event="reset-label" class="acf-btn acf-btn-secondary acf-reset-label">${acf.strEscape(acf.__("Remove Custom Label"))}</button>`}\n\t\t\t\t\t\t\t<button type="button" data-event="close" class="acf-btn acf-btn-secondary acf-close-popup">${acf.strEscape(this.get("textCancel"))}</button>\n\t\t\t\t\t\t\t<button type="submit" data-event="confirm" class="acf-btn acf-btn-primary acf-confirm">${acf.strEscape(this.get("textConfirm"))}</button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div class="bg" data-event="close"></div>\n\t\t\t</div>`},render:function(){acf.models.PopupConfirm.prototype.render.apply(this,arguments),setTimeout(()=>{const t=this.$el.find("input#acf-new-layout-label"),e=t.val().length;t.trigger("focus"),t[0].setSelectionRange(e,e)},1)},onConfirm:function(t,e){t.preventDefault(),t.stopPropagation();const a=this.$el.find("input#acf-new-layout-label").val();this.close();const i=this.get("confirm"),n=this.get("context")||this;i.apply(n,[t,e,a])},onReset:function(t,e){t.preventDefault(),t.stopPropagation(),this.$el.find("input#acf-new-layout-label").val(""),this.onConfirm(t,e)}}),o=function(e,a,i,n){if(!a.length||!i.length)return;const o=a.offset(),s=i.offset(),l=a.outerWidth(),r=a.outerHeight(),c=e.outerWidth(),d=e.outerHeight(),f=t("body").hasClass("rtl"),u=t(window).scrollTop(),h=t(window).height();let p,g,m=o.top-s.top+r+n,v=!1;o.top+r+d+n>u+h&&o.top-d-n>u&&(m=o.top-s.top-d-n,v=!0),f?(p=o.left-s.left,g=v?"bottom-left":"top-left"):(p=o.left-s.left+l-c,g=v?"bottom-right":"top-right"),e.removeClass("top-right bottom-right top-left bottom-left").css({position:"absolute",top:m,left:p}).addClass(g)};acf.registerConditionForFieldType("hasValue","flexible_content"),acf.registerConditionForFieldType("hasNoValue","flexible_content"),acf.registerConditionForFieldType("lessThan","flexible_content"),acf.registerConditionForFieldType("greaterThan","flexible_content");var s=new acf.Model({name:"this.collapsedLayouts",key:function(t,e){var a=this.get(t+e)||0;return a++,this.set(t+e,a,!0),a>1&&(t+="-"+a),t},load:function(t){t=this.key(t,"load");var e=acf.getPreference(this.name);return!(!e||!e[t])&&e[t]},save:function(e,a){e=this.key(e,"save");var i=acf.getPreference(this.name)||{};null===a?delete i[e]:i[e]=a,t.isEmptyObject(i)&&(i=null),acf.setPreference(this.name,i)}})}(jQuery)},349:()=>{var t,e;t=jQuery,e=acf.Field.extend({type:"gallery",events:{"click .acf-gallery-add":"onClickAdd","click .acf-gallery-edit":"onClickEdit","click .acf-gallery-remove":"onClickRemove","click .acf-gallery-attachment":"onClickSelect","click .acf-gallery-close":"onClickClose","change .acf-gallery-sort":"onChangeSort","click .acf-gallery-update":"onUpdate",mouseover:"onHover",showField:"render"},actions:{validation_begin:"onValidationBegin",validation_failure:"onValidationFailure",resize:"onResize"},onValidationBegin:function(){acf.disable(this.$sideData(),this.cid)},onValidationFailure:function(){acf.enable(this.$sideData(),this.cid)},$control:function(){return this.$(".acf-gallery")},$collection:function(){return this.$(".acf-gallery-attachments")},$attachments:function(){return this.$(".acf-gallery-attachment")},$attachment:function(t){return this.$('.acf-gallery-attachment[data-id="'+t+'"]')},$active:function(){return this.$(".acf-gallery-attachment.active")},$main:function(){return this.$(".acf-gallery-main")},$side:function(){return this.$(".acf-gallery-side")},$sideData:function(){return this.$(".acf-gallery-side-data")},isFull:function(){var t=parseInt(this.get("max")),e=this.$attachments().length;return t&&e>=t},getValue:function(){var e=[];return this.$attachments().each(function(){e.push(t(this).data("id"))}),!!e.length&&e},addUnscopedEvents:function(e){this.on("change",".acf-gallery-side",function(a){e.onUpdate(a,t(this))})},addSortable:function(t){this.$collection().sortable({items:".acf-gallery-attachment",forceHelperSize:!0,zIndex:9999,forcePlaceholderSize:!0,scroll:!0,start:function(t,e){e.placeholder.html(e.item.html()),e.placeholder.removeAttr("style")},update:function(e,a){t.$input().trigger("change")}}),this.$control().resizable({handles:"s",minHeight:200,stop:function(t,e){acf.update_user_setting("gallery_height",e.size.height)}})},initialize:function(){this.addUnscopedEvents(this),this.render()},render:function(){var t=this.$(".acf-gallery-sort"),e=this.$(".acf-gallery-add"),a=this.$attachments().length;this.isFull()?e.addClass("disabled"):e.removeClass("disabled"),a?t.removeClass("disabled"):t.addClass("disabled"),this.resize()},resize:function(){var t=this.$control().width(),e=Math.round(t/150);e=Math.min(e,8),this.$control().attr("data-columns",e)},onResize:function(){this.resize()},openSidebar:function(){this.$control().addClass("-open");var t=this.$control().width()/3;t=parseInt(t),t=Math.max(t,350),this.$(".acf-gallery-side-inner").css({width:t-1}),this.$side().animate({width:t-1},250),this.$main().animate({right:t},250)},closeSidebar:function(){this.$control().removeClass("-open"),this.$active().removeClass("active"),acf.disable(this.$side());var t=this.$(".acf-gallery-side-data");this.$main().animate({right:0},250),this.$side().animate({width:0},250,function(){t.html("")})},onClickAdd:function(e,a){this.isFull()?this.showNotice({text:acf.__("Maximum selection reached"),type:"warning"}):acf.newMediaPopup({mode:"select",title:acf.__("Add Image to Gallery"),field:this.get("key"),multiple:"add",library:this.get("library"),allowedTypes:this.get("mime_types"),selected:this.val(),select:t.proxy(function(t,e){this.appendAttachment(t,e)},this)})},appendAttachment:function(e,a){if(e=this.validateAttachment(e),!this.isFull()&&!this.$attachment(e.id).length){var i=['<div class="acf-gallery-attachment" data-id="'+e.id+'">','<input type="hidden" value="'+e.id+'" name="'+this.getInputName()+'[]">','<div class="margin" title="">','<div class="thumbnail">','<img src="" alt="">',"</div>",'<div class="filename"></div>',"</div>",'<div class="actions">','<a href="#" class="acf-icon -cancel dark acf-gallery-remove" data-id="'+e.id+'"></a>',"</div>","</div>"].join(""),n=t(i);if(this.$collection().append(n),"prepend"===this.get("insert")){var o=this.$attachments().eq(a);o.length&&o.before(n)}this.renderAttachment(e),this.render(),this.$input().trigger("change")}},validateAttachment:function(t){if((t=acf.parseArgs(t,{id:"",url:"",alt:"",title:"",filename:"",type:"image"})).attributes){t=t.attributes;var e=acf.isget(t,"sizes",this.get("preview_size"),"url");null!==e&&(t.url=e)}return t},renderAttachment:function(t){t=this.validateAttachment(t);var e=this.$attachment(t.id);if("image"==t.type)e.find(".filename").remove();else{var a=acf.isget(t,"image","src");null!==a&&(t.url=a),e.find(".filename").text(t.filename)}t.url||(t.url=acf.get("mimeTypeIcon"),e.addClass("-icon")),e.find("img").attr({src:t.url,alt:t.alt,title:t.title}),acf.val(e.find("input"),t.id)},editAttachment:function(e){acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:e,field:this.get("key"),select:t.proxy(function(t,e){this.renderAttachment(t)},this)})},onClickEdit:function(t,e){var a=e.data("id");a&&this.editAttachment(a)},removeAttachment:function(t){this.closeSidebar(),this.$attachment(t).remove(),this.render(),this.$input().trigger("change")},onClickRemove:function(t,e){t.preventDefault(),t.stopPropagation();var a=e.data("id");a&&this.removeAttachment(a)},selectAttachment:function(e){var a=this.$attachment(e);if(!a.hasClass("active")){var i=this.proxy(function(){this.$side().find(":focus").trigger("blur"),this.$active().removeClass("active"),a.addClass("active"),this.openSidebar(),n()}),n=this.proxy(function(){const a={action:"acf/fields/gallery/get_attachment",nonce:this.get("nonce"),field_key:this.get("key"),id:e};this.has("xhr")&&this.get("xhr").abort(),acf.showLoading(this.$sideData());var i=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"html",cache:!1,success:o});this.set("xhr",i)}),o=this.proxy(function(t){if(t){var e=this.$sideData();e.html(t),e.find(".compat-field-acf-form-data").remove(),e.find("> table.form-table > tbody").append(e.find("> .compat-attachment-fields > tbody > tr")),acf.doAction("append",e)}});i()}},onClickSelect:function(t,e){var a=e.data("id");a&&this.selectAttachment(a)},onClickClose:function(t,e){this.closeSidebar()},onChangeSort:function(e,a){if(!a.hasClass("disabled")){var i=a.val();if(i){var n=[];this.$attachments().each(function(){n.push(t(this).data("id"))});var o=this.proxy(function(){const e={action:"acf/fields/gallery/get_sort_order",nonce:this.get("nonce"),field_key:this.get("key"),ids:n,sort:i};t.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax(e),success:s})}),s=this.proxy(function(t){acf.isAjaxSuccess(t)&&(t.data.reverse(),t.data.map(function(t){this.$collection().prepend(this.$attachment(t))},this))});o()}}},onUpdate:function(e,a){var i=this.$(".acf-gallery-update");if(i.hasClass("disabled"))return;const n=acf.serialize(this.$sideData());i.addClass("disabled"),i.before('<i class="acf-loading"></i> '),n.action="acf/fields/gallery/update_attachment",n.nonce=this.get("nonce"),n.field_key=this.get("key"),t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"json",complete:function(){i.removeClass("disabled"),i.prev(".acf-loading").remove()}})},onHover:function(){this.addSortable(this),this.off("mouseover")}}),acf.registerFieldType(e),acf.registerConditionForFieldType("hasValue","gallery"),acf.registerConditionForFieldType("hasNoValue","gallery"),acf.registerConditionForFieldType("selectionLessThan","gallery"),acf.registerConditionForFieldType("selectionGreaterThan","gallery")}},e={};function a(i){var n=e[i];if(void 0!==n)return n.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,a),o.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";a(327),a(340),a(349)})()})();
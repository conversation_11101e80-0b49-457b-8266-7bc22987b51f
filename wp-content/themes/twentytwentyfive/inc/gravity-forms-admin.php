<?php
/**
 * Gravity Forms admin customizations and meta boxes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add meta box to entry detail page
 */
add_action('gform_entry_detail_sidebar_middle', 'add_admin_meta_box', 10, 2);
function add_admin_meta_box($form, $entry)
{
    $admin_notes = gform_get_meta($entry['id'], 'admin_notes');
    $processing_status = gform_get_meta($entry['id'], 'processing_status');

    ?>
    <div class="postbox">
        <h3 class="hndle" style="cursor: default;">
            <span>Admin Meta Information</span>
        </h3>
        <div class="inside">
            <form method="post">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="admin_notes">Admin Notes</label>
                        </th>
                        <td>
                            <textarea id="admin_notes" name="admin_notes" rows="4" cols="50"><?php echo esc_textarea($admin_notes); ?></textarea>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="processing_status">Processing Status</label>
                        </th>
                        <td>
                            <select id="processing_status" name="processing_status">
                                <option value="pending" <?php selected($processing_status, 'pending'); ?>>Pending</option>
                                <option value="in_progress" <?php selected($processing_status, 'in_progress'); ?>>In Progress</option>
                                <option value="completed" <?php selected($processing_status, 'completed'); ?>>Completed</option>
                                <option value="rejected" <?php selected($processing_status, 'rejected'); ?>>Rejected</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <p class="submit">
                    <input type="submit" name="save_admin_meta" class="button-primary" value="Save Admin Meta" />
                </p>
            </form>
        </div>
    </div>
    <?php
}

/**
 * Save the admin meta data
 */
add_action('gform_entry_detail_content_before', 'save_admin_meta_data', 10, 2);
function save_admin_meta_data($form, $entry)
{
    if (isset($_POST['save_admin_meta'])) {
        gform_update_meta($entry['id'], 'admin_notes', sanitize_textarea_field($_POST['admin_notes']));
        gform_update_meta($entry['id'], 'processing_status', sanitize_text_field($_POST['processing_status']));

        echo '<div class="notice notice-success"><p>Admin meta data saved successfully!</p></div>';
    }
}

/**
 * Add custom columns to the entry list
 */
add_filter('gform_entries_column_filter', 'add_admin_meta_columns', 10, 4);
function add_admin_meta_columns($columns, $form_id, $field_ids, $entry_ids)
{
    if (isset($columns['processing_status'])) {
        unset($columns['processing_status']);
    }

    $columns['processing_status'] = 'Processing Status';
    return $columns;
}

/**
 * Display custom column content
 */
add_action('gform_entries_column_filter', 'display_admin_meta_column_content', 10, 5);
function display_admin_meta_column_content($column_id, $entry, $form_id, $field_ids, $entry_ids)
{
    if ($column_id == 'processing_status') {
        $status = gform_get_meta($entry['id'], 'processing_status');
        if ($status) {
            $class = '';
            switch ($status) {
                case 'pending':
                    $class = 'status-pending';
                    break;
                case 'in_progress':
                    $class = 'status-in-progress';
                    break;
                case 'completed':
                    $class = 'status-completed';
                    break;
                case 'rejected':
                    $class = 'status-rejected';
                    break;
            }
            echo '<span class="' . $class . '">' . esc_html(ucfirst(str_replace('_', ' ', $status))) . '</span>';
        } else {
            echo '—';
        }
    }
}

/**
 * Populate Gravity Forms field with Chamber titles
 */
add_filter('gform_form_post_get_meta_1', 'add_chambers_dropdown_field');
function add_chambers_dropdown_field($form)
{
    // Get all published chambers
    $chambers = get_posts(array(
        'post_type' => 'chamber',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));

    // Build choices array
    $choices = array();
    $choices[] = array('text' => 'Valitse kauppakamari', 'value' => '');
    
    foreach ($chambers as $chamber) {
        $choices[] = array(
            'text' => $chamber->post_title,
            'value' => $chamber->post_title,
        );
    }

    // If no chambers found, add notice
    if (empty($chambers)) {
        $choices = array(
            array('text' => 'Ei kauppakamareita saatavilla', 'value' => ''),
        );
    }

    // Create chambers dropdown field
    $chambers_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 5000,
        'formId' => $form['id'],
        'label' => 'Kauppakamari',
        'choices' => $choices,
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Add the field to the form
    array_splice($form['fields'], 4, 0, array($chambers_field));

    return $form;
}

// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_chambers_dropdown_field', 10, 3);
function remove_chambers_dropdown_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 5000), 'NOT');
    }
    return $form_meta;
}

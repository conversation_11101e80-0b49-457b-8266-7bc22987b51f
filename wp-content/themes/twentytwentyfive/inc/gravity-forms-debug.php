<?php
/**
 * Gravity Forms debugging functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug Gravity Forms submission data
 */
add_action('gform_after_submission_1', 'debug_form_submission', 10, 2);
function debug_form_submission($entry, $form)
{
    error_log('=== GRAVITY FORMS DEBUG - FORM SUBMISSION ===');
    error_log('Entry ID: ' . $entry['id']);
    error_log('Form ID: ' . $form['id']);
    
    // Log all entry data
    error_log('=== ENTRY DATA ===');
    foreach ($entry as $key => $value) {
        if (is_array($value)) {
            error_log($key . ': ' . print_r($value, true));
        } else {
            error_log($key . ': ' . $value);
        }
    }
    
    // Log form fields structure
    error_log('=== FORM FIELDS STRUCTURE ===');
    foreach ($form['fields'] as $field) {
        error_log('Field ID: ' . $field->id . ' | Type: ' . $field->type . ' | Label: ' . $field->label);
        
        if ($field->type == 'checkbox' && $field->id == 20011) {
            error_log('=== CHECKBOX FIELD 20011 DETAILS ===');
            error_log('Field Object: ' . print_r($field, true));
            
            // Check specific entry values for this field
            $checkbox_value = rgar($entry, '20011');
            error_log('Checkbox main value (20011): ' . $checkbox_value);
            
            // Check for sub-inputs
            $checkbox_sub_value = rgar($entry, '20011.1');
            error_log('Checkbox sub value (20011.1): ' . $checkbox_sub_value);
            
            // Check all possible variations
            for ($i = 1; $i <= 5; $i++) {
                $sub_value = rgar($entry, '20011.' . $i);
                if (!empty($sub_value)) {
                    error_log('Checkbox sub value (20011.' . $i . '): ' . $sub_value);
                }
            }
        }
    }
    
    error_log('=== END GRAVITY FORMS DEBUG ===');
}

/**
 * Debug form rendering to see field structure
 */
add_filter('gform_pre_render_1', 'debug_form_render');
function debug_form_render($form)
{
    error_log('=== GRAVITY FORMS DEBUG - FORM RENDER ===');
    error_log('Form ID: ' . $form['id']);
    
    foreach ($form['fields'] as $field) {
        if ($field->id == 2000) {
            error_log('=== CHECKBOX FIELD 2000 RENDER DEBUG ===');
            error_log('Field Type: ' . $field->type);
            error_log('Field ID: ' . $field->id);
            error_log('Field Label: ' . $field->label);
            error_log('Field Choices: ' . print_r($field->choices, true));
            error_log('Field Inputs: ' . print_r($field->inputs, true));
            error_log('Full Field Object: ' . print_r($field, true));
        }
    }
    
    return $form;
}

/**
 * Debug form validation
 */
add_filter('gform_validation_1', 'debug_form_validation');
function debug_form_validation($validation_result)
{
    error_log('=== GRAVITY FORMS DEBUG - VALIDATION ===');
    error_log('Validation passed: ' . ($validation_result['is_valid'] ? 'YES' : 'NO'));
    
    // Log submitted values
    error_log('=== SUBMITTED VALUES ===');
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'input_') === 0) {
            if (is_array($value)) {
                error_log($key . ': ' . print_r($value, true));
            } else {
                error_log($key . ': ' . $value);
            }
        }
    }
    
    return $validation_result;
}

/**
 * Debug all form fields on render
 */
add_filter('gform_pre_render', 'debug_all_form_fields');
function debug_all_form_fields($form)
{
    if ($form['id'] == 1) {
        error_log('=== ALL FORM FIELDS DEBUG ===');
        foreach ($form['fields'] as $field) {
            error_log('Field ID: ' . $field->id . ' | Type: ' . $field->type . ' | Label: ' . $field->label);
            
            if ($field->type == 'checkbox') {
                error_log('Checkbox Field Details:');
                error_log('- Choices: ' . print_r($field->choices, true));
                error_log('- Inputs: ' . print_r($field->inputs, true));
            }
        }
    }
    
    return $form;
}

/**
 * Debug POST data on form submission
 */
add_action('gform_pre_submission_1', 'debug_post_data');
function debug_post_data($form)
{
    error_log('=== RAW POST DATA DEBUG ===');
    error_log('All POST data: ' . print_r($_POST, true));
    
    // Specifically look for checkbox inputs
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'input_20011') !== false) {
            error_log('Found checkbox input: ' . $key . ' = ' . print_r($value, true));
        }
    }
}

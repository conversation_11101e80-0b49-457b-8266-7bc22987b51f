<?php

/**
 * Gravity Forms customizations and field additions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_contact_repeater_field', 10, 3);
function remove_contact_repeater_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove the Repeater field by ID
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 2000), 'NOT');
    }
    return $form_meta;
}

/**
 * Optional: Process the repeater data after form submission
 */
add_action('gform_after_submission_1', 'process_repeater_data', 10, 2);
function process_repeater_data($entry, $form)
{

    // Get the repeater field data
    $repeater_data = rgar($entry, '2000');

    if (! empty($repeater_data)) {
        // Decode the JSON data
        // $contacts = json_decode($repeater_data, true);

        // // Loop through each contact
        // foreach ($contacts as $index => $contact) {
        //     $name = rgar($contact, '2001');
        //     $email = rgar($contact, '2002');
        //     $phone = rgar($contact, '2003');
        //     $type = rgar($contact, '2004');

        //     // Do something with the data
        //     error_log("Contact " . ($index + 1) . ": Name: $name, Email: $email, Phone: $phone, Type: $type");

        //     // Example: Save to custom table, send emails, etc.
        // }
    }
}

/**
 * Goods repeater field
 */
add_filter('gform_form_post_get_meta_1', 'add_goods_repeater_field');
function add_goods_repeater_field($form)
{
    // Create fields for goods repeater
    $goods_name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4001,
        'formId' => $form['id'],
        'label' => 'Tavaran nimi',
        'pageNumber' => 1,
    ));

    $goods_quantity = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4002,
        'formId' => $form['id'],
        'label' => 'Määrä, kpl',
        'pageNumber' => 1,
    ));

    $goods_weight = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4003,
        'formId' => $form['id'],
        'label' => 'Kokonaispaino/tilavuus',
        'pageNumber' => 1,
    ));

    $goods_value = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4004,
        'formId' => $form['id'],
        'label' => 'Tavaroiden arvo',
        'pageNumber' => 1,
    ));

    $goods_origin = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4005,
        'formId' => $form['id'],
        'label' => 'Alkuperä',
        'pageNumber' => 1,
    ));

    // Create goods list repeater
    $goods_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 4000,
        'formId' => $form['id'],
        'label' => 'Tavaraluettelo',
        'description' => 'Lisää tavaroiden tiedot',
        'addButtonText' => 'Lisää tavara',
        'removeButtonText' => 'Poista',
        'maxItems' => 6,
        'conditionalLogic' => array(
            'actionType' => 'show', // or 'hide'
            'logicType' => 'all',   // 'all' or 'any'
            'rules' => array(
                array(
                    'fieldId' => '49.1', // Your checkbox field (note the .1 for checkbox inputs)
                    'operator' => 'is',    // 'is', 'isnot', '>', '<', 'contains', etc.
                    'value' => 'manual_listing'         // The value to check against
                )
            )
        ),
        'pageNumber' => 1,
        'fields' => array($goods_name, $goods_quantity, $goods_weight, $goods_value, $goods_origin),
    ));

    array_splice($form['fields'], 31, 0, array($goods_repeater));
    return $form;
}

add_filter('gform_form_post_get_meta_1', 'add_country_repeater_field');
function add_country_repeater_field($form)
{
    //Get list of countries from acf options field "countrylist"
    $countries = get_field('countrylist', 'option');
    $countries = array_map(function ($countryArray) {
        return ['text' => $countryArray['country'], 'value' => $countryArray['country']];
    }, $countries);

    // Create a Dropdown field for Contact Type
    $country_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 2001, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Maa',
        'choices' => $countries,
        'pageNumber' => 1,
    ));

    //Checkbox field "is_transit"
    $is_transit_field = GF_Fields::create(array(
        'type' => 'checkbox',
        'id' => 20011, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Transit-maa',
        'choices' => array(
            array(
                'text' => 'Kyllä',
                'value' => 'yes',
                'isSelected' => true,
            ),
        ),
        'pageNumber' => 1,
    ));

    // Create the Repeater field and add all the sub-fields
    $repeater_field = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 2000, // Must be unique on the form
        'formId' => $form['id'],
        // 'label' => 'Maat',
        'addButtonText' => 'Lisää maa', // Custom button text
        'removeButtonText' => 'Poista maa', // Custom button text
        'maxItems' => 8, // Maximum number of repetitions
        'pageNumber' => 1,
        'fields' => array($country_field, $is_transit_field), // Add all sub-fields here
    ));

    // Add the repeater field to the form
    array_splice($form['fields'], 29, 0, array($repeater_field));

    return $form;
}

/**
 * Gravity Forms Auto-Fill Helper
 * Automatically fills out any Gravity Forms form with realistic dummy data
 */
class GravityFormsAutoFill {
  constructor() {
    this.dummyData = {
      firstNames: [
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
      ],
      lastNames: [
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "Miller",
        "Davis",
        "Rodriguez",
        "Martinez",
      ],
      emails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
      phones: ["************", "************", "************"],
      addresses: ["123 Main St", "456 Oak Ave", "789 Pine Rd", "321 Elm St"],
      cities: [
        "New York",
        "Los Angeles",
        "Chicago",
        "Houston",
        "Phoenix",
        "Philadelphia",
      ],
      states: ["NY", "CA", "TX", "FL", "IL", "PA", "OH", "GA", "NC", "MI"],
      countries: [
        "United States",
        "Canada",
        "United Kingdom",
        "Australia",
        "Germany",
      ],
      zipCodes: ["12345", "67890", "54321", "98765", "13579"],
      companies: [
        "Acme Corp",
        "Tech Solutions",
        "Global Industries",
        "Innovation Labs",
        "Future Systems",
      ],
      websites: ["https://example.com", "https://demo.org", "https://test.net"],
      textSamples: [
        "This is a sample text entry for testing purposes.",
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        "Testing form functionality with dummy data.",
        "Sample response for text area fields.",
      ],
    };
  }

  /**
   * Get random item from array
   */
  getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * Generate random number within range
   */
  getRandomNumber(min = 1, max = 100) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Generate random date
   */
  getRandomDate(pastYears = 5) {
    const start = new Date();
    start.setFullYear(start.getFullYear() - pastYears);
    const end = new Date();
    const randomDate = new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime())
    );
    return randomDate.toISOString().split("T")[0]; // YYYY-MM-DD format
  }

  /**
   * Fill text inputs (text, email, url, tel, etc.)
   */
  fillTextInput(input) {
    const type = input.type.toLowerCase();
    const name = input.name.toLowerCase();
    const id = input.id.toLowerCase();

    let value = "";

    // Determine appropriate dummy data based on field type and name
    if (type === "email" || name.includes("email") || id.includes("email")) {
      value = this.getRandomItem(this.dummyData.emails);
    } else if (
      type === "tel" ||
      name.includes("phone") ||
      id.includes("phone") ||
      name.includes("tel")
    ) {
      value = this.getRandomItem(this.dummyData.phones);
    } else if (
      type === "url" ||
      name.includes("website") ||
      id.includes("url")
    ) {
      value = this.getRandomItem(this.dummyData.websites);
    } else if (name.includes("first") && name.includes("name")) {
      value = this.getRandomItem(this.dummyData.firstNames);
    } else if (name.includes("last") && name.includes("name")) {
      value = this.getRandomItem(this.dummyData.lastNames);
    } else if (name.includes("name") && !name.includes("user")) {
      value = `${this.getRandomItem(
        this.dummyData.firstNames
      )} ${this.getRandomItem(this.dummyData.lastNames)}`;
    } else if (name.includes("address") || id.includes("address")) {
      value = this.getRandomItem(this.dummyData.addresses);
    } else if (name.includes("city") || id.includes("city")) {
      value = this.getRandomItem(this.dummyData.cities);
    } else if (name.includes("state") || id.includes("state")) {
      value = this.getRandomItem(this.dummyData.states);
    } else if (name.includes("country") || id.includes("country")) {
      value = this.getRandomItem(this.dummyData.countries);
    } else if (
      name.includes("zip") ||
      name.includes("postal") ||
      id.includes("zip")
    ) {
      value = this.getRandomItem(this.dummyData.zipCodes);
    } else if (
      name.includes("company") ||
      name.includes("organization") ||
      id.includes("company")
    ) {
      value = this.getRandomItem(this.dummyData.companies);
    } else if (
      type === "number" ||
      name.includes("age") ||
      name.includes("quantity")
    ) {
      value = this.getRandomNumber(1, 99);
    } else if (type === "date") {
      value = this.getRandomDate();
    } else {
      // Default text
      value = this.getRandomItem(this.dummyData.textSamples);
    }

    input.value = value;

    // Trigger change events
    input.dispatchEvent(new Event("input", { bubbles: true }));
    input.dispatchEvent(new Event("change", { bubbles: true }));
  }

  /**
   * Fill textarea elements
   */
  fillTextarea(textarea) {
    const sampleTexts = [
      "This is a sample textarea content for testing purposes. It contains multiple sentences to simulate real user input.",
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      "Testing form functionality with this longer text sample that spans multiple lines and provides realistic content for textarea fields.",
      "Sample feedback: The service was excellent and exceeded my expectations. I would definitely recommend it to others.",
    ];

    textarea.value = this.getRandomItem(sampleTexts);
    textarea.dispatchEvent(new Event("input", { bubbles: true }));
    textarea.dispatchEvent(new Event("change", { bubbles: true }));
  }

  /**
   * Fill select dropdowns
   */
  fillSelect(select) {
    const options = Array.from(select.options).filter(
      (option) => option.value && option.value !== ""
    );
    if (options.length > 0) {
      const randomOption = this.getRandomItem(options);
      select.value = randomOption.value;
      select.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }

  /**
   * Handle checkboxes (randomly check some)
   */
  fillCheckboxes(form) {
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach((checkbox) => {
      // Randomly check about 30% of checkboxes
      if (Math.random() < 0.3) {
        checkbox.checked = true;
        checkbox.dispatchEvent(new Event("change", { bubbles: true }));
      }
    });
  }

  /**
   * Handle radio buttons (select one per group)
   */
  fillRadioButtons(form) {
    const radioGroups = {};
    const radios = form.querySelectorAll('input[type="radio"]');

    // Group radios by name
    radios.forEach((radio) => {
      if (!radioGroups[radio.name]) {
        radioGroups[radio.name] = [];
      }
      radioGroups[radio.name].push(radio);
    });

    // Select one radio from each group
    Object.values(radioGroups).forEach((group) => {
      if (group.length > 0) {
        const randomRadio = this.getRandomItem(group);
        randomRadio.checked = true;
        randomRadio.dispatchEvent(new Event("change", { bubbles: true }));
      }
    });
  }

  /**
   * Handle file inputs (can't actually upload files, but can simulate)
   */
  handleFileInputs(form) {
    const fileInputs = form.querySelectorAll('input[type="file"]');
    fileInputs.forEach((input) => {
      console.log(
        "File input found:",
        input.name,
        "- Cannot auto-fill file inputs with dummy files"
      );
    });
  }

  /**
   * Fill Gravity Forms specific fields
   */
  fillGravityFormsSpecific(form) {
    // Handle GF name fields (first, last, etc.)
    const nameFields = form.querySelectorAll('input[id*="name"]');
    nameFields.forEach((field) => {
      if (
        field.id.includes("first") ||
        field.placeholder?.toLowerCase().includes("first")
      ) {
        field.value = this.getRandomItem(this.dummyData.firstNames);
      } else if (
        field.id.includes("last") ||
        field.placeholder?.toLowerCase().includes("last")
      ) {
        field.value = this.getRandomItem(this.dummyData.lastNames);
      }
      field.dispatchEvent(new Event("input", { bubbles: true }));
      field.dispatchEvent(new Event("change", { bubbles: true }));
    });

    // Handle GF address fields
    const addressFields = form.querySelectorAll('input[id*="address"]');
    addressFields.forEach((field) => {
      if (field.id.includes("address_line_1") || field.id.includes("street")) {
        field.value = this.getRandomItem(this.dummyData.addresses);
      } else if (field.id.includes("city")) {
        field.value = this.getRandomItem(this.dummyData.cities);
      } else if (field.id.includes("state")) {
        field.value = this.getRandomItem(this.dummyData.states);
      } else if (field.id.includes("zip")) {
        field.value = this.getRandomItem(this.dummyData.zipCodes);
      }
      field.dispatchEvent(new Event("input", { bubbles: true }));
      field.dispatchEvent(new Event("change", { bubbles: true }));
    });
  }

  /**
   * Main function to fill the entire form
   */
  fillForm(formSelector = "form") {
    const forms = document.querySelectorAll(formSelector);

    if (forms.length === 0) {
      console.log("No forms found with selector:", formSelector);
      return;
    }

    forms.forEach((form, index) => {
      console.log(`Filling form ${index + 1} of ${forms.length}`);

      // Fill text inputs
      const textInputs = form.querySelectorAll(
        'input[type="text"], input[type="email"], input[type="tel"], input[type="url"], input[type="number"], input[type="date"], input[type="time"], input[type="datetime-local"]'
      );
      textInputs.forEach((input) => this.fillTextInput(input));

      // Fill textareas
      const textareas = form.querySelectorAll("textarea");
      textareas.forEach((textarea) => this.fillTextarea(textarea));

      // Fill selects
      const selects = form.querySelectorAll("select");
      selects.forEach((select) => this.fillSelect(select));

      // Fill checkboxes and radios
      this.fillCheckboxes(form);
      this.fillRadioButtons(form);

      // Handle file inputs
      this.handleFileInputs(form);

      // Fill Gravity Forms specific fields
      this.fillGravityFormsSpecific(form);

      console.log(`Form ${index + 1} filled successfully!`);
    });
  }

  /**
   * Fill only Gravity Forms (more specific selector)
   */
  fillGravityForms() {
    this.fillForm('.gform_wrapper form, form[id*="gform"]');
  }

  /**
   * Add custom dummy data
   */
  addCustomData(dataType, values) {
    if (this.dummyData[dataType] && Array.isArray(values)) {
      this.dummyData[dataType] = [...this.dummyData[dataType], ...values];
    }
  }

  /**
   * Clear all form data
   */
  clearForm(formSelector = "form") {
    const forms = document.querySelectorAll(formSelector);
    forms.forEach((form) => {
      const inputs = form.querySelectorAll("input, textarea, select");
      inputs.forEach((input) => {
        if (input.type === "checkbox" || input.type === "radio") {
          input.checked = false;
        } else {
          input.value = "";
        }
        input.dispatchEvent(new Event("change", { bubbles: true }));
      });
    });
  }
}

// Create global instance
window.GFAutoFill = new GravityFormsAutoFill();

// Add convenient global functions
window.fillGravityForms = () => window.GFAutoFill.fillGravityForms();
window.fillAllForms = () => window.GFAutoFill.fillForm();
window.clearForms = () => window.GFAutoFill.clearForm();

// Auto-run when script loads (optional - comment out if you want manual control)
document.addEventListener("DOMContentLoaded", function () {
  console.log("Gravity Forms Auto-Fill Helper loaded!");
  console.log("Available functions:");
  console.log("- fillGravityForms() - Fill only Gravity Forms");
  console.log("- fillAllForms() - Fill all forms on page");
  console.log("- clearForms() - Clear all forms");
  console.log(
    '- GFAutoFill.addCustomData("firstNames", ["CustomName"]) - Add custom dummy data'
  );
});

// Example usage:
/*
// Fill all Gravity Forms on the page
fillGravityForms();

// Fill all forms on the page
fillAllForms();

// Clear all forms
clearForms();

// Add custom dummy data
GFAutoFill.addCustomData('firstNames', ['Custom', 'Names', 'Here']);
GFAutoFill.addCustomData('companies', ['My Company', 'Test Corp']);

// Fill specific form
GFAutoFill.fillForm('#gform_1');
*/

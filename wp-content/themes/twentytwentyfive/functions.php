<?php

/**
 * Twenty Twenty-Five functions and definitions.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_Five
 * @since Twenty Twenty-Five 1.0
 */

// Adds theme support for post formats.
if (! function_exists('twentytwentyfive_post_format_setup')) :
    /**
     * Adds theme support for post formats.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_post_format_setup()
    {
        add_theme_support('post-formats', array('aside', 'audio', 'chat', 'gallery', 'image', 'link', 'quote', 'status', 'video'));
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_post_format_setup');

// Enqueues editor-style.css in the editors.
if (! function_exists('twentytwentyfive_editor_style')) :
    /**
     * Enqueues editor-style.css in the editors.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_editor_style()
    {
        add_editor_style('assets/css/editor-style.css');
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_editor_style');

// Enqueues style.css on the front.
if (! function_exists('twentytwentyfive_enqueue_styles')) :
    /**
     * Enqueues style.css on the front.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_enqueue_styles()
    {
        wp_enqueue_style(
            'twentytwentyfive-style',
            get_parent_theme_file_uri('style.css'),
            array(),
            wp_get_theme()->get('Version')
        );
    }
endif;
add_action('wp_enqueue_scripts', 'twentytwentyfive_enqueue_styles');

// Registers custom block styles.
if (! function_exists('twentytwentyfive_block_styles')) :
    /**
     * Registers custom block styles.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_block_styles()
    {
        register_block_style(
            'core/list',
            array(
                'name'         => 'checkmark-list',
                'label'        => __('Checkmark', 'twentytwentyfive'),
                'inline_style' => '
				ul.is-style-checkmark-list {
					list-style-type: "\2713";
				}

				ul.is-style-checkmark-list li {
					padding-inline-start: 1ch;
				}',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_block_styles');

// Registers pattern categories.
if (! function_exists('twentytwentyfive_pattern_categories')) :
    /**
     * Registers pattern categories.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_pattern_categories()
    {

        register_block_pattern_category(
            'twentytwentyfive_page',
            array(
                'label'       => __('Pages', 'twentytwentyfive'),
                'description' => __('A collection of full page layouts.', 'twentytwentyfive'),
            )
        );

        register_block_pattern_category(
            'twentytwentyfive_post-format',
            array(
                'label'       => __('Post formats', 'twentytwentyfive'),
                'description' => __('A collection of post format patterns.', 'twentytwentyfive'),
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_pattern_categories');

// Registers block binding sources.
if (! function_exists('twentytwentyfive_register_block_bindings')) :
    /**
     * Registers the post format block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_register_block_bindings()
    {
        register_block_bindings_source(
            'twentytwentyfive/format',
            array(
                'label'              => _x('Post format name', 'Label for the block binding placeholder in the editor', 'twentytwentyfive'),
                'get_value_callback' => 'twentytwentyfive_format_binding',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_register_block_bindings');

// Registers block binding callback function for the post format name.
if (! function_exists('twentytwentyfive_format_binding')) :
    /**
     * Callback function for the post format name block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return string|void Post format name, or nothing if the format is 'standard'.
     */
    function twentytwentyfive_format_binding()
    {
        $post_format_slug = get_post_format();

        if ($post_format_slug && 'standard' !== $post_format_slug) {
            return get_post_format_string($post_format_slug);
        }
    }
endif;



/**
 * Gravity Forms Repeater Field Example
 * This example creates a repeater field with multiple input fields
 * Add this code to your theme's functions.php file
 */

// Replace '123' with your actual form ID
add_filter('gform_form_post_get_meta_2', 'add_contact_repeater_field');
function add_contact_repeater_field($form)
{

    // Create a Single Line Text field for Name
    $name_field = GF_Fields::create(array(
        'type' => 'text',
        'id' => 2001, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Name',
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Create an Email field
    $email_field = GF_Fields::create(array(
        'type' => 'email',
        'id' => 2002, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Email Address',
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Create a Phone field (international format only for repeaters)
    $phone_field = GF_Fields::create(array(
        'type' => 'phone',
        'id' => 2003, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Phone Number',
        'phoneFormat' => 'international', // Required for repeaters
        'pageNumber' => 1,
    ));

    // Create a Dropdown field for Contact Type
    $contact_type_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 2004, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Type',
        'choices' => array(
            array('text' => 'Primary Contact', 'value' => 'primary'),
            array('text' => 'Secondary Contact', 'value' => 'secondary'),
            array('text' => 'Emergency Contact', 'value' => 'emergency'),
        ),
        'pageNumber' => 1,
    ));

    // Create the Repeater field and add all the sub-fields
    $repeater_field = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 2000, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Information',
        'description' => 'Add multiple contacts (maximum of 5)',
        'addButtonText' => 'Add Another Contact', // Custom button text
        'removeButtonText' => 'Remove Contact', // Custom button text
        'maxItems' => 5, // Maximum number of repetitions
        'pageNumber' => 1,
        'fields' => array($name_field, $email_field, $phone_field, $contact_type_field), // Add all sub-fields here
    ));

    // Add the repeater field to the form
    $form['fields'][] = $repeater_field;

    var_dump($form['fields']);

    return $form;
}

// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_contact_repeater_field', 10, 3);
function remove_contact_repeater_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove the Repeater field by ID
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 2000), 'NOT');
    }
    return $form_meta;
}

/**
 * Optional: Process the repeater data after form submission
 */
add_action('gform_after_submission_1', 'process_repeater_data', 10, 2);
function process_repeater_data($entry, $form)
{

    // Get the repeater field data
    $repeater_data = rgar($entry, '2000');

    if (! empty($repeater_data)) {
        // Decode the JSON data
        $contacts = json_decode($repeater_data, true);

        // Loop through each contact
        foreach ($contacts as $index => $contact) {
            $name = rgar($contact, '2001');
            $email = rgar($contact, '2002');
            $phone = rgar($contact, '2003');
            $type = rgar($contact, '2004');

            // Do something with the data
            error_log("Contact " . ($index + 1) . ": Name: $name, Email: $email, Phone: $phone, Type: $type");

            // Example: Save to custom table, send emails, etc.
        }
    }
}

/**
 * Simpler Example: Just Name and Email Repeater
 */
add_filter('gform_form_post_get_meta_1', 'add_simple_repeater');
function add_simple_repeater($form)
{

    // Create Name field
    $name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 3001,
        'formId' => $form['id'],
        'label' => 'Full Name',
        'pageNumber' => 1,
    ));

    // Create Email field
    $email = GF_Fields::create(array(
        'type' => 'email',
        'id' => 3002,
        'formId' => $form['id'],
        'label' => 'Email Address',
        'pageNumber' => 1,
    ));

    // Create simple repeater
    $team_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 3000,
        'formId' => $form['id'],
        'label' => 'Team Members',
        'description' => 'Add team member details',
        'addButtonText' => 'Add Team Member',
        'removeButtonText' => 'Remove',
        'maxItems' => 10,
        'pageNumber' => 1,
        'fields' => array($name, $email),
    ));





    // Create fields for goods repeater
    $goods_name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4001,
        'formId' => $form['id'],
        'label' => 'Tavaran nimi',
        'pageNumber' => 1,
    ));

    $goods_quantity = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4002,
        'formId' => $form['id'],
        'label' => 'Määrä, kpl',
        'pageNumber' => 1,
    ));

    $goods_weight = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4003,
        'formId' => $form['id'],
        'label' => 'Kokonaispaino/tilavuus',
        'pageNumber' => 1,
    ));

    $goods_value = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4004,
        'formId' => $form['id'],
        'label' => 'Tavaroiden arvo',
        'pageNumber' => 1,
    ));

    $goods_origin = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4005,
        'formId' => $form['id'],
        'label' => 'Alkuperä',
        'pageNumber' => 1,
    ));

    // Create goods list repeater
    $goods_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 4000,
        'formId' => $form['id'],
        'label' => 'Tavaraluettelo',
        'description' => 'Lisää tavaroiden tiedot',
        'addButtonText' => 'Lisää tavara',
        'removeButtonText' => 'Poista',
        'maxItems' => 6,
        'pageNumber' => 1,
        'fields' => array($goods_name, $goods_quantity, $goods_weight, $goods_value, $goods_origin),
    ));

    // $form['fields'][] = $team_repeater;
    // array_splice($form['fields'], 1, 0, array($team_repeater));
    array_splice($form['fields'], 33, 0, array($goods_repeater));
    return $form;
}




add_filter('gform_form_update_meta_1', 'remove_simple_repeater', 10, 3);
function remove_simple_repeater($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove both repeater fields to prevent duplication
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 3000), 'NOT');
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 4000), 'NOT');
    }
    return $form_meta;
}

// Add meta box to entry detail page
add_action('gform_entry_detail_sidebar_middle', 'add_admin_meta_box', 10, 2);
function add_admin_meta_box($form, $entry)
{
    $admin_notes = gform_get_meta($entry['id'], 'admin_notes');
    $processing_status = gform_get_meta($entry['id'], 'processing_status');

?>
    <div class="postbox">
        <h3>Administrative Information</h3>
        <div class="inside">
            <table>
                <tr>
                    <td><strong>Admin Notes:</strong></td>
                    <td>
                        <textarea name="admin_notes" rows="3" cols="30"><?php echo esc_textarea($admin_notes); ?></textarea>
                    </td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <select name="processing_status">
                            <option value="pending" <?php selected($processing_status, 'pending'); ?>>Pending</option>
                            <option value="in_progress" <?php selected($processing_status, 'in_progress'); ?>>In Progress</option>
                            <option value="completed" <?php selected($processing_status, 'completed'); ?>>Completed</option>
                        </select>
                    </td>
                </tr>
            </table>
            <input type="submit" name="save_admin_meta" value="Save Admin Data" class="button button-primary" />
        </div>
    </div>
<?php
}

// Save the admin meta data
add_action('gform_entry_detail_content_before', 'save_admin_meta_data', 10, 2);
function save_admin_meta_data($form, $entry)
{
    if (isset($_POST['save_admin_meta'])) {
        gform_update_meta($entry['id'], 'admin_notes', sanitize_textarea_field($_POST['admin_notes']));
        gform_update_meta($entry['id'], 'processing_status', sanitize_text_field($_POST['processing_status']));

        echo '<div class="notice notice-success"><p>Administrative data saved!</p></div>';
    }
}


// Add custom columns to the entry list
add_filter('gform_entries_column_filter', 'add_admin_meta_columns', 10, 4);
function add_admin_meta_columns($columns, $form_id, $field_ids, $entry_ids)
{


    // var_dump($columns);

    if (isset($columns['processing_status'])) {
        $columns['processing_status'] = 'Status';
    }


    return $columns;
}

// Display values for custom columns
add_action('gform_entries_column', 'display_admin_meta_values', 10, 5);
function display_admin_meta_values($form_id, $field_id, $value, $entry, $query_string)
{



    if ($field_id == 'processing_status') {
        $status = gform_get_meta($entry['id'], 'processing_status');
        if (!empty($status)) {
            // Add some styling based on status
            $class = '';
            switch ($status) {
                case 'pending':
                    $class = 'status-pending';
                    break;
                case 'in_progress':
                    $class = 'status-in-progress';
                    break;
                case 'completed':
                    $class = 'status-completed';
                    break;
            }
            echo '<span class="' . $class . '">' . esc_html(ucfirst(str_replace('_', ' ', $status))) . '</span>';
        } else {
            echo '—';
        }
    }
}

/**
 * Register Custom Post Type: Kauppakamarit (Chambers)
 */
function register_kauppakamarit_cpt()
{
    $labels = array(
        'name'                  => 'Kauppakamarit',
        'singular_name'         => 'Kauppakamari',
        'menu_name'             => 'Kauppakamarit',
        'name_admin_bar'        => 'Kauppakamari',
        'archives'              => 'Kauppakamari Archives',
        'attributes'            => 'Kauppakamari Attributes',
        'parent_item_colon'     => 'Parent Kauppakamari:',
        'all_items'             => 'Kaikki Kauppakamarit',
        'add_new_item'          => 'Lisää Uusi Kauppakamari',
        'add_new'               => 'Lisää Uusi',
        'new_item'              => 'Uusi Kauppakamari',
        'edit_item'             => 'Muokkaa Kauppakamaria',
        'update_item'           => 'Päivitä Kauppakamari',
        'view_item'             => 'Näytä Kauppakamari',
        'view_items'            => 'Näytä Kauppakamarit',
        'search_items'          => 'Etsi Kauppakamareita',
        'not_found'             => 'Ei löytynyt',
        'not_found_in_trash'    => 'Ei löytynyt roskakorista',
        'featured_image'        => 'Kuva',
        'set_featured_image'    => 'Aseta kuva',
        'remove_featured_image' => 'Poista kuva',
        'use_featured_image'    => 'Käytä kuvana',
        'insert_into_item'      => 'Lisää kauppakamarin sisään',
        'uploaded_to_this_item' => 'Ladattu tähän kauppakamariin',
        'items_list'            => 'Kauppakamariluettelo',
        'items_list_navigation' => 'Kauppakamariluettelon navigointi',
        'filter_items_list'     => 'Suodata kauppakamariluetteloa',
    );

    $args = array(
        'label'                 => 'Kauppakamari',
        'description'           => 'Kauppakamarien tiedot',
        'labels'                => $labels,
        'supports'              => array('title', 'thumbnail', 'revisions'),
        'taxonomies'            => array(),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 20,
        'menu_icon'             => 'dashicons-building',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rest_base'             => 'kauppakamarit',
    );

    register_post_type('chamber', $args);
}
add_action('init', 'register_kauppakamarit_cpt', 0);

/**
 * Register ACF Fields for Kauppakamarit
 */
function register_kauppakamarit_acf_fields()
{
    if (function_exists('acf_add_local_field_group')) {
        acf_add_local_field_group(array(
            'key' => 'group_kauppakamarit',
            'title' => 'Kauppakamarin Tiedot',
            'fields' => array(
                array(
                    'key' => 'field_chamber_address',
                    'label' => 'Osoite',
                    'name' => 'chamber_address',
                    'type' => 'textarea',
                    'instructions' => 'Kauppakamarin osoite',
                    'required' => 1,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_chamber_phone',
                    'label' => 'Puhelinnumero',
                    'name' => 'chamber_phone',
                    'type' => 'text',
                    'instructions' => 'Kauppakamarin puhelinnumero',
                    'required' => 1,
                ),
                array(
                    'key' => 'field_chamber_contact_person',
                    'label' => 'Yhteyshenkilö',
                    'name' => 'chamber_contact_person',
                    'type' => 'text',
                    'instructions' => 'Yhteyshenkilön nimi',
                    'required' => 1,
                ),
                array(
                    'key' => 'field_chamber_contact_email',
                    'label' => 'Yhteyshenkilön sähköposti',
                    'name' => 'chamber_contact_email',
                    'type' => 'email',
                    'instructions' => 'Yhteyshenkilön sähköpostiosoite',
                    'required' => 1,
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'chamber',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => array('the_content', 'excerpt', 'discussion', 'comments', 'slug', 'author'),
        ));
    }
}
add_action('acf/init', 'register_kauppakamarit_acf_fields');

/**
 * Populate Gravity Forms field with Chamber titles
 * This function populates a field with all published chambers in alphabetical order
 */
add_filter('gform_pre_render', 'populate_chambers_field');
add_filter('gform_pre_validation', 'populate_chambers_field');
add_filter('gform_pre_submission_filter', 'populate_chambers_field');
add_filter('gform_admin_pre_render', 'populate_chambers_field');

function populate_chambers_field($form)
{
    foreach ($form['fields'] as &$field) {
        // Check if this is the chambers field (you can identify by field ID, CSS class, or parameter name)
        // Method 1: By parameter name (if you set inputName to 'chambers')
        if ($field->inputName == 'chambers') {
            populate_field_with_chambers($field);
        }

        // Method 2: By field ID (replace 'X' with your actual field ID)
        // if ($field->id == 'X') {
        //     populate_field_with_chambers($field);
        // }

        // Method 3: By CSS class (if you add a CSS class 'chambers-field' to your field)
        // if (strpos($field->cssClass, 'chambers-field') !== false) {
        //     populate_field_with_chambers($field);
        // }
    }

    return $form;
}

function populate_field_with_chambers(&$field)
{
    // Only populate dropdown, radio, or checkbox fields
    if (!in_array($field->type, array('select', 'radio', 'checkbox'))) {
        return;
    }

    // Get all published chambers
    $chambers = get_posts(array(
        'post_type' => 'chamber',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    ));

    // Clear existing choices
    $field->choices = array();

    // Add a default "Select a chamber" option for dropdowns
    if ($field->type == 'select') {
        $field->choices[] = array(
            'text' => 'Valitse kauppakamari',
            'value' => '',
        );
    }

    // Add chambers to choices
    foreach ($chambers as $chamber) {
        $field->choices[] = array(
            'text' => $chamber->post_title,
            'value' => $chamber->post_title, // or use $chamber->ID if you prefer
        );
    }

    // If no chambers found, add a notice
    if (empty($chambers)) {
        $field->choices[] = array(
            'text' => 'Ei kauppakamareita saatavilla',
            'value' => '',
        );
    }
}

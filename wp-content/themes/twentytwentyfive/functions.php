<?php

/**
 * Twenty Twenty-Five functions and definitions.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_Five
 * @since Twenty Twenty-Five 1.0
 */

// Adds theme support for post formats.
if (! function_exists('twentytwentyfive_post_format_setup')) :
    /**
     * Adds theme support for post formats.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_post_format_setup()
    {
        add_theme_support('post-formats', array('aside', 'audio', 'chat', 'gallery', 'image', 'link', 'quote', 'status', 'video'));
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_post_format_setup');

// Enqueues editor-style.css in the editors.
if (! function_exists('twentytwentyfive_editor_style')) :
    /**
     * Enqueues editor-style.css in the editors.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_editor_style()
    {
        add_editor_style('assets/css/editor-style.css');
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_editor_style');

// Enqueues style.css on the front.
if (! function_exists('twentytwentyfive_enqueue_styles')) :
    /**
     * Enqueues style.css on the front.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_enqueue_styles()
    {
        wp_enqueue_style(
            'twentytwentyfive-style',
            get_parent_theme_file_uri('style.css'),
            array(),
            wp_get_theme()->get('Version')
        );
    }
endif;
add_action('wp_enqueue_scripts', 'twentytwentyfive_enqueue_styles');

// Registers custom block styles.
if (! function_exists('twentytwentyfive_block_styles')) :
    /**
     * Registers custom block styles.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_block_styles()
    {
        register_block_style(
            'core/list',
            array(
                'name'         => 'checkmark-list',
                'label'        => __('Checkmark', 'twentytwentyfive'),
                'inline_style' => '
				ul.is-style-checkmark-list {
					list-style-type: "\2713";
				}

				ul.is-style-checkmark-list li {
					padding-inline-start: 1ch;
				}',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_block_styles');

// Registers pattern categories.
if (! function_exists('twentytwentyfive_pattern_categories')) :
    /**
     * Registers pattern categories.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_pattern_categories()
    {

        register_block_pattern_category(
            'twentytwentyfive_page',
            array(
                'label'       => __('Pages', 'twentytwentyfive'),
                'description' => __('A collection of full page layouts.', 'twentytwentyfive'),
            )
        );

        register_block_pattern_category(
            'twentytwentyfive_post-format',
            array(
                'label'       => __('Post formats', 'twentytwentyfive'),
                'description' => __('A collection of post format patterns.', 'twentytwentyfive'),
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_pattern_categories');

// Registers block binding sources.
if (! function_exists('twentytwentyfive_register_block_bindings')) :
    /**
     * Registers the post format block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_register_block_bindings()
    {
        register_block_bindings_source(
            'twentytwentyfive/format',
            array(
                'label'              => _x('Post format name', 'Label for the block binding placeholder in the editor', 'twentytwentyfive'),
                'get_value_callback' => 'twentytwentyfive_format_binding',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_register_block_bindings');

// Registers block binding callback function for the post format name.
if (! function_exists('twentytwentyfive_format_binding')) :
    /**
     * Callback function for the post format name block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return string|void Post format name, or nothing if the format is 'standard'.
     */
    function twentytwentyfive_format_binding()
    {
        $post_format_slug = get_post_format();

        if ($post_format_slug && 'standard' !== $post_format_slug) {
            return get_post_format_string($post_format_slug);
        }
    }
endif;



/**
 * Gravity Forms Repeater Field Example
 * This example creates a repeater field with multiple input fields
 * Add this code to your theme's functions.php file
 */

// Replace '123' with your actual form ID
add_filter('gform_form_post_get_meta_2', 'add_contact_repeater_field');
function add_contact_repeater_field($form)
{

    // Create a Single Line Text field for Name
    $name_field = GF_Fields::create(array(
        'type' => 'text',
        'id' => 2001, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Name',
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Create an Email field
    $email_field = GF_Fields::create(array(
        'type' => 'email',
        'id' => 2002, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Email Address',
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Create a Phone field (international format only for repeaters)
    $phone_field = GF_Fields::create(array(
        'type' => 'phone',
        'id' => 2003, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Phone Number',
        'phoneFormat' => 'international', // Required for repeaters
        'pageNumber' => 1,
    ));

    // Create a Dropdown field for Contact Type
    $contact_type_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 2004, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Type',
        'choices' => array(
            array('text' => 'Primary Contact', 'value' => 'primary'),
            array('text' => 'Secondary Contact', 'value' => 'secondary'),
            array('text' => 'Emergency Contact', 'value' => 'emergency'),
        ),
        'pageNumber' => 1,
    ));

    // Create the Repeater field and add all the sub-fields
    $repeater_field = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 2000, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Contact Information',
        'description' => 'Add multiple contacts (maximum of 5)',
        'addButtonText' => 'Add Another Contact', // Custom button text
        'removeButtonText' => 'Remove Contact', // Custom button text
        'maxItems' => 5, // Maximum number of repetitions
        'pageNumber' => 1,
        'fields' => array($name_field, $email_field, $phone_field, $contact_type_field), // Add all sub-fields here
    ));

    // Add the repeater field to the form
    $form['fields'][] = $repeater_field;

    var_dump($form['fields']);

    return $form;
}

// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_contact_repeater_field', 10, 3);
function remove_contact_repeater_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove the Repeater field by ID
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 2000), 'NOT');
    }
    return $form_meta;
}

/**
 * Optional: Process the repeater data after form submission
 */
add_action('gform_after_submission_1', 'process_repeater_data', 10, 2);
function process_repeater_data($entry, $form)
{

    // Get the repeater field data
    $repeater_data = rgar($entry, '2000');

    if (! empty($repeater_data)) {
        // Decode the JSON data
        $contacts = json_decode($repeater_data, true);

        // Loop through each contact
        foreach ($contacts as $index => $contact) {
            $name = rgar($contact, '2001');
            $email = rgar($contact, '2002');
            $phone = rgar($contact, '2003');
            $type = rgar($contact, '2004');

            // Do something with the data
            error_log("Contact " . ($index + 1) . ": Name: $name, Email: $email, Phone: $phone, Type: $type");

            // Example: Save to custom table, send emails, etc.
        }
    }
}

/**
 * Simpler Example: Just Name and Email Repeater
 */
add_filter('gform_form_post_get_meta_1', 'add_simple_repeater');
function add_simple_repeater($form)
{

    // Create Name field
    $name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 3001,
        'formId' => $form['id'],
        'label' => 'Full Name',
        'pageNumber' => 1,
    ));

    // Create Email field
    $email = GF_Fields::create(array(
        'type' => 'email',
        'id' => 3002,
        'formId' => $form['id'],
        'label' => 'Email Address',
        'pageNumber' => 1,
    ));

    // Create simple repeater
    $team_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 3000,
        'formId' => $form['id'],
        'label' => 'Team Members',
        'description' => 'Add team member details',
        'addButtonText' => 'Add Team Member',
        'removeButtonText' => 'Remove',
        'maxItems' => 10,
        'pageNumber' => 1,
        'fields' => array($name, $email),
    ));

    // Create Name field
    $name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4001,
        'formId' => $form['id'],
        'label' => 'Full Name',
        'pageNumber' => 1,
    ));

    // Create goods list repeater
    $goods_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 4000,
        'formId' => $form['id'],
        'label' => 'Tavaraluettelo',
        'description' => 'Add goods list details',
        'addButtonText' => 'Add goods',
        'removeButtonText' => 'Remove',
        'maxItems' => 10,
        'pageNumber' => 1,
        'fields' => array($name, $email),
    ));

    // $form['fields'][] = $team_repeater;
    array_splice($form['fields'], 1, 0, array($team_repeater));
    array_splice($form['fields'], 3, 0, array($goods_repeater));
    return $form;
}

add_filter('gform_form_update_meta_1', 'remove_simple_repeater', 10, 3);
function remove_simple_repeater($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 3000), 'NOT');
    }
    return $form_meta;
}

<?php

/**
 * Twenty Twenty-Five functions and definitions.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_Five
 * @since Twenty Twenty-Five 1.0
 */

// Adds theme support for post formats.
if (! function_exists('twentytwentyfive_post_format_setup')) :
    /**
     * Adds theme support for post formats.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_post_format_setup()
    {
        add_theme_support('post-formats', array('aside', 'audio', 'chat', 'gallery', 'image', 'link', 'quote', 'status', 'video'));
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_post_format_setup');

// Enqueues editor-style.css in the editors.
if (! function_exists('twentytwentyfive_editor_style')) :
    /**
     * Enqueues editor-style.css in the editors.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_editor_style()
    {
        add_editor_style('assets/css/editor-style.css');
    }
endif;
add_action('after_setup_theme', 'twentytwentyfive_editor_style');

// Enqueues style.css on the front.
if (! function_exists('twentytwentyfive_enqueue_styles')) :
    /**
     * Enqueues style.css on the front.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_enqueue_styles()
    {
        wp_enqueue_style(
            'twentytwentyfive-style',
            get_parent_theme_file_uri('style.css'),
            array(),
            wp_get_theme()->get('Version')
        );
    }
endif;
add_action('wp_enqueue_scripts', 'twentytwentyfive_enqueue_styles');

// Registers custom block styles.
if (! function_exists('twentytwentyfive_block_styles')) :
    /**
     * Registers custom block styles.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_block_styles()
    {
        register_block_style(
            'core/list',
            array(
                'name'         => 'checkmark-list',
                'label'        => __('Checkmark', 'twentytwentyfive'),
                'inline_style' => '
				ul.is-style-checkmark-list {
					list-style-type: "\2713";
				}

				ul.is-style-checkmark-list li {
					padding-inline-start: 1ch;
				}',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_block_styles');

// Registers pattern categories.
if (! function_exists('twentytwentyfive_pattern_categories')) :
    /**
     * Registers pattern categories.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_pattern_categories()
    {

        register_block_pattern_category(
            'twentytwentyfive_page',
            array(
                'label'       => __('Pages', 'twentytwentyfive'),
                'description' => __('A collection of full page layouts.', 'twentytwentyfive'),
            )
        );

        register_block_pattern_category(
            'twentytwentyfive_post-format',
            array(
                'label'       => __('Post formats', 'twentytwentyfive'),
                'description' => __('A collection of post format patterns.', 'twentytwentyfive'),
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_pattern_categories');

// Registers block binding sources.
if (! function_exists('twentytwentyfive_register_block_bindings')) :
    /**
     * Registers the post format block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return void
     */
    function twentytwentyfive_register_block_bindings()
    {
        register_block_bindings_source(
            'twentytwentyfive/format',
            array(
                'label'              => _x('Post format name', 'Label for the block binding placeholder in the editor', 'twentytwentyfive'),
                'get_value_callback' => 'twentytwentyfive_format_binding',
            )
        );
    }
endif;
add_action('init', 'twentytwentyfive_register_block_bindings');

// Registers block binding callback function for the post format name.
if (! function_exists('twentytwentyfive_format_binding')) :
    /**
     * Callback function for the post format name block binding source.
     *
     * @since Twenty Twenty-Five 1.0
     *
     * @return string|void Post format name, or nothing if the format is 'standard'.
     */
    function twentytwentyfive_format_binding()
    {
        $post_format_slug = get_post_format();

        if ($post_format_slug && 'standard' !== $post_format_slug) {
            return get_post_format_string($post_format_slug);
        }
    }
endif;



/**
 * Gravity Forms Repeater Field Example
 * This example creates a repeater field with multiple input fields
 * Add this code to your theme's functions.php file
 */



// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_contact_repeater_field', 10, 3);
function remove_contact_repeater_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove the Repeater field by ID
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 2000), 'NOT');
    }
    return $form_meta;
}

/**
 * Optional: Process the repeater data after form submission
 */
add_action('gform_after_submission_1', 'process_repeater_data', 10, 2);
function process_repeater_data($entry, $form)
{

    // Get the repeater field data
    $repeater_data = rgar($entry, '2000');

    if (! empty($repeater_data)) {
        // Decode the JSON data
        // $contacts = json_decode($repeater_data, true);

        // // Loop through each contact
        // foreach ($contacts as $index => $contact) {
        //     $name = rgar($contact, '2001');
        //     $email = rgar($contact, '2002');
        //     $phone = rgar($contact, '2003');
        //     $type = rgar($contact, '2004');

        //     // Do something with the data
        //     error_log("Contact " . ($index + 1) . ": Name: $name, Email: $email, Phone: $phone, Type: $type");

        //     // Example: Save to custom table, send emails, etc.
        // }
    }
}

/**
 * Simpler Example: Just Name and Email Repeater
 */
add_filter('gform_form_post_get_meta_1', 'add_goods_repeater_field');
function add_goods_repeater_field($form)
{
    // Create fields for goods repeater
    $goods_name = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4001,
        'formId' => $form['id'],
        'label' => 'Tavaran nimi',
        'pageNumber' => 1,
    ));

    $goods_quantity = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4002,
        'formId' => $form['id'],
        'label' => 'Määrä, kpl',
        'pageNumber' => 1,
    ));

    $goods_weight = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4003,
        'formId' => $form['id'],
        'label' => 'Kokonaispaino/tilavuus',
        'pageNumber' => 1,
    ));

    $goods_value = GF_Fields::create(array(
        'type' => 'number',
        'id' => 4004,
        'formId' => $form['id'],
        'label' => 'Tavaroiden arvo',
        'pageNumber' => 1,
    ));

    $goods_origin = GF_Fields::create(array(
        'type' => 'text',
        'id' => 4005,
        'formId' => $form['id'],
        'label' => 'Alkuperä',
        'pageNumber' => 1,
    ));

    // Create goods list repeater
    $goods_repeater = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 4000,
        'formId' => $form['id'],
        'label' => 'Tavaraluettelo',
        'description' => 'Lisää tavaroiden tiedot',
        'addButtonText' => 'Lisää tavara',
        'removeButtonText' => 'Poista',
        'maxItems' => 6,
        'conditionalLogic' => array(
            'actionType' => 'show', // or 'hide'
            'logicType' => 'all',   // 'all' or 'any'
            'rules' => array(
                array(
                    'fieldId' => '49.1', // Your checkbox field (note the .1 for checkbox inputs)
                    'operator' => 'is',    // 'is', 'isnot', '>', '<', 'contains', etc.
                    'value' => 'manual_listing'         // The value to check against
                )
            )
        ),
        'pageNumber' => 1,
        'fields' => array($goods_name, $goods_quantity, $goods_weight, $goods_value, $goods_origin),
    ));

    array_splice($form['fields'], 31, 0, array($goods_repeater));
    return $form;
}

add_filter('gform_form_post_get_meta_1', 'add_country_repeater_field');
function add_country_repeater_field($form)
{
    //Get list of countries from acf options field "countrylist"
    $countries = get_field('countrylist', 'option');
    $countries = array_map(function ($countryArray) {
        return ['text' => $countryArray['country'], 'value' => $countryArray['country']];
    }, $countries);

    // Create a Dropdown field for Contact Type
    $country_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 2001, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Maa',
        'choices' => $countries,
        'pageNumber' => 1,
    ));

    //Checkbox field "is_transit"
    $is_transit_field = GF_Fields::create(array(
        'type' => 'checkbox',
        'id' => 20011, // Must be unique on the form
        'formId' => $form['id'],
        'label' => 'Transit-maa',
        'choices' => array(
            array(
                'text' => 'Kyllä',
                'value' => 'yes',
                'isSelected' => true,
            ),
        ),
        'pageNumber' => 1,
    ));

    // Create the Repeater field and add all the sub-fields
    $repeater_field = GF_Fields::create(array(
        'type' => 'repeater',
        'id' => 2000, // Must be unique on the form
        'formId' => $form['id'],
        // 'label' => 'Maat',
        'addButtonText' => 'Lisää maa', // Custom button text
        'removeButtonText' => 'Poista maa', // Custom button text
        'maxItems' => 8, // Maximum number of repetitions
        'pageNumber' => 1,
        'fields' => array($country_field, $is_transit_field), // Add all sub-fields here
    ));

    // Add the repeater field to the form
    array_splice($form['fields'], 29, 0, array($repeater_field));

    return $form;
}


add_filter('gform_form_update_meta_1', 'remove_simple_repeater', 10, 3);
function remove_simple_repeater($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        // Remove both repeater fields to prevent duplication
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 3000), 'NOT');
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 4000), 'NOT');
    }
    return $form_meta;
}

// Add meta box to entry detail page
add_action('gform_entry_detail_sidebar_middle', 'add_admin_meta_box', 10, 2);
function add_admin_meta_box($form, $entry)
{
    $admin_notes = gform_get_meta($entry['id'], 'admin_notes');
    $processing_status = gform_get_meta($entry['id'], 'processing_status');
    $chamber_in_charge = gform_get_meta($entry['id'], 'chamber_in_charge');
?>
    <div class="postbox">
        <h3>Hallinnan työkalut</h3>
        <div class="inside">
            <table>
                <tr>
                    <td><strong>Sisäiset huomiot:</strong></td>
                    <td>
                        <textarea name="admin_notes" rows="3" cols="30"><?php echo esc_textarea($admin_notes); ?></textarea>
                    </td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <select name="processing_status">
                            <option value="pending" <?php selected($processing_status, 'pending'); ?>>Odottaa käsittelyä</option>
                            <option value="in_progress" <?php selected($processing_status, 'in_progress'); ?>>Käsittelyssä</option>
                            <option value="waiting_for_info" <?php selected($processing_status, 'waiting_for_info'); ?>>Palautettu asiakkaalle</option>
                            <option value="completed" <?php selected($processing_status, 'completed'); ?>>Käsitelty</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td><strong>Käsittelevä K2:</strong></td>
                    <td>
                        <select name="chamber_in_charge">
                            <option>Ei valittu</option>
                            <?php

                            // Get all published chambers
                            $chambers = get_posts(array(
                                'post_type' => 'chamber',
                                'post_status' => 'publish',
                                'posts_per_page' => -1,
                                'orderby' => 'title',
                                'order' => 'ASC',
                            ));

                            // Build choices array
                            $choices = array();
                            $choices[] = array('text' => 'Ei vielä valittu', 'value' => '');

                            foreach ($chambers as $chamber) {
                                $choices[] = array(
                                    'text' => $chamber->post_title,
                                    'value' => $chamber->post_title,
                                );
                            }

                            foreach ($chambers as $chamber) {
                                $selected = ($chamber->post_title == $chamber_in_charge) ? 'selected' : '';
                                echo '<option value="' . esc_attr($chamber->post_title) . '" ' . $selected . '>' . esc_html($chamber->post_title) . '</option>';
                            }

                            ?>
                        </select>
                    </td>
                </tr>
            </table>
            <p><input type="submit" name="save_admin_meta" value="Tallenna" class="button button-primary" /></p>
        </div>
    </div>
<?php
}

// Save the admin meta data
add_action('gform_entry_detail_content_before', 'save_admin_meta_data', 10, 2);
function save_admin_meta_data($form, $entry)
{
    if (isset($_POST['save_admin_meta'])) {
        gform_update_meta($entry['id'], 'admin_notes', sanitize_textarea_field($_POST['admin_notes']));
        gform_update_meta($entry['id'], 'processing_status', sanitize_text_field($_POST['processing_status']));
        gform_update_meta($entry['id'], 'chamber_in_charge', sanitize_text_field($_POST['chamber_in_charge']));

        echo '<div class="notice notice-success"><p>Administrative data saved!</p></div>';
    }
}


// Add custom columns to the entry list
add_filter('gform_entries_column_filter', 'add_admin_meta_columns', 10, 4);
function add_admin_meta_columns($columns, $form_id, $field_ids, $entry_ids)
{

    if (isset($columns['processing_status'])) {
        $columns['processing_status'] = 'Status';
    }


    return $columns;
}

// Display values for custom columns
add_action('gform_entries_column', 'display_admin_meta_values', 10, 5);
function display_admin_meta_values($form_id, $field_id, $value, $entry, $query_string)
{



    if ($field_id == 'processing_status') {
        $status = gform_get_meta($entry['id'], 'processing_status');
        if (!empty($status)) {
            // Add some styling based on status
            $class = '';
            switch ($status) {
                case 'pending':
                    $class = 'status-pending';
                    break;
                case 'in_progress':
                    $class = 'status-in-progress';
                    break;
                case 'completed':
                    $class = 'status-completed';
                    break;
            }
            echo '<span class="' . $class . '">' . esc_html(ucfirst(str_replace('_', ' ', $status))) . '</span>';
        } else {
            echo '—';
        }
    }
}

/**
 * Register Custom Post Type: Kauppakamarit (Chambers)
 */
function register_kauppakamarit_cpt()
{
    $labels = array(
        'name'                  => 'Kauppakamarit',
        'singular_name'         => 'Kauppakamari',
        'menu_name'             => 'Kauppakamarit',
        'name_admin_bar'        => 'Kauppakamari',
        'archives'              => 'Kauppakamari Archives',
        'attributes'            => 'Kauppakamari Attributes',
        'parent_item_colon'     => 'Parent Kauppakamari:',
        'all_items'             => 'Kaikki Kauppakamarit',
        'add_new_item'          => 'Lisää Uusi Kauppakamari',
        'add_new'               => 'Lisää Uusi',
        'new_item'              => 'Uusi Kauppakamari',
        'edit_item'             => 'Muokkaa Kauppakamaria',
        'update_item'           => 'Päivitä Kauppakamari',
        'view_item'             => 'Näytä Kauppakamari',
        'view_items'            => 'Näytä Kauppakamarit',
        'search_items'          => 'Etsi Kauppakamareita',
        'not_found'             => 'Ei löytynyt',
        'not_found_in_trash'    => 'Ei löytynyt roskakorista',
        'featured_image'        => 'Kuva',
        'set_featured_image'    => 'Aseta kuva',
        'remove_featured_image' => 'Poista kuva',
        'use_featured_image'    => 'Käytä kuvana',
        'insert_into_item'      => 'Lisää kauppakamarin sisään',
        'uploaded_to_this_item' => 'Ladattu tähän kauppakamariin',
        'items_list'            => 'Kauppakamariluettelo',
        'items_list_navigation' => 'Kauppakamariluettelon navigointi',
        'filter_items_list'     => 'Suodata kauppakamariluetteloa',
    );

    $args = array(
        'label'                 => 'Kauppakamari',
        'description'           => 'Kauppakamarien tiedot',
        'labels'                => $labels,
        'supports'              => array('title', 'thumbnail', 'revisions'),
        'taxonomies'            => array(),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 20,
        'menu_icon'             => 'dashicons-building',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rest_base'             => 'kauppakamarit',
    );

    register_post_type('chamber', $args);
}
add_action('init', 'register_kauppakamarit_cpt', 0);

/**
 * Register ACF Fields for Kauppakamarit
 */
function register_kauppakamarit_acf_fields()
{
    if (function_exists('acf_add_local_field_group')) {
        acf_add_local_field_group(array(
            'key' => 'group_kauppakamarit',
            'title' => 'Kauppakamarin Tiedot',
            'fields' => array(
                array(
                    'key' => 'field_chamber_address',
                    'label' => 'Osoite',
                    'name' => 'chamber_address',
                    'type' => 'textarea',
                    'instructions' => 'Kauppakamarin osoite',
                    'required' => 1,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_chamber_phone',
                    'label' => 'Puhelinnumero',
                    'name' => 'chamber_phone',
                    'type' => 'text',
                    'instructions' => 'Kauppakamarin puhelinnumero',
                    'required' => 1,
                ),
                array(
                    'key' => 'field_chamber_contact_person',
                    'label' => 'Yhteyshenkilö',
                    'name' => 'chamber_contact_person',
                    'type' => 'text',
                    'instructions' => 'Yhteyshenkilön nimi',
                    'required' => 1,
                ),
                array(
                    'key' => 'field_chamber_contact_email',
                    'label' => 'Yhteyshenkilön sähköposti',
                    'name' => 'chamber_contact_email',
                    'type' => 'email',
                    'instructions' => 'Yhteyshenkilön sähköpostiosoite',
                    'required' => 1,
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'chamber',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => array('the_content', 'excerpt', 'discussion', 'comments', 'slug', 'author'),
        ));
    }
}
add_action('acf/init', 'register_kauppakamarit_acf_fields');

/**
 * Populate Gravity Forms field with Chamber titles
 * This function populates a field with all published chambers in alphabetical order
 */
add_filter('gform_form_post_get_meta_1', 'add_chambers_dropdown_field');
function add_chambers_dropdown_field($form)
{
    // Get all published chambers
    $chambers = get_posts(array(
        'post_type' => 'chamber',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));

    // Build choices array
    $choices = array();
    $choices[] = array('text' => 'Valitse kauppakamari', 'value' => '');

    foreach ($chambers as $chamber) {
        $choices[] = array(
            'text' => $chamber->post_title,
            'value' => $chamber->post_title,
        );
    }

    // If no chambers found, add notice
    if (empty($chambers)) {
        $choices = array(
            array('text' => 'Ei kauppakamareita saatavilla', 'value' => ''),
        );
    }

    // Create chambers dropdown field
    $chambers_field = GF_Fields::create(array(
        'type' => 'select',
        'id' => 5000,
        'formId' => $form['id'],
        'label' => 'Mihin kauppakamariin hakemus lähetetään?',
        'choices' => $choices,
        'isRequired' => true,
        'pageNumber' => 1,
    ));

    // Add the field to the form
    array_splice($form['fields'], 16, 0, array($chambers_field));

    return $form;
}

// Remove the field before the form is saved (prevents duplication)
add_filter('gform_form_update_meta_1', 'remove_chambers_dropdown_field', 10, 3);
function remove_chambers_dropdown_field($form_meta, $form_id, $meta_name)
{
    if ($meta_name == 'display_meta') {
        $form_meta['fields'] = wp_list_filter($form_meta['fields'], array('id' => 5000), 'NOT');
    }
    return $form_meta;
}

/**
 * Enqueue debugger.js in footer
 */
function enqueue_debugger_script()
{
    wp_enqueue_script(
        'theme-debugger',
        get_template_directory_uri() . '/assets/debugger.js',
        array(),
        wp_get_theme()->get('Version'),
        true // Load in footer
    );
}
add_action('wp_enqueue_scripts', 'enqueue_debugger_script');

/**
 * Debug Gravity Forms submission data
 */
add_action('gform_after_submission_1', 'debug_form_submission', 10, 2);
function debug_form_submission($entry, $form)
{
    error_log('=== GRAVITY FORMS DEBUG - FORM SUBMISSION ===');
    error_log('Entry ID: ' . $entry['id']);
    error_log('Form ID: ' . $form['id']);

    // Log all entry data
    error_log('=== ENTRY DATA ===');
    foreach ($entry as $key => $value) {
        if (is_array($value)) {
            error_log($key . ': ' . print_r($value, true));
        } else {
            error_log($key . ': ' . $value);
        }
    }

    // Log form fields structure
    error_log('=== FORM FIELDS STRUCTURE ===');
    foreach ($form['fields'] as $field) {
        error_log('Field ID: ' . $field->id . ' | Type: ' . $field->type . ' | Label: ' . $field->label);

        if ($field->type == 'checkbox' && $field->id == 20011) {
            error_log('=== CHECKBOX FIELD 20011 DETAILS ===');
            error_log('Field Object: ' . print_r($field, true));

            // Check specific entry values for this field
            $checkbox_value = rgar($entry, '20011');
            error_log('Checkbox main value (20011): ' . $checkbox_value);

            // Check for sub-inputs
            $checkbox_sub_value = rgar($entry, '20011.1');
            error_log('Checkbox sub value (20011.1): ' . $checkbox_sub_value);

            // Check all possible variations
            for ($i = 1; $i <= 5; $i++) {
                $sub_value = rgar($entry, '20011.' . $i);
                if (!empty($sub_value)) {
                    error_log('Checkbox sub value (20011.' . $i . '): ' . $sub_value);
                }
            }
        }
    }

    error_log('=== END GRAVITY FORMS DEBUG ===');
}

/**
 * Debug form rendering to see field structure
 */
add_filter('gform_pre_render_1', 'debug_form_render');
function debug_form_render($form)
{
    error_log('=== GRAVITY FORMS DEBUG - FORM RENDER ===');
    error_log('Form ID: ' . $form['id']);

    foreach ($form['fields'] as $field) {
        if ($field->id == 20011) {
            error_log('=== CHECKBOX FIELD 20011 RENDER DEBUG ===');
            error_log('Field Type: ' . $field->type);
            error_log('Field ID: ' . $field->id);
            error_log('Field Label: ' . $field->label);
            error_log('Field Choices: ' . print_r($field->choices, true));
            error_log('Field Inputs: ' . print_r($field->inputs, true));
            error_log('Full Field Object: ' . print_r($field, true));
        }
    }

    return $form;
}

/**
 * Debug form validation
 */
add_filter('gform_validation_1', 'debug_form_validation');
function debug_form_validation($validation_result)
{
    error_log('=== GRAVITY FORMS DEBUG - VALIDATION ===');
    error_log('Validation passed: ' . ($validation_result['is_valid'] ? 'YES' : 'NO'));

    // Log submitted values
    error_log('=== SUBMITTED VALUES ===');
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'input_') === 0) {
            if (is_array($value)) {
                error_log($key . ': ' . print_r($value, true));
            } else {
                error_log($key . ': ' . $value);
            }
        }
    }

    return $validation_result;
}

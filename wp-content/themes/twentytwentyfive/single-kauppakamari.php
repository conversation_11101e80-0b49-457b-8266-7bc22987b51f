<?php
/**
 * Template for displaying single Kauppakamari (Chamber)
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <header class="entry-header">
                <h1 class="entry-title"><?php the_title(); ?></h1>
            </header>

            <div class="entry-content">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="chamber-image">
                        <?php the_post_thumbnail('large'); ?>
                    </div>
                <?php endif; ?>

                <div class="chamber-details">
                    <?php if (function_exists('get_field')) : ?>
                        
                        <?php $address = get_field('chamber_address'); ?>
                        <?php if ($address) : ?>
                            <div class="chamber-field">
                                <h3>Osoite</h3>
                                <p><?php echo wp_kses_post($address); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php $phone = get_field('chamber_phone'); ?>
                        <?php if ($phone) : ?>
                            <div class="chamber-field">
                                <h3>Puhelinnumero</h3>
                                <p><a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a></p>
                            </div>
                        <?php endif; ?>

                        <?php $contact_person = get_field('chamber_contact_person'); ?>
                        <?php if ($contact_person) : ?>
                            <div class="chamber-field">
                                <h3>Yhteyshenkilö</h3>
                                <p><?php echo esc_html($contact_person); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php $contact_email = get_field('chamber_contact_email'); ?>
                        <?php if ($contact_email) : ?>
                            <div class="chamber-field">
                                <h3>Yhteyshenkilön sähköposti</h3>
                                <p><a href="mailto:<?php echo esc_attr($contact_email); ?>"><?php echo esc_html($contact_email); ?></a></p>
                            </div>
                        <?php endif; ?>

                    <?php else : ?>
                        <p>ACF plugin is required to display chamber details.</p>
                    <?php endif; ?>
                </div>

                <div class="chamber-navigation">
                    <a href="<?php echo get_post_type_archive_link('kauppakamari'); ?>" class="back-to-chambers">
                        ← Takaisin kauppakamariluetteloon
                    </a>
                </div>
            </div>
        </article>
    <?php endwhile; ?>
</main>

<style>
.chamber-details {
    margin: 2rem 0;
}

.chamber-field {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #0073aa;
}

.chamber-field h3 {
    margin: 0 0 0.5rem 0;
    color: #0073aa;
    font-size: 1.1rem;
    font-weight: 600;
}

.chamber-field p {
    margin: 0;
    line-height: 1.6;
}

.chamber-field a {
    color: #0073aa;
    text-decoration: none;
}

.chamber-field a:hover {
    text-decoration: underline;
}

.chamber-image {
    margin-bottom: 2rem;
    text-align: center;
}

.chamber-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.chamber-navigation {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e0e0e0;
}

.back-to-chambers {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.back-to-chambers:hover {
    background-color: #005a87;
    color: white;
}

@media (max-width: 768px) {
    .chamber-field {
        padding: 0.75rem;
    }
    
    .chamber-field h3 {
        font-size: 1rem;
    }
}
</style>

<?php get_footer(); ?>

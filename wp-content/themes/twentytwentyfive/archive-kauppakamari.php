<?php
/**
 * Template for displaying Kauppakamari (Chamber) archive
 */

get_header(); ?>

<main id="main" class="site-main">
    <header class="page-header">
        <h1 class="page-title">Kauppakamarit</h1>
        <p class="archive-description"><PERSON><PERSON><PERSON><PERSON> yhteystiedot eri ka<PERSON>.</p>
    </header>

    <div class="chambers-grid">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('chamber-card'); ?>>
                    
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="chamber-thumbnail">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium'); ?>
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="chamber-content">
                        <h2 class="chamber-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h2>

                        <?php if (function_exists('get_field')) : ?>
                            <div class="chamber-summary">
                                
                                <?php $address = get_field('chamber_address'); ?>
                                <?php if ($address) : ?>
                                    <div class="chamber-info">
                                        <span class="info-label">📍</span>
                                        <span class="info-text"><?php echo wp_kses_post(wp_trim_words($address, 10)); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php $phone = get_field('chamber_phone'); ?>
                                <?php if ($phone) : ?>
                                    <div class="chamber-info">
                                        <span class="info-label">📞</span>
                                        <span class="info-text">
                                            <a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php $contact_person = get_field('chamber_contact_person'); ?>
                                <?php if ($contact_person) : ?>
                                    <div class="chamber-info">
                                        <span class="info-label">👤</span>
                                        <span class="info-text"><?php echo esc_html($contact_person); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php $contact_email = get_field('chamber_contact_email'); ?>
                                <?php if ($contact_email) : ?>
                                    <div class="chamber-info">
                                        <span class="info-label">✉️</span>
                                        <span class="info-text">
                                            <a href="mailto:<?php echo esc_attr($contact_email); ?>"><?php echo esc_html($contact_email); ?></a>
                                        </span>
                                    </div>
                                <?php endif; ?>

                            </div>
                        <?php endif; ?>

                        <div class="chamber-actions">
                            <a href="<?php the_permalink(); ?>" class="view-chamber-btn">
                                Näytä tiedot →
                            </a>
                        </div>
                    </div>
                </article>
            <?php endwhile; ?>

            <div class="pagination-wrapper">
                <?php
                the_posts_pagination(array(
                    'prev_text' => '← Edellinen',
                    'next_text' => 'Seuraava →',
                ));
                ?>
            </div>

        <?php else : ?>
            <div class="no-chambers">
                <h2>Ei kauppakamareita</h2>
                <p>Kauppakamareita ei ole vielä lisätty.</p>
            </div>
        <?php endif; ?>
    </div>
</main>

<style>
.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    border-bottom: 2px solid #0073aa;
}

.page-title {
    font-size: 2.5rem;
    color: #0073aa;
    margin-bottom: 0.5rem;
}

.archive-description {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
}

.chambers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.chamber-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chamber-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.chamber-thumbnail {
    height: 200px;
    overflow: hidden;
}

.chamber-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.chamber-card:hover .chamber-thumbnail img {
    transform: scale(1.05);
}

.chamber-content {
    padding: 1.5rem;
}

.chamber-title {
    margin: 0 0 1rem 0;
    font-size: 1.3rem;
}

.chamber-title a {
    color: #0073aa;
    text-decoration: none;
    font-weight: 600;
}

.chamber-title a:hover {
    color: #005a87;
}

.chamber-summary {
    margin-bottom: 1.5rem;
}

.chamber-info {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.info-label {
    margin-right: 0.5rem;
    font-size: 1rem;
    flex-shrink: 0;
}

.info-text {
    line-height: 1.4;
}

.info-text a {
    color: #0073aa;
    text-decoration: none;
}

.info-text a:hover {
    text-decoration: underline;
}

.chamber-actions {
    text-align: center;
}

.view-chamber-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.view-chamber-btn:hover {
    background-color: #005a87;
    color: white;
}

.pagination-wrapper {
    grid-column: 1 / -1;
    text-align: center;
    margin-top: 2rem;
}

.no-chambers {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    background-color: #f8f9fa;
    border-radius: 12px;
}

@media (max-width: 768px) {
    .chambers-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .chamber-content {
        padding: 1rem;
    }
}
</style>

<?php get_footer(); ?>
